<script setup lang="ts">
import { computed, onMounted, ref, watch, inject } from 'vue'
import dayjs from '@/boot/dayjs'
import { storeToRefs } from 'pinia'
import useBookingItemStore from '@/stores/booking-items'
import useWorkScheduleStore from '@/stores/work-schedules'
import useClinicStore from '@/stores/clinics'
import mtUtils from '@/utils/mtUtils'
import { typeBusinessDay } from '@/utils/enum'
import { flatten } from 'lodash'
// @ts-ignore
import MtFormCheckBox from '@/components/form/MtFormCheckBox.vue'
// @ts-ignore - Import with ignoring TypeScript errors
import BulkUpdateShiftModal from '@/pages/employeeAvailability/components/BulkUpdateShiftModal.vue'

// State management
const selectedMonth = ref(dayjs().startOf('month'))
const selectedMonthLabel = ref('')
const isLoading = ref(false)
const monthlyData = ref<any[]>([])
const clinicName = ref('')
const bulkUpdateData = ref<any[]>([])
const employees = ref<any[]>([])

// Store instances
const bookingItemStore = useBookingItemStore()
const workScheduleStore = useWorkScheduleStore()
const clinicStore = useClinicStore()

// Get the clinic ID and date range from inject (provided by parent component)
const injectedClinicId = inject<number>('clinicId')
const injectedStartDate = inject<string>('startDate')
const injectedEndDate = inject<string>('endDate')
const injectedEditMode = inject<boolean>('editMode')
const injectedDisplayMode = inject<'slot' | 'day'>('displayMode')

// Get the clinic ID and date range from props or localStorage
const props = defineProps<{
  clinicId?: number | null
  startDate?: string
  endDate?: string
  editMode?: boolean
  displayMode?: 'slot' | 'day'
  booking_item_id?: number
}>()

// Replace the editMode and displayMode computed properties with refs
const editMode = ref(false)
const displayMode = ref<'slot' | 'day'>('slot')

// Get the clinic ID from props or localStorage
const clinicId = computed(() => {
  // First check if props.clinicId is available
  if (props.clinicId) {
    return props.clinicId
  }
  
  // Then check if injected clinicId is available
  if (injectedClinicId) {
    return injectedClinicId
  }
  
  // Finally fallback to localStorage
  const storedId = localStorage.getItem('id_clinic')
  return storedId ? JSON.parse(storedId) : null
})

// Watch for clinic ID changes to refetch data
watch(clinicId, async (newClinicId) => {
  if (newClinicId) {
    await fetchClinicName()
    await fetchMonthlySchedule()
  }
})

// Set up the start and end date
const startDateValue = computed(() => {
  if (props.startDate) {
    return props.startDate
  }
  
  if (injectedStartDate) {
    return injectedStartDate
  }
  
  return selectedMonth.value.format('YYYY-MM-DD')
})

const endDateValue = computed(() => {
  if (props.endDate) {
    return props.endDate
  }
  
  if (injectedEndDate) {
    return injectedEndDate
  }
  
  return selectedMonth.value.endOf('month').format('YYYY-MM-DD')
})

// Watch for date changes
watch([startDateValue, endDateValue], ([newStart, newEnd]) => {
  if (newStart && newEnd) {
    selectedMonth.value = dayjs(newStart).startOf('month')
    selectedMonthLabel.value = selectedMonth.value.format('YYYY年MM月')
    fetchMonthlySchedule()
  }
})

// Interface definitions
interface EmployeeSchedule {
  id_employee_workschedule: number | null
  time_workschedule_start?: string
  time_workschedule_end?: string
  flg_whole_dayoff: boolean
  checked: boolean
}

interface EmployeeSchedules {
  [key: string]: {
    [key: number]: EmployeeSchedule
  }
}

interface TimeSlot {
  slot_number: number
  business_time: {
    start: string
    end: string
  }
  checkin_time?: {
    start: string
    end: string
  }
  ticket_issue_time?: {
    start: string
    end: string
  }
  ticket_limit?: number | null
}

interface DayData {
  display_date: string
  date: string
  day_of_week: number
  type_weekday: number // UI value (11-17)
  today: boolean
  business_hour_slot?: {
    id_business_hour_slot?: number
    type_business_day: number
    name_business_hour: string
    display_order?: number
    time_slots?: TimeSlot[]
  }
  employeeSchedules: EmployeeSchedules
  is_off_day: boolean
  employee_schedules?: Array<{
    id_employee: number
    name_display: string
    type_occupation: number
    flg_calendar: boolean
    id_employee_workschedule?: number
    schedules: Array<{
      time_workschedule_start: string
      time_workschedule_end: string
      flg_whole_dayoff: boolean
    }>
  }>
  is_current_month: boolean
  slot_name?: string
  slot_type?: number
  day_name?: string
  special_schedule?: any
}

// Sort doctors by display order
const sortedDoctors = computed(() =>
  employees.value.filter((emp) => emp.flg_calendar).sort((a, b) => (a.display_order || 999) - (b.display_order || 999))
)

// Q-Table columns definition
const tableColumns = computed(() => {
  const baseColumns = [
    {
      name: 'date',
      label: '',
      field: 'date',
      align: 'center' as const,
      style: 'width: 75px;',
      headerStyle: 'width: 75px; background-color: #e0e0e0;'
    },
    {
      name: 'businessHours',
      label: '営業時間帯',
      field: 'businessHours',
      align: 'left' as const,
      style: 'width: 22.11%;',
      headerStyle: 'width: 22.11%; background-color: #e0e0e0;'
    }
  ]
  
  // Add doctor columns dynamically
  const doctorCols = sortedDoctors.value.map(doctor => ({
    name: `doctor_${doctor.id_employee}`,
    label: doctor.name_display,
    field: `doctor_${doctor.id_employee}`,
    align: 'center' as const,
    style: `width: ${doctorWidth.value}%;`,
    headerStyle: `width: ${doctorWidth.value}%; background-color: #f5f5f5;`
  }))
  
  return [...baseColumns, ...doctorCols]
})

// Transform data for q-table
const tableRows = computed(() => {
  return monthlyData.value
    .filter(day => day.is_current_month)
    .map(day => {
      const row: any = {
        id: day.date,
        date: day.display_date,
        businessHours: day,
        dayData: day,
        // Additional properties for styling
        isOffDay: day.is_off_day,
        isToday: day.today,
        isSaturday: isSaturday(day),
        isSunday: isSunday(day)
      }
      
      // Add doctor data for each column
      sortedDoctors.value.forEach(doctor => {
        row[`doctor_${doctor.id_employee}`] = {
          doctor,
          day,
          scheduleData: day.employee_schedules ? 
            getHighestPrioritySchedule(day.employee_schedules, doctor.id_employee, day.date) : null
        }
      })
      
      return row
    })
})



// Calculate doctor column width
const doctorWidth = computed(() => {
  const dateColumnPercent = 7.5 // ~75px out of ~1000px typical width
  const businessHoursPercent = 22.11
  const totalRemainingWidth = 100 - dateColumnPercent - businessHoursPercent
  return sortedDoctors.value.length ? totalRemainingWidth / sortedDoctors.value.length : 0
})

// Q-Table row styling
const getRowClass = (row: any) => {
  const classes = []
  if (row.isOffDay) classes.push('off-day-row')
  if (row.isToday) classes.push('today-row')
  return classes.join(' ')
}

// Helper function to check if a day is Saturday (type_weekday = 16)
const isSaturday = (day: DayData) => {
  return day.type_weekday === 16
}

// Helper function to check if a day is Sunday (type_weekday = 17)
const isSunday = (day: DayData) => {
  return day.type_weekday === 17
}

// Change the selected month
const changeDate = async (prefix: 'next' | 'prev') => {
  if (prefix === 'next') {
    selectedMonth.value = selectedMonth.value.add(1, 'month')
  } else {
    selectedMonth.value = selectedMonth.value.subtract(1, 'month')
  }
  selectedMonthLabel.value = selectedMonth.value.format('YYYY年MM月')
  await fetchMonthlySchedule()
}

// Fetch monthly schedule data
const fetchMonthlySchedule = async () => {
  if (!clinicId.value) return
  
  isLoading.value = true

  try {
    // Use date range from parent if available, otherwise use selectedMonth
    const fetchStartDate = startDateValue.value 
      ? dayjs(startDateValue.value).format('YYYY-MM-DD')
      : selectedMonth.value.format('YYYY-MM-DD')
    
    const fetchEndDate = endDateValue.value 
      ? dayjs(endDateValue.value).format('YYYY-MM-DD')
      : selectedMonth.value.endOf('month').format('YYYY-MM-DD')

    // Fetch scheduling data for the date range
    const response = await workScheduleStore.fetchSchedulingData({
      clinic_id: clinicId.value,
      period_type: 'monthly',
      start_date: fetchStartDate,
      end_date: fetchEndDate,
      booking_item_id: props.booking_item_id
    })

    // Set employees from API response
    if (response?.employees) {
      employees.value = response.employees
    }

    buildMonthlyData()
  } catch (error) {
    console.error('Error fetching monthly schedule:', error)
  } finally {
    isLoading.value = false
  }
}

// Build calendar days based only on API response data
const buildMonthlyData = () => {
  const monthlyDays = workScheduleStore.getMonthlySchedulingData()
  if (!Array.isArray(monthlyDays) || !monthlyDays.length) {
    return
  }

  // Create array of days to display based only on API response
  const allDays: DayData[] = []

  // Process only the days returned by the API
  monthlyDays.forEach((apiDay: any) => {
    if (!apiDay.date) return // Skip if no date in API response
    
    const dayObj = dayjs(apiDay.date)
    if (!dayObj.isValid()) return // Skip if invalid date
    
    // Use data directly from API response
    const businessHourSlot = apiDay.business_hour_slot || null
    const slotName = apiDay.slot_name || (businessHourSlot ? businessHourSlot.name_business_hour : null)
    const slotType = apiDay.slot_type || (businessHourSlot ? businessHourSlot.type_business_day : null)

    // Map employee availability data
    const employeeSchedules: EmployeeSchedules = {}
    if (apiDay.employee_schedules) {
      apiDay.employee_schedules.forEach((employeeSchedule: any) => {
        if (!employeeSchedules[employeeSchedule.id_employee]) {
          employeeSchedules[employeeSchedule.id_employee] = {}
        }

        // Map each schedule to a slot number
        employeeSchedule.schedules.forEach((schedule: any) => {
          // Figure out which time slot this schedule corresponds to
          const slotNumber = getSlotNumberForTime(
            schedule.time_workschedule_start,
            businessHourSlot?.time_slots
          )

          if (slotNumber) {
            employeeSchedules[employeeSchedule.id_employee][slotNumber] = {
              checked: schedule.flg_whole_dayoff,
              id_employee_workschedule: schedule.id_employee_workschedule || null,
              time_workschedule_start: schedule.time_workschedule_start,
              time_workschedule_end: schedule.time_workschedule_end,
              flg_whole_dayoff: schedule.flg_whole_dayoff
            }
          }
        })
      })
    }

    // Create slots for all doctors and all potential time slots
    sortedDoctors.value.forEach((doctor) => {
      if (!employeeSchedules[doctor.id_employee]) {
        employeeSchedules[doctor.id_employee] = {}
      }

      // Ensure all slots exist
      if (businessHourSlot?.time_slots) {
        businessHourSlot.time_slots.forEach((slot: any, index: number) => {
          const slotNumber = index + 1
          if (!employeeSchedules[doctor.id_employee][slotNumber]) {
            employeeSchedules[doctor.id_employee][slotNumber] = {
              checked: false,
              id_employee_workschedule: null,
              flg_whole_dayoff: false
            }
          }
        })
      }
    })

    // Create day data object using API response data
    const displayDate = dayjs(apiDay.date).format('MM/DD (dd)')
    
    allDays.push({
      display_date: displayDate,
      date: apiDay.date,
      day_of_week: dayObj.day(), // 0-6 for Sunday-Saturday
      type_weekday: apiDay.type_weekday || (dayObj.day() === 0 ? 17 : dayObj.day() + 10), // 11-17 for UI display
      today: dayjs().isSame(dayObj, 'day'),
      business_hour_slot: businessHourSlot,
      employeeSchedules,
      is_off_day: businessHourSlot?.type_business_day === 90,
      employee_schedules: apiDay.employee_schedules || [],
      is_current_month: true, // All API response days are considered current
      slot_name: slotName,
      slot_type: slotType,
      day_name: apiDay.day_name || ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'][dayObj.day() === 0 ? 6 : dayObj.day() - 1],
      special_schedule: apiDay.special_schedule
    } as DayData)
  })

  monthlyData.value = allDays
}


// Helper function to match a time to a slot number
const getSlotNumberForTime = (time: string, timeSlots?: TimeSlot[]): number | null => {
  if (!timeSlots) return null

  for (let i = 0; i < timeSlots.length; i++) {
    if (timeSlots[i].business_time.start === time) {
      return i + 1
    }
  }
  return null
}

// Helper function to get business day type name
const typeBusinessDayName = (value: number) => typeBusinessDay.find((v) => v.value === value)

// Get total number of time slots for a day
const getTotalSlots = (timeSlots?: TimeSlot[]) => {
  return timeSlots?.length || 0
}

// Check if all slots for an employee on a specific day are marked as off
const isAllSlotChecked = (data: DayData, idEmployee: number | string) => {
  const employeeSchedules = data.employeeSchedules[idEmployee]
  if (!employeeSchedules) return false

  const values = Object.values(employeeSchedules)
  return values.length > 0 && values.every((item) => item.checked === true)
}

// Set all slots for an employee on a specific day to checked or unchecked
const setAllSlotChecked = (checked: boolean, data: DayData, idEmployee: number | string) => {
  const employeeSchedules = data.employeeSchedules[idEmployee]
  if (!employeeSchedules) return

  Object.values(employeeSchedules).forEach((item) => {
    item.checked = checked
  })
}

// Update a single slot's checked state
const updateSlotChecked = (day: DayData, employeeId: number | string, slotNumber: number, value: boolean) => {
  if (day.employeeSchedules[employeeId]?.[slotNumber]) {
    day.employeeSchedules[employeeId][slotNumber].checked = value
  }
}

// Update all slots for an employee on a specific day
const updateAllSlotChecked = (day: DayData, employeeId: number | string, value: boolean) => {
  setAllSlotChecked(value, day, employeeId)
}

// Direct bulk update for day mode
const directBulkUpdate = async () => {
  try {
    isLoading.value = true

    // Collect selected employees and days with checked schedules
    const selectedEmployees = new Set<number>()
    const selectedData: any[] = []
    
    monthlyData.value.forEach((day) => {
      if (day.is_off_day) return // Skip off days

      Object.entries(day.employeeSchedules).forEach(([employeeId, slots]) => {
        const hasCheckedSlots = Object.values(slots as Record<string, EmployeeSchedule>)
          .some(schedule => schedule.checked === true)
        
        if (hasCheckedSlots) {
          selectedEmployees.add(parseInt(employeeId as string))
          selectedData.push({
            id_employee: parseInt(employeeId as string),
            type_weekday: 99,
            date_booking_special: day.date // Add date for monthly view
          })
        }
      })
    })

    if (selectedData.length === 0) {
      mtUtils.alert('更新する予定がありません')
      return
    }

    // Remove duplicates based on employee and date
    const uniqueData = selectedData.filter((item, index, self) => 
      index === self.findIndex(t => 
        t.id_employee === item.id_employee && 
        t.date_booking_special === item.date_booking_special
      )
    )

    // Prepare employee work schedules for API
    const employeeList = Array.from(selectedEmployees).map((employeeId) => {
      // Filter uniqueData for current employee
      const employeeData = uniqueData.filter((item: any) => item.id_employee === employeeId)
      
      const employeeWorkscheduleList = employeeData.map((data: any) => ({
        type_weekday: 99,
        time_workschedule_start: '00:00:00',
        time_workschedule_end: '00:00:00',
        flg_whole_dayoff: true, // Always true for day mode
        date_booking_special: data.date_booking_special,
        min_rest: 0,
        id_booking_item: props.booking_item_id
      }))

      return {
        id_employee: employeeId,
        employee_workschedule_list: employeeWorkscheduleList
      }
    })

    // Call the API
    if (clinicId.value === null) {
      throw new Error('Clinic ID is required');
    }
    
    await workScheduleStore.createOrUpdateWorkSchedules({
      id_clinic: clinicId.value,
      employee_list: employeeList
    })

    // Show success message
    mtUtils.autoCloseAlert('休みを適用しました！')
    
    // Refresh data and exit edit mode
    await fetchMonthlySchedule()
    editMode.value = false
  } catch (error) {
    console.error('Failed to update schedules:', error)
    mtUtils.autoCloseAlert('スケジュールの更新に失敗しました。')
  } finally {
    isLoading.value = false
  }
}

// Open the bulk update modal
const openBulkUpdateModal = async () => {
  if (!bulkUpdateData.value.length) {
    mtUtils.alert('更新する予定がありません')
    return
  }

  await mtUtils.smallPopup(BulkUpdateShiftModal, {
    bulkUpdateData: bulkUpdateData.value,
    clinicId: clinicId.value,
    displayMode: displayMode.value,
    booking_item_id: props.booking_item_id,
    onSuccess: async () => {
      // Refresh data
      await fetchMonthlySchedule()
      editMode.value = false
    }
  })
}

// Helper function to prepare the bulk update data
const prepareBulkUpdateData = async (): Promise<any[]> => {
  // Prepare payload for scheduling API - collect unique employee-date combinations with changes
  const payload: any[] = []
  const processedEmployeeDates = new Set<string>()

  monthlyData.value.forEach((day) => {
    Object.entries(day.employeeSchedules).forEach(([employeeId, slots]) => {
      const hasCheckedSlots = Object.values(slots as Record<string, EmployeeSchedule>)
        .some(schedule => schedule.checked === true)
      
      const hasExistingSchedules = Object.values(slots as Record<string, EmployeeSchedule>)
        .some(schedule => schedule.id_employee_workschedule)

      if (hasCheckedSlots || hasExistingSchedules) {
        const employeeDateKey = `${employeeId}-${day.date}`
        
        // Only process each employee-date combination once
        if (!processedEmployeeDates.has(employeeDateKey)) {
          processedEmployeeDates.add(employeeDateKey)
          
          // Find the first time slot for this day (or use default times)
          const firstTimeSlot = day.business_hour_slot?.time_slots?.[0]
          
          // Get the first schedule with data (either checked or existing ID)
          const scheduleWithData = Object.values(slots as Record<string, EmployeeSchedule>)
            .find(schedule => schedule.checked || schedule.id_employee_workschedule)

          payload.push({
            id_employee_workschedule: scheduleWithData?.id_employee_workschedule || null,
            id_employee: parseInt(employeeId as string),
            type_weekday: 99, // Use 99 for special schedules with specific dates
            time_workschedule_start: firstTimeSlot?.business_time.start || '00:00:00',
            time_workschedule_end: firstTimeSlot?.business_time.end || '00:00:00',
            time_workschedule_start2: '00:00:00',
            time_workschedule_end2: '00:00:00',
            time_workschedule_start3: '00:00:00',
            time_workschedule_end3: '00:00:00',
            flg_whole_dayoff: scheduleWithData?.checked || false,
            id_clinic: clinicId.value,
            id_booking_item: props.booking_item_id,
            date_booking_special: day.date // Add date for monthly view
          })
        }
      }
    })
  })

  return payload;
}

// Fetch clinic name
const fetchClinicName = async () => {
  if (clinicId.value) {
    try {
      const clinic = await clinicStore.fetchClinicById(clinicId.value)
      if (clinic) {
        clinicName.value = clinic.name_clinic_display
      }
    } catch (error) {
      console.error('Error fetching clinic name:', error)
      clinicName.value = ''
    }
  } else {
    clinicName.value = ''
  }
}

// Format time string to display format (removing :00 seconds)
const formatTimeDisplay = (time: string) => {
  return time ? time.replace(/:00$/, '') : ''
}


// Add click handler for schedule items
const handleScheduleClick = async (schedule: EmployeeScheduleItem | null, employeeId: number, day: DayData) => {
  if (!schedule) return;
  
  // Open the bulk update modal with the schedule data
  await mtUtils.smallPopup(BulkUpdateShiftModal, {
    bulkUpdateData: [{
      id_employee_workschedule: schedule.id_employee_workschedule,
      id_employee: employeeId,
      type_weekday: day.type_weekday,
      time_workschedule_start: schedule.time_workschedule_start,
      time_workschedule_end: schedule.time_workschedule_end,
      flg_whole_dayoff: schedule.flg_whole_dayoff,
      date_booking_special: day.date // Add date for monthly view
    }],
    clinicId: clinicId.value,
    booking_item_id: props.booking_item_id,
    workScheduleId: schedule.id_employee_workschedule || undefined,
    onSuccess: async () => {
      // Refresh data
      await fetchMonthlySchedule()
    }
  })
}

// Check if all slots for an employee across all days in the current month are marked as off
const isAllDaysSlotChecked = (employeeId: number | string) => {
  const currentMonthDays = monthlyData.value.filter(day => day.is_current_month && !day.is_off_day)
  if (!currentMonthDays.length) return false
  
  return currentMonthDays.every(day => {
    const employeeSchedules = day.employeeSchedules[employeeId]
    if (!employeeSchedules) return false
    
    const values = Object.values(employeeSchedules) as EmployeeSchedule[]
    return values.length > 0 && values.every(item => item.checked === true)
  })
}

// Update all slots for an employee across all days in the current month
const updateAllDaysSlotChecked = (employeeId: number | string, value: boolean) => {
  const currentMonthDays = monthlyData.value.filter(day => day.is_current_month && !day.is_off_day)
  currentMonthDays.forEach(day => {
    updateAllSlotChecked(day, employeeId, value)
  })
}

// Interface for schedule
interface EmployeeScheduleItem {
  id_employee_workschedule?: number;
  time_workschedule_start: string;
  time_workschedule_end: string;
  time_workschedule_start2?: string;
  time_workschedule_end2?: string;
  time_workschedule_start3?: string;
  time_workschedule_end3?: string;
  flg_whole_dayoff: boolean;
  type_weekday?: number;
}

// Add a new helper function to get the highest priority schedule for a specific date
const getHighestPrioritySchedule = (employeeSchedules: any[], employeeId: number, currentDate?: string): EmployeeScheduleItem | null => {
  if (!employeeSchedules || !employeeSchedules.length) return null;
  
  // Filter schedules for the given employee
  const empSchedules = employeeSchedules.filter(emp => emp.id_employee === employeeId);
  if (!empSchedules.length || !empSchedules[0].schedules || !empSchedules[0].schedules.length) return null;
  
  const schedules = empSchedules[0].schedules;
  
  // If we have a current date, check for special schedules that match this specific date
  if (currentDate) {
    const dateSpecificSchedules = schedules.filter((s: any) => {
      return s.type_weekday === 99 && s.date_booking_special === currentDate;
    });
    
    if (dateSpecificSchedules.length) {
      return {
        ...dateSpecificSchedules[0],
        id_employee_workschedule: empSchedules[0].id_employee_workschedule
      };
    }
    
    // If there are special schedules for other dates, don't show regular schedules
    const hasOtherSpecialSchedules = schedules.some((s: any) => {
      return s.type_weekday === 99 && s.date_booking_special && s.date_booking_special !== currentDate;
    });
    
    if (hasOtherSpecialSchedules) {
      return null; // Don't show regular schedule if there are special schedules for other dates
    }
  }
  
  // Second priority: Regular schedules (type_weekday=11-18)
  const regularSchedules = schedules.filter((s: EmployeeScheduleItem) => s.type_weekday && s.type_weekday >= 11 && s.type_weekday <= 18);
  if (regularSchedules.length) {
    return {
      ...regularSchedules[0],
      id_employee_workschedule: empSchedules[0].id_employee_workschedule
    };
  }
  
  // If no matching schedules, return the first one only if it's not a date-specific special schedule
  const firstSchedule = schedules[0];
  if (firstSchedule.type_weekday !== 99 || !firstSchedule.date_booking_special || firstSchedule.date_booking_special === currentDate) {
    return {
      ...firstSchedule,
      id_employee_workschedule: empSchedules[0].id_employee_workschedule
    };
  }
  
  return null;
};

// Function to display multiple time slots
const formatMultipleTimeSlots = (schedule: EmployeeScheduleItem | null): string => {
  if (!schedule) return '';
  
  // If it's a day off, return the "休" character
  if (schedule.flg_whole_dayoff) {
    return '<span class="text-darkred">休</span>';
  }
  
  let result = '';
  
  // Format primary time slot
  if (schedule.time_workschedule_start && schedule.time_workschedule_end) {
    result += `${formatTimeDisplay(schedule.time_workschedule_start)}~${formatTimeDisplay(schedule.time_workschedule_end)}`;
  }
  
  // // Add secondary time slot if exists
  if (schedule.time_workschedule_start2 && schedule.time_workschedule_end2) {
    result += `<br>${formatTimeDisplay(schedule.time_workschedule_start2)}~${formatTimeDisplay(schedule.time_workschedule_end2)}`;
  }
  
  // Add tertiary time slot if exists
  if (schedule.time_workschedule_start3 && schedule.time_workschedule_end3) {
    result += `<br>${formatTimeDisplay(schedule.time_workschedule_start3)}~${formatTimeDisplay(schedule.time_workschedule_end3)}`;
  }
  
  return result;
}

// Initialize on component mount
onMounted(async () => {
  selectedMonthLabel.value = selectedMonth.value.format('YYYY年MM月')
  
  // Only fetch data if clinicId is available
  if (clinicId.value) {
    await mtUtils.promiseAllWithLoader([fetchClinicName(), fetchMonthlySchedule()])
  }
})

// Toggle edit mode functions
const toggleEditMode = (mode?: 'day' | 'slot') => {
  editMode.value = !editMode.value
  if (mode) {
    displayMode.value = mode
  }
}

// Cancel edit mode
const cancelEdit = () => {
  editMode.value = false
  // Reset data by refetching
  fetchMonthlySchedule()
}

// Update the bulkUpdate function
const bulkUpdate = async () => {
  // If in day mode, directly update without showing modal
  if (displayMode.value === 'day') {
    await directBulkUpdate()
    return
  }

  const payload = await prepareBulkUpdateData();
  // Store payload in the ref for use by the modal
  bulkUpdateData.value = payload

  // Show modal
  openBulkUpdateModal()
}

// Expose methods for parent component
defineExpose({
  toggleEditMode,
  cancelEdit,
  bulkUpdate,
  fetchMonthlySchedule,
  prepareBulkUpdateData
})
</script>

<template>
  <q-layout container :style="{ height: 'calc(100vh - 70px)' }">
    <q-page-container>
      <q-page>
        <div class="q-pl-xl q-pr-sm">
          <div class="row items-center justify-between q-my-md">
            <div class="row items-center">
              <div v-if="!editMode" class="flex items-center">
                <q-btn
                  label="時間枠毎設定"
                  @click="toggleEditMode('slot')"
                  padding="4px 20px"
                  flat
                  unelevated
                  class="bg-grey-100 q-mr-md"
                  style="border: 1px solid #9e9e9e"
                />
                <q-btn
                  label="終日休みの一括設定"
                  @click="toggleEditMode('day')"
                  padding="4px 20px"
                  flat
                  unelevated
                  class="bg-grey-100"
                  style="border: 1px solid #9e9e9e"
                />
              </div>
              <div v-else class="flex items-center">
                <div class="caption2">
                  {{
                    displayMode === 'day'
                      ? '終日休みを設定する日にチェックを入れてください'
                      : '休みを設定する時間枠にチェックを入れてください'
                  }}
                </div>
                <q-btn unelevated color="primary" class="q-ml-md" type="button" @click="bulkUpdate">
                  <span>決定</span>
                </q-btn>
                <q-btn outline class="bg-grey-100 text-grey-800 q-ml-sm" @click="cancelEdit">
                  <span>キャンセル</span>
                </q-btn>
              </div>
            </div>
          </div>

          <!-- Monthly table view using q-table -->
          <div class="calendar-view" v-if="!isLoading && monthlyData.length">
            <q-table
              :columns="tableColumns"
              :rows="tableRows"
              row-key="id"
              flat
              bordered
              separator="cell"
              :rows-per-page-options="[0]"
              hide-bottom
              :row-class="getRowClass"
              class="monthly-schedule-qtable"
            >
              <!-- Custom header slot -->
              <template v-slot:header="props">
                <q-tr :props="props">
                  <q-th
                    v-for="col in props.cols"
                    :key="col.name"
                    :props="props"
                    :style="col.headerStyle"
                    class="text-center"
                  >
                    <template v-if="col.name.startsWith('doctor_')">
                      <div class="doctor-header-cell">
                        <MtFormCheckBox
                          v-if="editMode"
                          type="checkbox"
                          label=""
                          :checked="isAllDaysSlotChecked(col.name.replace('doctor_', ''))"
                          class="caption1 q-mt-xs"
                          style="padding: 0; border: none"
                          @update:checked="(newVal) => updateAllDaysSlotChecked(col.name.replace('doctor_', ''), newVal)"
                        />
                        <div class="doctor-name">{{ col.label }}</div>
                      </div>
                    </template>
                    <template v-else>
                      {{ col.label }}
                    </template>
                  </q-th>
                </q-tr>
              </template>

              <!-- Custom body slot for complete control -->
              <template v-slot:body="props">
                <q-tr :props="props" :class="getRowClass(props.row)">
                  <!-- Date column -->
                  <q-td key="date" :props="props" class="date-cell text-center">
                    <div 
                      :class="{ 
                        'text-blue': props.row.isSaturday,
                        'text-red': props.row.isSunday,
                        'bg-yellow-100': props.row.isToday
                      }"
                    >
                      {{ props.row.date }}
                    </div>
                  </q-td>

                  <!-- Business hours column -->
                  <q-td key="businessHours" :props="props" class="business-hours-cell">
                    <div 
                      :class="{ 
                        'bg-grey-300': props.row.isOffDay,
                        'bg-yellow-100': props.row.isToday 
                      }"
                      class="q-pa-sm"
                    >
                      <div class="text-body1 q-mb-sm">
                        {{ props.row.dayData.slot_name || typeBusinessDayName(props.row.dayData.business_hour_slot?.type_business_day || 0)?.label }} /
                        {{ props.row.dayData.business_hour_slot?.name_business_hour }}
                      </div>
                      <template v-if="props.row.dayData.business_hour_slot?.time_slots">
                        <div class="flex flex-wrap gap-2 text-caption">
                          <div 
                            v-for="(timeSlot, slotIdx) in props.row.dayData.business_hour_slot.time_slots" 
                            :key="slotIdx"
                            class="flex items-center gap-2"
                          >
                            枠{{ slotIdx + 1 }}
                            {{ formatTimeDisplay(timeSlot.business_time.start) }} ~
                            {{ formatTimeDisplay(timeSlot.business_time.end) }}
                          </div>
                        </div>
                      </template>
                    </div>
                  </q-td>

                  <!-- Doctor schedule columns -->
                  <q-td 
                    v-for="doctor in sortedDoctors"
                    :key="`doctor_${doctor.id_employee}`" 
                    :props="props"
                    class="doctor-schedule-cell text-center"
                  >
                    <div 
                      class="schedule-content q-pa-sm"
                      :class="{ 
                        'bg-grey-300': props.row.isOffDay,
                        'bg-yellow-100': props.row.isToday 
                      }"
                    >
                      <template v-if="!props.row.isOffDay">
                        <template v-if="!editMode">
                          <!-- Show only the highest priority schedule for this employee -->
                          <template v-if="props.row.dayData.employee_schedules">
                            <div 
                              v-if="!getHighestPrioritySchedule(props.row.dayData.employee_schedules, doctor.id_employee, props.row.dayData.date)" 
                              class="text-grey-700 schedule-item"
                            >
                              <!-- 予定なし -->
                            </div>
                            <div 
                              v-else
                              :class="{ 'text-red': getHighestPrioritySchedule(props.row.dayData.employee_schedules, doctor.id_employee, props.row.dayData.date)?.flg_whole_dayoff }"
                              class="schedule-item cursor-pointer"
                              @click="handleScheduleClick(getHighestPrioritySchedule(props.row.dayData.employee_schedules, doctor.id_employee, props.row.dayData.date), doctor.id_employee, props.row.dayData)"
                            >
                              <template v-if="getHighestPrioritySchedule(props.row.dayData.employee_schedules, doctor.id_employee, props.row.dayData.date)?.flg_whole_dayoff">
                                休
                              </template>
                              <template v-else>
                                <div v-html="formatMultipleTimeSlots(getHighestPrioritySchedule(props.row.dayData.employee_schedules, doctor.id_employee, props.row.dayData.date))"></div>
                              </template>
                            </div>
                          </template>
                        </template>
                        <template v-else>
                          <div 
                            class="flex flex-col justify-center items-center cursor-pointer"
                            @click="updateAllSlotChecked(props.row.dayData, doctor.id_employee, !isAllSlotChecked(props.row.dayData, doctor.id_employee))"
                          >
                            <div class="flex items-center">
                              <MtFormCheckBox
                                type="checkbox"
                                label=""
                                :checked="isAllSlotChecked(props.row.dayData, doctor.id_employee)"
                                class="caption1 q-pa-none"
                                style="padding: 0; border: none"
                                @update:checked="(newVal) => updateAllSlotChecked(props.row.dayData, doctor.id_employee, newVal)"
                              />
                            </div>
                            <!-- Display time information alongside checkbox -->
                            <template v-if="props.row.dayData.employee_schedules">
                              <template v-if="!getHighestPrioritySchedule(props.row.dayData.employee_schedules, doctor.id_employee)">
                                <!-- No schedule info -->
                              </template>
                              <template v-else>
                                <div
                                  :class="{ 'text-red': getHighestPrioritySchedule(props.row.dayData.employee_schedules, doctor.id_employee, props.row.dayData.date)?.flg_whole_dayoff }"
                                  class="schedule-item"
                                >
                                  <template v-if="getHighestPrioritySchedule(props.row.dayData.employee_schedules, doctor.id_employee, props.row.dayData.date)?.flg_whole_dayoff">
                                    休
                                  </template>
                                  <template v-else>
                                    <div v-html="formatMultipleTimeSlots(getHighestPrioritySchedule(props.row.dayData.employee_schedules, doctor.id_employee, props.row.dayData.date))"></div>
                                  </template>
                                </div>
                              </template>
                            </template>
                          </div>
                        </template>
                      </template>
                      <template v-else>
                        <div class="text-red">休</div>
                      </template>
                    </div>
                  </q-td>
                </q-tr>
              </template>
            </q-table>
          </div>

          <!-- Loading or no data states -->
          <div v-else-if="isLoading" class="flex justify-center items-center" style="height: 300px">
            <q-spinner size="40px" color="primary" />
          </div>
          <div v-else class="flex justify-center items-center" style="height: 300px">
            <div class="text-grey-700">データがありません</div>
          </div>
        </div>
      </q-page>
    </q-page-container>
  </q-layout>
</template>

<style lang="scss" scoped>
.doctor-wrapper {
  display: flex;
  flex-direction: row;
  width: calc(100% - 75px - 22.11%);
}

.doctor-wrapper .doc-name {
  flex: 1;
  text-align: center;
}

.availability {
  flex: 1;
  text-align: center;
}

.business-hours-column {
  display: flex;
  flex-direction: column;
  min-height: 95px;
}

.slot-checkbox-wrapper {
  display: flex;
  align-items: center;
  padding: 2px 0;

  .slot-label {
    margin-right: 5px;
  }
}

@media screen and (max-width: 1180px) and (min-width: 820px) {
  .col-2.flex.items-center.justify-between.q-bb.q-br.q-pa-sm.h-95.bg-accent-100 {
    width: 12% !important;
  }

  .business-hours-column {
    width: 28.11% !important;
  }

  .col-4.bg-grey-300.text-center.q-ba-400.q-pa-sm {
    width: 28.11% !important;
  }
}

.calendar-view {
  .monthly-schedule-qtable {
    .q-table__top,
    .q-table__bottom {
      display: none;
    }

    .q-table {
      border-collapse: collapse;
    }

    // Header styling
    thead tr th {
      background-color: $grey-300;
      height: 40px;
      padding: 8px;
      border: 1px solid $grey-400;
      font-size: 12px;
      position: sticky;
      top: 0;
      z-index: 10;

      &:first-child {
        width: 75px;
        background-color: $grey-300;
      }

      &:nth-child(2) {
        width: 22.11%;
        background-color: $grey-300;
      }
    }

    .doctor-header-cell {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4px;
      
      .doctor-name {
        font-size: 12px;
        line-height: 1.2;
      }
    }

    // Body cell styling
    tbody tr {
      &.off-day-row {
        background-color: $grey-400;
      }

      &.today-row {
        .date-cell,
        .business-hours-cell,
        .doctor-schedule-cell {
          background-color: rgba(255, 235, 59, 0.3) !important;
        }
      }

      td {
        height: 95px;
        vertical-align: top;
        border: 1px solid $grey-400;
        padding: 0;

        &.date-cell {
          width: 75px;
          text-align: center;
          font-size: 13px;
          
          > div {
            height: 95px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 8px;
          }
        }

        &.business-hours-cell {
          width: 22.11%;
          
          > div {
            height: 95px;
            display: flex;
            flex-direction: column;
          }
        }

        &.doctor-schedule-cell {
          text-align: center;
          
          .schedule-content {
            height: 95px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: $grey-100;
          }

          .schedule-item {
            padding: 3px;
            font-size: 13px;
            min-height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }
  }

  // Utility classes
  .h-40 {
    height: 40px;
  }
  .h-95 {
    height: 95px;
  }
  .h-30 {
    height: 30px;
  }
  .h-45 {
    height: 45px;
  }
  .p-5 {
    padding: 5px;
  }
  .bg-yellow-100 {
    background-color: rgba(255, 235, 59, 0.3) !important;
  }
  .bg-grey-300 {
    background-color: $grey-300 !important;
  }
  .text-blue {
    color: $blue !important;
  }
  .text-red {
    color: $darkRed !important;
  }
  .truncated {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    width: 250px !important;
    word-wrap: break-word;
    white-space: normal !important;
    text-align: left;
    @media only screen and (min-width: 1500px) {
      width: 130px !important;
    }
  }
}

.cursor-pointer {
  cursor: pointer;
}
</style>
