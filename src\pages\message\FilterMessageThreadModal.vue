<script setup lang="ts">
import { onMounted, ref, reactive, computed, watch } from 'vue'
import MtInputForm from '@/components/form/MtInputForm.vue'

import MtSearchCustomer from '@/components/MtSearchCustomer.vue'
import MtModalHeader from '@/components/MtModalHeader.vue'
import MtPetInfoLabel from '@/components/customers/MtPetInfoLabel.vue'
import InputEmployeeOptGroup from '@/components/form/InputEmployeeOptGroup.vue'
import useMessageStore from '@/stores/message-clinic'
import useEmployeeStore from '@/stores/employees'
import useCustomerStore from '@/stores/customers'
import { concatenate } from '@/utils/aahUtils'
import _ from 'lodash'

const emits = defineEmits(['close'])
const employeesStore = useEmployeeStore()
const customerStore = useCustomerStore()
const messageStore = useMessageStore()
const showPets = ref(false)
const customerList = ref([])
const customerListDefault = reactive<any>([])
const petList = ref<any>([])
const petListDefault = reactive<any>([])
const employeesList = ref<any>([])
const employeesListDefault = reactive<any>([])
const defaultEmployee = JSON.parse(localStorage.getItem("id_employee") || 'null')
const props = defineProps({
  filterData: {
    type: Object,
    default: {
      name_thread: '',
      id_customer: '',
      id_pet: '',
      flg_closed: false,
      id_employee_ask: '',
      id_employee_answer: '',
      number_link1: '',
      flg_goal_achieved: false
    }
  },
  fields: {
    type: Object,
    default: {
      value: 0
    }
  },
  departments: {
    type: Array,
    default: () => []
  },
  cancelFilter: {
    type: Object,
    default: {
      value: false
    }
  }
})

const duplicateVar = value => {
  if (value === null || value === undefined) {
    return value;
  }
  // An empty string is a valid JSON value, so it will be duplicated correctly.
  // If you want to treat it as an error, you can add a check here.
  if (typeof value === 'string' && value.length === 0) {
      // return an empty string or null based on desired behavior
      return "";
  }
  try {
    return JSON.parse(JSON.stringify(value));
  } catch (error) {
    // Handle potential stringify/parse errors with other data types if needed
    console.error("Error duplicating value:", error);
    return value; // or return a default value
  }
};

type FilterDataType = {
  type_department: number[] | null
  flg_goal_achieved: boolean | null
  flg_closed: boolean | null
  name_thread: string | null
  number_link1: string | null
  id_customer: string | null
  id_pet: string | null
  id_employee_ask: string | null
  id_employee_answer: string | null
}

// localStorage key for filter persistence
const FILTER_STORAGE_KEY = 'messageThreadFilterData'

// Save filter data to localStorage
const saveFilterToStorage = (filterData: any) => {
  try {
    localStorage.setItem(FILTER_STORAGE_KEY, JSON.stringify(filterData))
  } catch (error) {
    console.error('Failed to save filter data to localStorage:', error)
  }
}

// Load filter data from localStorage
const loadFilterFromStorage = () => {
  try {
    const stored = localStorage.getItem(FILTER_STORAGE_KEY)
    return stored ? JSON.parse(stored) : null
  } catch (error) {
    console.error('Failed to load filter data from localStorage:', error)
    return null
  }
}

// Clear filter data from localStorage
const clearFilterFromStorage = () => {
  try {
    localStorage.removeItem(FILTER_STORAGE_KEY)
  } catch (error) {
    console.error('Failed to clear filter data from localStorage:', error)
  }
}

const selectDefaultEmployee = (key: string) => {
  props.filterData[key as keyof FilterDataType] = defaultEmployee
}

// Customer selection logic
const selectingCustomer = async (value: any) => {
  props.filterData.id_customer = value
  // If address length not matched then refresh the list
  if (value) {
    // code_customer and code_ahmics_customer
    await customerStore.selectCustomerOptions(value)
    const selectedCustomer = customerStore.getCustomerOption
    if (selectedCustomer) {
      // This ensures the customer name is properly displayed in the MtSearchCustomer component
      // The component will automatically show the customer name based on the preSelectedId
    }
  }
  await handlePetsList(value)
}

// Pet selection logic
const selectPet = (petId: string) => {
  props.filterData.id_pet = petId
}

// Department toggle logic for multiple selection
const toggleDepartment = (deptValue: number) => {
  if (!props.filterData.type_department) {
    props.filterData.type_department = []
  }

  const index = props.filterData.type_department.indexOf(deptValue)
  if (index > -1) {
    // Remove if already selected
    props.filterData.type_department.splice(index, 1)
  } else {
    // Add if not selected
    props.filterData.type_department.push(deptValue)
  }
}

const init = () => {
  if (
    props.filterData?.id_customer === null ||
    props.filterData?.id_customer === ''
  ) {
    props.filterData.id_pet = ''
    showPets.value = false
  } else if (
    props.filterData?.id_customer !== null ||
    props.filterData?.id_customer !== ''
  ) {
    showPets.value = true
  }
  if (petListDefault.length < 1) {
    props.filterData.id_pet = ''
  }
}
const closeModal = () => {
  props.cancelFilter.value = true
  initialSetup()
  emits('close')
}

const cancelFilteration = async () => {
  props.cancelFilter.value = true
  closeModal()
}

const filterThreadData = async () => {
  if (props.filterData) {
    let valuedkeys = Object.keys(props.filterData).filter(
      (key) => {
        const value = props.filterData[key as keyof typeof props.filterData]
        if (Array.isArray(value)) {
          return value.length > 0
        }
        return value !== '' && value !== null && value !== false
      }
    )
    let data: any = {}
    valuedkeys.forEach((key) => {
      if (key === 'type_department' && Array.isArray(props.filterData[key]) && props.filterData[key].length > 0) {
        data[key] = `[${props.filterData[key].join(',')}]`
      } else {
        data[key] = props.filterData[key]
      }
    })
    let totalValuedKeysLength = Object.keys(props.filterData).filter(
      (key) => {
        const value = props.filterData[key as keyof typeof props.filterData]
        if (Array.isArray(value)) {
          return value.length > 0
        }
        return value !== '' && value !== null && value !== false
      }
    )

    // Save filter data to localStorage
    saveFilterToStorage(props.filterData)
    await messageStore.fetchThreadMessages(data)
    props.fields.valuedTotalFields = totalValuedKeysLength.length

    emits('close')
  }
}

// Clear all filters function
const clearAllFilters = async () => {
  // Reset all filter values
  props.filterData.name_thread = ''
  props.filterData.number_link1 = ''
  props.filterData.id_customer = ''
  props.filterData.id_pet = ''
  props.filterData.flg_closed = false
  props.filterData.flg_goal_achieved = ''
  props.filterData.id_employee_ask = ''
  props.filterData.id_employee_answer = ''
  props.filterData.type_department = []

  // Clear pets list
  petList.value.length = 0
  petListDefault.length = 0
  showPets.value = false

  // Clear localStorage
  clearFilterFromStorage()

  // Apply the cleared filters (this will fetch data with no filters and close the modal)
  await filterThreadData()
}

const handlePetsList = async (value: any) => {
  await customerStore.selectCustomer(value)
  if (value) {
    const selectedCustomer = customerStore?.getCustomer
    if (selectedCustomer) {
      if (selectedCustomer.pets.length) {
        petListDefault.length = 0
        selectedCustomer.pets.map((petObj: any) => {
          petListDefault.push({
            name_pet: petObj.name_pet,
            name_kana_pet: petObj.name_kana_pet,
            code_pet: petObj.code_pet,
            value: petObj.id_pet
          })
        })
        petList.value = [...petListDefault]
        if (petList.value.length > 0 && !props.filterData.id_pet) {
          props.filterData.id_pet = petList.value[0].value
          const savedFilter = loadFilterFromStorage()
          if (savedFilter?.id_pet) {
            selectPet(savedFilter.id_pet)
          }
        }
      }
    }
  } else {
    props.filterData.id_pet = ''
    petList.value.length = 0
    petListDefault.length = 0
  }
  init()
}



// why we use local? because from the parent, the data is empty string as default, which is not good for checkbox input
// so we convert it into boolean and make it false as default
const localFlgGoalAchieved = computed({
  // Getter: Transforms the prop value for the checkbox
  get() {
    // If the prop value is an empty string, treat it as 'false' for the checkbox
    return props.filterData.flg_goal_achieved === '' ? false : props.filterData.flg_goal_achieved;
  },
  // Setter: Transforms the checkbox's value back for the prop
  set(newValue) {
    // If the checkbox emits 'false', set the prop value back to an empty string.
    // Otherwise, use the new value (which would be 'true' if checked).
    props.filterData.flg_goal_achieved = newValue === false ? '' : newValue;
  },
});

// Watch for type_department changes to auto-populate number_link1
// watch(() => props.filterData.type_department, (newDepartments) => {
//   if (newDepartments && newDepartments.length > 0) {
//     // Auto-populate number_link1 based on first selected department
//     const firstDept = (props.departments as any[]).find((dept: any) => dept.value === newDepartments[0])
//     if (firstDept) {
//       props.filterData.number_link1 = `${firstDept.value}-${firstDept.label}`
//     }
//   } else {
//     // Clear number_link1 if no departments selected
//     if (props.filterData.number_link1?.includes('-')) {
//       props.filterData.number_link1 = ''
//     }
//   }
// }, { deep: true })

const initialSetup = () => {
  employeesList.value.length = 0
  employeesListDefault.length = 0
  employeesList.value = [...employeesStore.getEmployeeListOptions] as any
  employeesListDefault.push(...employeesStore.getEmployeeListOptions)
  customerList.value.length = 0
  customerListDefault.length = 0
  customerList.value = [...customerStore.getCustomerListOptions] as any
  customerListDefault.push(...customerStore.getCustomerListOptions)

  // Initialize type_department as array if not already
  if (!Array.isArray(props.filterData.type_department)) {
    props.filterData.type_department = []
  }

  // Load saved filter data from localStorage
  const savedFilter = loadFilterFromStorage()
  if (savedFilter) {
    // Merge saved data with current filter data
    Object.keys(savedFilter).forEach(key => {
      if (savedFilter[key] !== null && savedFilter[key] !== '' && savedFilter[key] !== false) {
        if (key === 'type_department' && Array.isArray(savedFilter[key])) {
          props.filterData[key as keyof typeof props.filterData] = savedFilter[key]
        } else if (key !== 'type_department') {
          console.log(props.filterData[key as keyof typeof props.filterData], savedFilter[key]);
          props.filterData[key as keyof typeof props.filterData] = savedFilter[key]
        }
      }
    })
  }
  
  if (props.filterData.id_customer) {
    handlePetsList(props.filterData.id_customer)
  }
  
  init()
}

onMounted(() => {
  initialSetup()
})
</script>

<template>
  <MtModalHeader @closeModal="closeModal">
    <q-toolbar-title class="title2 bold">追加絞り込み条件 </q-toolbar-title>
  </MtModalHeader>
  <q-card-section>
    <div class="row q-col-gutter-md q-mb-md">
      <div class="col-md-6 col-sm-6" :tabindex="1">
        <MtInputForm
          autofocus
          type="text"
          label="スレッド名"
          v-model="props.filterData.name_thread"
        />
      </div>
    </div>
    <div class="row q-col-gutter-md q-mb-md">
      <div class="col-md-6 col-sm-6" :tabindex="2">
        <MtInputForm
          type="text"
          label="連携番号"
          v-model="props.filterData.number_link1"
        />
      </div>
    </div>
    <div class="row q-col-gutter-md q-mb-md">
      <div class="col-md-6 col-sm-6" :tabindex="3">
        <MtSearchCustomer
          :applyDefaultClass="false"
          :preSelectedId="props.filterData.id_customer"
          custom-option
          class="q-pb-none"
          label="診察券番号・オーナー"
          @update:selecting="selectingCustomer"
        />
      </div>
      <div class="col-12" v-if="showPets">
        <div class="pet-selection-container">
          <div
            v-for="pet in petListDefault"
            :key="pet.value"
            class="pet-card"
            :class="{ 'pet-card-selected': props.filterData.id_pet == pet.value }"
            @click="selectPet(pet.value)"
          >
            <div class="pet-icon">
              <MtPetInfoLabel :pet="pet" is-clickable>
              <template #codePet>
                {{ pet.code_pet }}
              </template>
              <template #kanaFullname>
                {{ pet.name_kana_pet }}
              </template>
              <template #fullname>
                {{ pet.name_pet }}
              </template>

              </MtPetInfoLabel>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="row q-col-gutter-md q-mb-md" :tabindex="5">
      <div class="col-lg-6 col-md-6 col-sm-6">
        <InputEmployeeOptGroup
          v-model:selected="props.filterData.id_employee_ask"
          default-blank
          label="質問者名"
          clearable
          class="clear-icon"
          dense
          @update:select-default-employee="selectDefaultEmployee('id_employee_ask')"
        />
      </div>
      <div class="col-lg-6 col-md-6 col-sm-6" :tabindex="6">
        <InputEmployeeOptGroup
          v-model:selected="props.filterData.id_employee_answer"
          default-blank
          label="希望回答者"
          clearable
          class="clear-icon"
          dense
          @update:select-default-employee="selectDefaultEmployee('id_employee_answer')"
        />
      </div>
      <q-separator class="q-my-md full-width-remove-v-padding" />
      <div class="col-12 full-width-remove-v-padding">
        <small>ステータス</small>
      </div>
      <div class="col-3" >
        <MtInputForm
          v-model="localFlgGoalAchieved"
          type="checkbox"
          :items="[{ label: '目標達成' }]"
        />
      </div>
      <div class="q-ml-sm" :tabindex="8">
        <MtInputForm
          v-model="props.filterData.flg_closed"
          type="checkbox"
          :items="[{ label: 'クローズ' }]"
        />
      </div>
      <q-separator class="q-my-md full-width-remove-v-padding" />
      <div class="col-12 full-width-remove-v-padding" :tabindex="9">
        <small>部署</small>
      </div>
      <div class="col-12 btn-toggle-departement mb-8px">
        <div class="custom-btn-group">
          <q-btn
            v-for="dept in (props.departments as any[])"
            :key="dept.value"
            :class="[
              'custom-btn-item',
              { 'custom-btn-selected': props.filterData.type_department.includes(dept.value) }
            ]"
            :label="dept.label"
            size="15px"
            padding="0px 10px"
            no-caps
            unelevated
            @click="toggleDepartment(dept.value)"
          />
        </div>
      </div>
    </div>
    <div class="flex justify-end mr-clear-btn">
      <q-btn
        outline
        class="bg-grey-100 text-grey-800"
        @click="clearAllFilters"
      >
        全クリア
      </q-btn>
    </div>
  </q-card-section>
  <q-card-section class="q-bt bg-white">
    <div class="text-center modal-btn">
      <span>
        <q-btn
          outline
          class="bg-grey-100 text-grey-800"
          @click="cancelFilteration"
        >
          キャンセル
        </q-btn>
      </span>
      <span :tabindex="7">
        <q-btn
          unelevated
          color="primary"
          class="q-ml-md"
          @click="filterThreadData"
        >
          検索

        </q-btn>
      </span>
    </div>
  </q-card-section>
</template>
<style lang="scss" scoped>
.full-width-remove-v-padding {
  width: 100%; padding-top: 0px; padding-bottom: 0px;
}
.column-2-department {
  margin-left: 22px;
}

.pet-selection-container {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  margin-top: 8px;
}

.pet-card {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border: 2px solid transparent;
  border-radius: 8px;
  background-color: #eeeeee;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 160px;

  &:hover {
    background-color: #e0e0e0;
  }

  &.pet-card-selected {
    background-color: #fff4cb;
    border-color: #edcc5a;
  }

  .pet-info {
    display: flex;
    flex-direction: column;
    gap: 2px;

    .pet-code {
      font-size: 12px;
      font-weight: 600;
      color: #333;
      line-height: 1.2;
    }

    .pet-name {
      font-size: 14px;
      font-weight: 500;
      color: #666;
      line-height: 1.2;
    }

    .pet-card-selected & {
      .pet-code {
        color: #8b7355;
      }

      .pet-name {
        color: #8b7355;
      }
    }
  }
}

// Custom button group styling inspired by CodePen example
.custom-btn-group {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.custom-btn-item {
  border-radius: 4px !important;
  border: 1px solid $grey-500 !important;
  background-color: white !important;
  white-space: nowrap;
  transition: all 0.3s ease;
  cursor: pointer;
  color: $grey-700;
  min-height: 32px;
  font-size: 14px;
  font-weight: 500;

  &:hover {
    background-color: #f5f5f5 !important;
  }

  &.custom-btn-selected {
    background-color: #3C7AD6 !important;
    border-color: #3C7AD6 !important;
    color: white !important;

    &:hover {
      background-color: #2c5aa0 !important;
      border-color: #2c5aa0 !important;
    }
  }
}

// Legacy btn-toggle-departement styling for compatibility
:deep(.btn-toggle-departement .q-btn-group > .q-btn-item) {
  border-radius: 4px;
  flex-wrap: nowrap!important;
  white-space: nowrap!important;
  margin-right: 8px;
  background-color: #3C7AD6;
}

:deep(.btn-toggle-departement .q-btn-group) {
  flex-wrap: wrap!important;
}

// Default border for all buttons in the toggle
:deep(.btn-toggle-departement .q-btn[aria-pressed="false"]) {
  border: 1px solid #9e9e9e !important;
}

// Default border for all buttons in the toggle
:deep(.btn-toggle-departement .q-btn[aria-pressed="true"]) {
  border: 1px solid #3C7AD6 !important;
}

:deep(.mb-8px .q-btn-group > .q-btn-item) {
  margin-bottom: 8px;
}

.mr-clear-btn {
  margin-right: 10px;
}
</style>