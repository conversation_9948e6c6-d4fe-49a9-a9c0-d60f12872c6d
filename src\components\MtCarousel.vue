<script setup lang="ts">

import { computed, onBeforeUnmount, onMounted, ref, watch } from 'vue'
import { debounce, throttle } from 'lodash'
import emblaCarouselVue from 'embla-carousel-vue'


/**
 * pass it a list of options
 */
const props = withDefaults(defineProps<{
  options: Array<any>
  modelValue: Number,
  optionLabel?: string | Function
  optionValue?: string
}>(), {
  optionLabel: 'label',
  optionValue: 'value'
})

const emits = defineEmits(['update:model-value'])

const slideItemRefs = ref([])

const [emblaRef, emblaApi] = emblaCarouselVue({loop: true,slidesToScroll: 1, align: 'center'})
let manualScrollCalculateDisable = false
onMounted(async () => {
  emblaRef.value?.addEventListener('wheel', wheelScrollHandler)
  if(emblaApi?.value) {
    emblaApi.value.on('slidesInView', slideIntoViewHandler)
    emblaApi.value.on('init', carouselOnInitHandler)
  }
})

const carouselOnInitHandler = () => {
  const tempPosition = props.options.findIndex((item => {
    return item[props.optionValue] === props.modelValue
  }))
  if(tempPosition < 0) {
    return
  }
  manualItemSelected(tempPosition)
}

const slideIntoViewHandler = () => {
  if(manualScrollCalculateDisable) {
    return
  }
  if(props.modelValue === null || props.modelValue === undefined) {
    return
  }
  const visibleElements = slideItemRefs.value
    .filter((item, index) => {
      return isVisible(item.getBoundingClientRect(), emblaRef?.value?.getBoundingClientRect())
    }).sort((a, b) => {
      const aRect = a.getBoundingClientRect();
      const bRect = b.getBoundingClientRect();
      if (Math.abs(aRect.top - bRect.top) > 5) {
        return aRect.top - bRect.top;
      } else {
        return aRect.left - bRect.left;
      }
    })
  const mid = Math.floor(visibleElements.length / 2)
  visibleElements.forEach((item, index) => {
    if(index === mid) {
      updateModelValue(item.getAttribute('data'))
      item.classList.add('selected')
    } else {
      item.classList.remove('selected')
    }
  })
}

const _onWheel = throttle((event) => {
  const threshold = 100;
  if (event.deltaY > threshold) {
    emblaApi.value.scrollNext();
    emblaApi.value.scrollNext();
  } else if (event.deltaY < -threshold) {
    emblaApi.value.scrollPrev();
    emblaApi.value.scrollPrev();
  } else if (event.deltaY > 0) {
    emblaApi.value.scrollNext();
  } else {
    emblaApi.value.scrollPrev();
  }
}, 100)


const wheelScrollHandler = (event) => {
  event.preventDefault()
  manualScrollCalculateDisable = false
  _onWheel(event)
}

const isVisible = (childRect, containerRect) => (
  childRect.bottom > containerRect.top &&
  childRect.top < containerRect.bottom &&
  childRect.right > containerRect.left &&
  childRect.left < containerRect.right
);

const isContainerOverflowing = computed(() => {
  return emblaApi.value.canScrollNext() || emblaApi?.value?.canScrollPrev()
})

const options = computed(() => {
  return props.options
})

const manualItemSelected = (slideItemIndex) => {
  if (emblaApi.value) {
    manualScrollCalculateDisable = true
    emblaApi.value.scrollTo(slideItemIndex)
    slideItemRefs.value.forEach((item, index) => {
      if (index === slideItemIndex) {
        updateModelValue(item.getAttribute('data'))
        item.classList.add('selected')
      } else {
        item.classList.remove('selected')
      }
    })
  }
}

const updateModelValue = debounce((value) => {
  const index = Number.parseInt(value)
  if(!props.options[index]) {
    return
  }
  const selectedValue = props.options[index][props.optionValue]
  emits('update:model-value', selectedValue)
}, 500)

onBeforeUnmount(() => {
  emblaRef.value?.removeEventListener('wheel', wheelScrollHandler)
  if(emblaApi?.value) {
    emblaApi.value.off('slidesInView', slideIntoViewHandler)
    emblaApi.value.off('init', carouselOnInitHandler)
  }
})
</script>

<template>
<div class="">
  <div class="embla" ref="emblaRef">
    <div class="embla__container">
      <div class="embla__slide cursor-pointer flex justify-center items-center" style="margin-right: 16px " v-for="(item, index) in options" :data="index" :key="index" ref="slideItemRefs" @click="manualItemSelected(index)">
          <q-chip class="content flex flex-1 justify-center items-center fit-content ellipsis bg-grey-2 selected-text" style="height: fit-content">
            {{item[props.optionLabel]}}
          </q-chip>
      </div>
    </div>
  </div>
</div>
</template>

<style scoped lang="scss">
.embla {
  overflow: hidden;
}
.embla__container {
  display: flex;
}
.embla__slide {
  font-size: 18px;
  flex: 0 0 fit-content;
  min-width: 0;
  height: 48px;
  align-items: center;
  justify-content: center;
  
  .content {
    min-width: 80px;
  }
  
  &.selected > .selected-text{
    font-weight: 700;
    color: $grey-10 !important;
    background: $lime-2 !important;
  }
}
</style>