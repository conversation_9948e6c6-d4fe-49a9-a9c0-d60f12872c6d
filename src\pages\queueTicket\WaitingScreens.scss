.ticket-to-call {
  height: 100vh;
  img {
    object-fit: contain;
    max-width: 75vw;
    max-height: 75vh;
  }
}

.ticket-to-call-gif {
  img {
    max-width: 100vw;
    height: 100vh;
  }
}

.qtcalling-screen-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .calling-text,
  .ticket-number,
  .room-number {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    font-size: clamp(3vh, 6vw, 8vh);
  }

  .calling-text {
    animation: blink 1s infinite;
  }

  .ticket-number {
    line-height: 1;
    font-size: clamp(8vh, 60vh, 60vh);
    transform: translateY(-8%);
  }

  .room-number {
    .room-text{
      font-size: clamp(4vh, 8vw, 10vh);
      color: blue;
      margin-right: 20px;
    }
  }
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

@keyframes blink {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}