<script setup lang="ts">
import { computed, onMounted, ref, watch, inject } from 'vue'
import dayjs from '@/boot/dayjs'
import { storeToRefs } from 'pinia'
import useBookingItemStore from '@/stores/booking-items'
import useWorkScheduleStore from '@/stores/work-schedules'
import useClinicStore from '@/stores/clinics'
import useEmployeeStore from '@/stores/employees'
import mtUtils from '@/utils/mtUtils'
import { typeBusinessDay } from '@/utils/enum'
import { flatten } from 'lodash'
// Import MtFormCheckBox correctly by ignoring TypeScript errors for now
// @ts-ignore
import MtFormCheckBox from '@/components/form/MtFormCheckBox.vue'
// Import components for the modal
// @ts-ignore - Import with ignoring TypeScript errors
import OptionModal from '@/components/OptionModal.vue'
// @ts-ignore - Import with ignoring TypeScript errors
import BulkUpdateShiftModal from './BulkUpdateShiftModal.vue'
import UpdateBulkShiftModal from '@/pages/employeeAvailability/components/UpdateBulkShiftModal.vue'
import UpdateEmployeeModal from '@/pages/master/employee/UpdateEmployeeModal.vue'

import {
  EmployeeType
} from '@/types/types'
import selectOptions from '@/utils/selectOptions'

// State management
const selectedDate = ref(dayjs().startOf('week'))
const selectedWeekLabel = ref('今週')
const isLoading = ref(false)
const weeklyData = ref<any[]>([])
const clinicName = ref('')
// Data for bulk update
const bulkUpdateData = ref<any[]>([])
const employees = ref<Employee[]>([])

// Store instances
const bookingItemStore = useBookingItemStore()
const workScheduleStore = useWorkScheduleStore()
// Type assertion for store methods
const schedulingStore = workScheduleStore
const clinicStore = useClinicStore()
const employeeStore = useEmployeeStore()

// Get the clinic ID and date range from inject (provided by parent component)
const injectedClinicId = inject<number>('clinicId')
const injectedStartDate = inject<string>('startDate')
const injectedEndDate = inject<string>('endDate')
const injectedEditMode = inject<boolean>('editMode')
const injectedDisplayMode = inject<'slot' | 'day'>('displayMode')

// Get the clinic ID and date range from props or localStorage
const props = defineProps<{
  clinicId?: number | null
  startDate?: string
  endDate?: string
  editMode?: boolean
  displayMode?: 'slot' | 'day'
}>()

// Use provided editMode and displayMode from props or inject
const editMode = computed(() => {
  return props.editMode !== undefined ? props.editMode : injectedEditMode || false
})

const displayMode = computed(() => {
  return props.displayMode !== undefined ? props.displayMode : injectedDisplayMode || 'slot'
})

const clinicId = computed(() => {
  // First check if props.clinicId is available
  if (props.clinicId) {
    return props.clinicId
  }
  
  // Then check if injected clinicId is available
  if (injectedClinicId) {
    return injectedClinicId
  }
  
  // Finally fallback to localStorage
  const storedId = localStorage.getItem('id_clinic')
  return storedId ? JSON.parse(storedId) : null
})

// Watch for clinic ID changes to refetch data
watch(clinicId, async (newClinicId) => {
  if (newClinicId) {
    await fetchClinicName()
    await fetchWeeklySchedule()
  }
})

// Interface definitions
interface EmployeeSchedule {
  id_employee_workschedule: number | null
  time_workschedule_start?: string
  time_workschedule_end?: string
  flg_whole_dayoff: boolean
  checked: boolean
}

interface Employee {
  id_employee: number
  name_display: string
  type_occupation: number
  flg_calendar: boolean
  display_order: number | null
}

interface EmployeeSchedules {
  [key: string]: {
    [key: number]: EmployeeSchedule
  }
}

interface TimeSlot {
  slot_number: number
  business_time: {
    start: string
    end: string
  }
  checkin_time?: {
    start: string
    end: string
  }
  ticket_issue_time?: {
    start: string
    end: string
  }
  ticket_limit?: number | null
}

interface DayData {
  display_date: string
  date?: string | null
  day_index: number
  day_of_week: number
  type_weekday: number // UI value (11-17, 18 for holiday)
  today: boolean
  business_hour_slot?: {
    id_business_hour_slot?: number
    type_business_day: number
    name_business_hour: string
    display_order?: number
    time_slots?: TimeSlot[]
  }
  employeeSchedules: EmployeeSchedules
  is_off_day: boolean
  slot_name?: string
  slot_type?: number
  employee_schedules?: Array<{
    id_employee: number
    name_display: string
    type_occupation: number
    flg_calendar: boolean
    schedules: Array<{
      id_employee_workschedule: number
      time_workschedule_start: string
      time_workschedule_end: string
      flg_whole_dayoff: boolean
    }>
  }>
  isHoliday?: boolean
}

// Interface for schedule
interface EmployeeScheduleItem {
  id_employee_workschedule: number | null;
  time_workschedule_start: string;
  time_workschedule_end: string;
  time_workschedule_start2?: string;
  time_workschedule_end2?: string;
  time_workschedule_start3?: string;
  time_workschedule_end3?: string;
  flg_whole_dayoff: boolean;
  type_weekday?: number;
  checked: boolean;
}

const excludedEmployeeTypeOccupations = [1000, 1107]
const sortedDoctors = computed(() => {
  return employees.value
    .filter(emp => !excludedEmployeeTypeOccupations.includes(emp.type_occupation))
    .sort((a, b) => {
      if (a.type_occupation === b.type_occupation) {
        return (a.display_order || 999) - (b.display_order || 999)
      }
      return (a.type_occupation || 1999) - (b.type_occupation || 1999)
    })
})

// Helper function to check if a day is Saturday (type_weekday = 16)
const isSaturday = (day: DayData) => {
  return day.type_weekday === 16
}

// Helper function to check if a day is Sunday (type_weekday = 17)
const isSunday = (day: DayData) => {
  return day.type_weekday === 17
}

// Helper function to check if a day is a Holiday (type_weekday = 18)
const isHoliday = (day: DayData) => {
  return day.type_weekday === 18
}

// Change the selected week
const changeDate = async (prefix: 'next' | 'prev') => {
  if (prefix === 'next') {
    selectedDate.value = selectedDate.value.add(1, 'week')
  } else {
    selectedDate.value = selectedDate.value.subtract(1, 'week')
  }
  selectedWeekLabel.value = `${selectedDate.value.format('YYYY/MM/DD')} ~ ${selectedDate.value
    .add(6, 'day')
    .format('YYYY/MM/DD')}`
  await fetchWeeklySchedule()
}

// Fetch weekly schedule data
const fetchWeeklySchedule = async () => {
  if (!clinicId.value) return
  
  isLoading.value = true

  try {
    // Fetch scheduling data for the selected week
    const response = await workScheduleStore.fetchSchedulingData({
      clinic_id: clinicId.value,
      period_type: 'weekly',
      start_date: selectedDate.value.format('YYYY-MM-DD')
    })

    // Set employees from API response
    if (response?.employees) {
      employees.value = response.employees
    }

    buildWeeklyData()
  } catch (error) {
    console.error('Error fetching weekly schedule:', error)
  } finally {
    isLoading.value = false
  }
}

// Build weekly data structure from scheduling API response
const buildWeeklyData = () => {
  const schedulingData = schedulingStore.getWeeklySchedulingData()
  if (!schedulingData?.length) return

  weeklyData.value = schedulingData.map((day: any) => {
    // For holiday (type_weekday: 18), we don't need to calculate a specific date
    const dayDate = day.type_weekday === 18 
      ? null 
      : selectedDate.value.add(day.day_index, 'day')

    // Create day data object
    const dayData: DayData = {
      // For holiday, use a static display name
      display_date: day.type_weekday === 18 ? '祝祭日' : (dayDate ? dayDate.format('ddd') : ''),
      date: dayDate ? dayDate.format('YYYY-MM-DD') : null,
      day_index: day.day_index,
      day_of_week: day.day_index, // 0-6 for Monday-Sunday
      type_weekday: day.type_weekday, // 11-17 for UI display, 18 for holiday
      today: dayDate ? dayjs().isSame(dayDate, 'day') : false,
      business_hour_slot: day.business_hour_slot,
      employeeSchedules: {},
      is_off_day: day.business_hour_slot?.type_business_day === 90,
      slot_name: day.slot_name,
      slot_type: day.slot_type,
      employee_schedules: day.employee_schedules,
      isHoliday: day.type_weekday === 18
    }

    // Map employee availability data
    if (day.employee_schedules) {
      day.employee_schedules.forEach((employeeSchedule: any) => {
        if (!dayData.employeeSchedules[employeeSchedule.id_employee]) {
          dayData.employeeSchedules[employeeSchedule.id_employee] = {}
        }

        // Map each schedule to a slot number
        employeeSchedule.schedules.forEach((schedule: any) => {
          dayData.employeeSchedules[employeeSchedule.id_employee] = {
            checked: false,
            id_employee_workschedule: employeeSchedule.id_employee_workschedule || null,
            time_workschedule_start: schedule.time_workschedule_start,
            time_workschedule_end: schedule.time_workschedule_end,
            flg_whole_dayoff: schedule.flg_whole_dayoff,
            type_weekday: day.type_weekday,
            min_rest: schedule.min_rest
          }
        })
      })
    }

    // Create slots for all doctors and all potential time slots
    sortedDoctors.value.forEach((doctor) => {
      if (!dayData.employeeSchedules[doctor.id_employee]) {
        dayData.employeeSchedules[doctor.id_employee] = {
          checked: false,
          id_employee_workschedule: null,
          flg_whole_dayoff: false,
          min_rest: null,
          type_weekday: day.type_weekday
        }
      }
    })

    return dayData
  })

  console.log('Weekly data built:', weeklyData.value)
}

// Helper function to match a time to a slot number
const getSlotNumberForTime = (time: string, timeSlots?: TimeSlot[]): number | null => {
  if (!timeSlots) return null

  for (let i = 0; i < timeSlots.length; i++) {
    if (timeSlots[i].business_time.start === time) {
      return i + 1
    }
  }
  return null
}

// Helper function to get business day type name
const typeBusinessDayName = (value: number) => typeBusinessDay.find((v) => v.value === value)

// Get total number of time slots for a day
const getTotalSlots = (timeSlots?: TimeSlot[]) => {
  return timeSlots?.length || 0
}

// Check if all slots for an employee on a specific day are marked as off
const isAllSlotChecked = (data: DayData, idEmployee: number | string) => {
  const employeeSchedules = data.employeeSchedules[idEmployee]
  if (!employeeSchedules) return false

  return employeeSchedules.checked
}

// Set all slots for an employee on a specific day to checked or unchecked
const setAllSlotChecked = (checked: boolean, data: DayData, idEmployee: number | string) => {
  const employeeSchedules = data.employeeSchedules[idEmployee]
  if (!employeeSchedules) return

  employeeSchedules.checked = checked
}

// Update all slots for an employee on a specific day
const updateAllSlotChecked = (day: DayData, employeeId: number | string, value: boolean) => {
  setAllSlotChecked(value, day, employeeId)
}

// Helper function to prepare the bulk update data
const prepareBulkUpdateData = async (): Promise<any[]> => {
  const bulkData = []
  const weekData = weeklyData.value.slice()
  weekData.forEach((day) => {

    Object.entries(day.employeeSchedules).forEach(([employeeId, data]) => {
      const hasCheckedEmployee = data.checked
      if(!hasCheckedEmployee) return

      const typeWeekday = day.type_weekday >= 11 && day.type_weekday <= 18 
            ? day.type_weekday 
            : 11

      const idEmployeeWorkschedule = data.id_employee_workschedule || null;
      
      const scheduleData = {
        id_employee_workschedule: idEmployeeWorkschedule,
        id_employee: parseInt(employeeId as string),
        type_weekday: typeWeekday, 
        time_workschedule_start: data.time_workschedule_start || '00:00:00',
        time_workschedule_end: data.time_workschedule_end || '00:00:00',
        flg_whole_dayoff: data.flg_whole_dayoff,
        id_clinic: clinicId.value
      }
      bulkData.push(scheduleData)
    })
  })

  return bulkData
}

// Save changes
const bulkUpdate = async () => {
  // If in day mode, directly update without showing modal
  if (displayMode.value === 'day') {
    await directBulkUpdate()
    return
  }

  const payload = await prepareBulkUpdateData();
  // Store payload in the ref for use by the modal
  bulkUpdateData.value = payload

  // Show modal instead of making API call directly
  openBulkUpdateModal()
}

// Direct bulk update for day mode
const directBulkUpdate = async () => {
  try {
    isLoading.value = true

    // Collect selected employees and days with checked schedules
    const selectedEmployees = new Set<number>()
    const selectedData: any[] = []
    
    weeklyData.value.forEach((day) => {
      if (day.is_off_day) return // Skip off days

      Object.entries(day.employeeSchedules).forEach(([employeeId, slots]) => {
        const hasCheckedSlots = Object.values(slots as Record<string, EmployeeSchedule>)
          .some(schedule => schedule.checked === true)
        
        if (hasCheckedSlots) {
          selectedEmployees.add(parseInt(employeeId as string))
          selectedData.push({
            id_employee: parseInt(employeeId as string),
            type_weekday: day.type_weekday, // Use the actual weekday type (11-18)
            date_booking_special: null // No special date for weekly view
          })
        }
      })
    })

    if (selectedData.length === 0) {
      mtUtils.alert('更新する予定がありません')
      return
    }

    // Remove duplicates based on employee and weekday
    const uniqueData = selectedData.filter((item, index, self) => 
      index === self.findIndex(t => 
        t.id_employee === item.id_employee && t.type_weekday === item.type_weekday
      )
    )

    // Prepare employee work schedules for API
    const employeeList = Array.from(selectedEmployees).map((employeeId) => {
      // Filter uniqueData for current employee
      const employeeData = uniqueData.filter((item: any) => item.id_employee === employeeId)
      
      const employeeWorkscheduleList = employeeData.map((data: any) => ({
        type_weekday: data.type_weekday,
        time_workschedule_start: '00:00:00',
        time_workschedule_end: '00:00:00',
        flg_whole_dayoff: true, // Always true for day mode
        date_booking_special: null,
        min_rest: 0
      }))

      return {
        id_employee: employeeId,
        employee_workschedule_list: employeeWorkscheduleList
      }
    })

    // Call the API
    if (clinicId.value === null) {
      throw new Error('Clinic ID is required');
    }
    
    await workScheduleStore.createOrUpdateWorkSchedules({
      id_clinic: clinicId.value,
      employee_list: employeeList
    })

    // Show success message
    mtUtils.autoCloseAlert('休みを適用しました！')
    
    // Refresh data and exit edit mode
    await fetchWeeklySchedule()
  } catch (error) {
    console.error('Failed to update schedules:', error)
    mtUtils.autoCloseAlert('スケジュールの更新に失敗しました。')
  } finally {
    isLoading.value = false
  }
}

// Open the bulk update modal
const openBulkUpdateModal = async () => {
  if (!bulkUpdateData.value.length) {
    mtUtils.alert('更新する予定がありません')
    return
  }

  await mtUtils.smallPopup(BulkUpdateShiftModal, {
    bulkUpdateData: bulkUpdateData.value,
    clinicId: clinicId.value,
    displayMode: displayMode.value,
    onSuccess: async () => {
      // Refresh data
      await fetchWeeklySchedule()
    }
  })
}

// Expose bulkUpdate method to parent
defineExpose({
  bulkUpdate,
  fetchWeeklySchedule,
  prepareBulkUpdateData
})

// Fetch clinic name
const fetchClinicName = async () => {
  if (clinicId.value) {
    try {
      const clinic = await clinicStore.fetchClinicById(clinicId.value)
      clinicName.value = clinic.name_clinic_display
    } catch (error) {
      console.error('Error fetching clinic name:', error)
      clinicName.value = ''
    }
  } else {
    clinicName.value = ''
  }
}

// Format time string to display format (removing :00 seconds)
const formatTimeDisplay = (time: string) => {
  return time ? time.replace(/:00$/, '') : ''
}

// Check if all slots for an employee across all days in the current week are marked as off
const isAllDaysSlotChecked = (employeeId: number | string) => {
  // Include all non-off days, including holiday type (18)
  const days = weeklyData.value.filter(day => day)
  if (!days.length) return false
  
  return days.every(day => {
    const employeeSchedules = day.employeeSchedules[employeeId]
    if (!employeeSchedules) return false
    
    return employeeSchedules.checked
  })
}

// Update all slots for an employee across all days in the current week
const updateAllDaysSlotChecked = (employeeId: number | string, value: boolean) => {
  // Include all non-off days, including holiday type (18)
  const days = weeklyData.value.filter(day => day)
  days.forEach(day => {
    updateAllSlotChecked(day, employeeId, value)
  })
}

// Add click handler for schedule items
const handleScheduleClick = async (day: DayData, idEmployee: number) => {
  const employeeSlotData = day.employeeSchedules[idEmployee]
  if(!employeeSlotData) return false
  const bulkData = []
  bulkData.push({ ...employeeSlotData, id_employee: parseInt(idEmployee) })
  await mtUtils.smallPopup(UpdateBulkShiftModal, {
    bulkData
  })
  fetchWeeklySchedule()
}

// Add a new helper function to get the highest priority schedule
const getHighestPrioritySchedule = (employeeSchedules: any[], employeeId: number): EmployeeScheduleItem | null => {
  if (!employeeSchedules || !employeeSchedules.length) return null;
  
  // Filter schedules for the given employee
  const empSchedules = employeeSchedules.filter(emp => emp.id_employee === employeeId);
  if (!empSchedules.length || !empSchedules[0].schedules || !empSchedules[0].schedules.length) return null;
  
  const schedules = empSchedules[0].schedules;
  
  // Priority: Regular schedules (type_weekday=11-18)
  const regularSchedules = schedules.filter((s: EmployeeScheduleItem) => s.type_weekday && s.type_weekday >= 11 && s.type_weekday <= 18);
  if (regularSchedules.length) {
    return {
      ...regularSchedules[0],
      id_employee_workschedule: empSchedules[0].id_employee_workschedule || null,
      checked: regularSchedules[0].flg_whole_dayoff
    };
  }
  
  // If no matching schedules, return the first one with checked property
  return {
    ...schedules[0],
    id_employee_workschedule: empSchedules[0].id_employee_workschedule || null,
    checked: schedules[0].flg_whole_dayoff
  };
};

const getBorderClasses = (dateIndex, totalDates, borderTop = false) => {
  let str = 'border-bottom'
  if(dateIndex !== 1) str += ' border-left'
  if(dateIndex === totalDates) str += ' border-right'
  if(borderTop) str += ' border-top'
  return str
}

const openEmployeeModal = async (idEmployee: Number) => {
  const employee = await mtUtils.callApi(selectOptions.reqMethod.GET, `/mst/employees/${idEmployee}`)
  if(employee) {
    mtUtils.popup(UpdateEmployeeModal, {
      data: employee,
      searchData: () => fetchWeeklySchedule()
    })
  }
}

const getMinRest = (day: DayData, idEmployee: number): string | false => {
  const employeeSlotData = day.employeeSchedules[idEmployee]
  const minRest = employeeSlotData?.min_rest

  return minRest != null ? minRest.toString() : false
}

// Initialize on component mount
onMounted(async () => {
  selectedWeekLabel.value = `${selectedDate.value.format('YYYY/MM/DD')} ~ ${selectedDate.value
    .add(6, 'day')
    .format('YYYY/MM/DD')}`
    
  // Only fetch data if clinicId is available
  if (clinicId.value) {
    await fetchWeeklySchedule()
  }
})
</script>

<template>
  <div>
    <div class="calendar-container q-px-md q-my-lg" v-if="!isLoading && weeklyData.length">
      <div class="calendar-header row items-start no-wrap">
        <div class="weekday-col">
          <div class="heading bg-grey-300 q-ba-400"></div>
          <div  
            v-for="(day, i) in weeklyData"
            :key="i"
            class="border-left border-right border-bottom flex items-center day"
            :class="{ 'off_day': day.is_off_day, 'holiday': day.type_weekday === 18 }"
          >
            <span
              class="q-pa-sm q-pt-sm flex items-center display_date"
             :class="[ 
                isSaturday(day) ? 'sat' : '', 
                isSunday(day) ? 'sun' : '',
              ]"
            >
              {{ day.display_date }}
            </span>
          </div>
        </div>
        <div class="business-hours-col">
          <div class="heading flex items-center justify-center bg-grey-300 border-top border-right border-bottom">営業時間帯</div>
          <div  
            v-for="(day, i) in weeklyData"
            :key="i"
            class="border-right border-bottom flex column text-left justify-center slot-col"
            :class="{ 'bg-accent-100': day.is_off_day }"
          >
            <div class="name">
              {{ day.business_hour_slot?.name_business_hour }}
            </div>
            <template v-if="day.business_hour_slot?.time_slots">
              <div class="flex flex-wrap">
                <div 
                  v-for="(timeSlot, slotIdx) in day.business_hour_slot.time_slots" 
                  :key="slotIdx"
                  class="flex items-center"
                >
                  枠{{ slotIdx + 1 }}
                  {{ formatTimeDisplay(timeSlot.business_time.start) }} ~
                  {{ formatTimeDisplay(timeSlot.business_time.end) }}
                </div>
              </div>
            </template>
          </div>
        </div>
        <div
          class="row flex items-center no-wrap doctor-row"
          id="doctor-row"
        >
          <div 
            v-for="(doctor, idx) in sortedDoctors" 
            :key="doctor.id_employee"
            class="col"
          >
            <div
              class="flex items-center justify-center text-center no-wrap doc-name ellipsis cursor-pointer"
              :class="[getBorderClasses(idx + 1, sortedDoctors.length, true)]"
              @click="() => {
                if(editMode) updateAllDaysSlotChecked(doctor.id_employee, !!!isAllDaysSlotChecked(doctor.id_employee))
                else openEmployeeModal(doctor.id_employee)
              }"
            >
              <MtFormCheckBox
                v-if="editMode"
                type="checkbox"
                class="update-checkbox"
                label=""
                :checked="isAllDaysSlotChecked(doctor.id_employee)"
                @update:checked="(newVal) => updateAllDaysSlotChecked(doctor.id_employee, newVal)"
              />
              <span class="ellipsis"> {{ doctor.name_display }} </span>
            </div>
            <div 
              v-for="(day, i) in weeklyData" 
              :key="i" 
               :class="[
                getBorderClasses(idx + 1, sortedDoctors.length),
                day.is_off_day ? 'off_day' : ''
              ]"
              class="schedule-col cursor-pointer"
              @click="() => {
                if(editMode) updateAllSlotChecked(day, doctor.id_employee, !!!isAllSlotChecked(day, doctor.id_employee))
                else handleScheduleClick(day, doctor.id_employee)
              }"
            >
              <template v-if="!day.is_off_day">
                <template v-if="!editMode">
                  <template v-if="day.employee_schedules">
                    <div 
                      v-if="!getHighestPrioritySchedule(day.employee_schedules, doctor.id_employee)" 
                      class="text-grey-700"
                    >
                    </div>
                    <div 
                      v-else
                      :class="{ 'text-darkred': getHighestPrioritySchedule(day.employee_schedules, doctor.id_employee)?.flg_whole_dayoff }"
                      class="schedule-item cursor-pointer"
                    >
                      <template v-if="getHighestPrioritySchedule(day.employee_schedules, doctor.id_employee)?.flg_whole_dayoff">
                        休
                      </template>
                      <template v-else>
                        {{ formatTimeDisplay(getHighestPrioritySchedule(day.employee_schedules, doctor.id_employee)?.time_workschedule_start || '') }} ~
                        {{ formatTimeDisplay(getHighestPrioritySchedule(day.employee_schedules, doctor.id_employee)?.time_workschedule_end || '') }}
                        <div
                          class="break-time text-grey-600"
                          v-if="getMinRest(day, doctor.id_employee)"
                        >
                          ({{ getMinRest(day, doctor.id_employee) }})
                        </div>
                      </template>
                    </div>
                  </template>
                </template>
                <template v-else>
                  <div 
                    class="flex column justify-center items-center cursor-pointer"
                  >
                    <div class="flex items-center">
                      <MtFormCheckBox
                        type="checkbox"
                        class="update-checkbox"
                        label=""
                        :checked="isAllSlotChecked(day, doctor.id_employee)"
                        @update:checked="(newVal) => updateAllSlotChecked(day, doctor.id_employee, newVal)"
                      />
                    </div>
                    <!-- Display time information alongside checkbox -->
                    <template v-if="day.employee_schedules">
                      <template v-if="!getHighestPrioritySchedule(day.employee_schedules, doctor.id_employee)">
                      </template>
                      <template v-else>
                        <div
                          :class="{ 'text-darkred': getHighestPrioritySchedule(day.employee_schedules, doctor.id_employee)?.flg_whole_dayoff }"
                          class="schedule-item"
                        >
                          <template v-if="getHighestPrioritySchedule(day.employee_schedules, doctor.id_employee)?.flg_whole_dayoff">
                            休
                          </template>
                          <template v-else>
                            {{ formatTimeDisplay(getHighestPrioritySchedule(day.employee_schedules, doctor.id_employee)?.time_workschedule_start || '') }} ~
                            {{ formatTimeDisplay(getHighestPrioritySchedule(day.employee_schedules, doctor.id_employee)?.time_workschedule_end || '') }}
                             <div
                              class="break-time text-grey-600"
                              v-if="getMinRest(day, doctor.id_employee)"
                            >
                              ({{ getMinRest(day, doctor.id_employee) }})
                            </div>
                          </template>
                        </div>
                      </template>
                    </template>
                  </div>
                </template>
              </template>
              <template v-else>
                <div 
                  class="text-darkred flex column no-wrap items-center justify-center text-center" 
                  style="height: 100%; width: 100%;"
                >
                  <div class="flex items-center" v-if="editMode">
                    <MtFormCheckBox
                      type="checkbox"
                      class="update-checkbox"
                      label=""
                      :checked="isAllSlotChecked(day, doctor.id_employee)"
                      @update:checked="(newVal) => updateAllSlotChecked(day, doctor.id_employee, newVal)"
                    />
                  </div>
                  <template 
                    v-if="!getHighestPrioritySchedule(day.employee_schedules, doctor.id_employee) || 
                    getHighestPrioritySchedule(day.employee_schedules, doctor.id_employee)?.flg_whole_dayoff"
                  >
                    休
                  </template>
                  <template v-else>
                    {{ formatTimeDisplay(getHighestPrioritySchedule(day.employee_schedules, doctor.id_employee)?.time_workschedule_start || '') }} ~
                    {{ formatTimeDisplay(getHighestPrioritySchedule(day.employee_schedules, doctor.id_employee)?.time_workschedule_end || '') }}
                     <div
                        class="break-time text-grey-600"
                        v-if="getMinRest(day, doctor.id_employee)"
                      >
                        ({{ getMinRest(day, doctor.id_employee) }})
                      </div>
                  </template>
                </div>
              </template>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.calendar-container {
  .calendar-header {
    .weekday-col {
      width: 10vw;
      .heading {
        font-size: clamp(1vw, 1vw, 1.5vw);
        height: clamp(2vw, 2.5vw, 3vw);
      }
      .day {
        font-size: clamp(1vw, 1vw, 1.5vw);
        height: clamp(5vw, 5.5vw, 6vw);
      }
      .holiday {
        color: #810000 !important;
        font-weight: bold;
        background-color: rgba(244, 67, 54, 0.1) !important;
      }
    }
    .business-hours-col {
      width: 25vw;
      .heading {
        font-size: clamp(1vw, 1vw, 1.5vw);
        min-height: clamp(2vw, 2.5vw, 3vw);
      }
      .slot-col {
        padding-left:clamp(.25vw, .5vw, 1vw);
        font-size: clamp(1vw, 1vw, 1.5vw);
        height: clamp(5vw, 5.5vw, 6vw);
        .name {
          margin-bottom: clamp(.25vw, .5vw, 1vw);
        }
      }
    }
    .doctor-row {
      max-width: 65vw;
      width: 100%;
      overflow-x: auto;
      .col {
        flex: 0 0 calc(100% / 8);
        max-width: calc(100% / 8);
      }
      .doc-name {
        font-size: clamp(1vw, 1vw, 1.5vw);
        height: clamp(2vw, 2.5vw, 3vw);
        max-width: 100%;
        .update-checkbox {
          margin-bottom: 0;
          margin-right: clamp(.25vw, .5vw, 1vw);
        }
      }
      .schedule-col {
        font-size: clamp(1vw, 1vw, 1.5vw);
        height: clamp(5vw, 5.5vw, 6vw);
        display: flex;
        justify-content: center;
        align-items: center;
        text-align: center;
        .schedule-item {
          font-size: clamp(1vw, 1vw, 1.5vw);
        }
      }
    }
  }
}

.off_day {
  div {
    background-color: $grey-400 !important;
  }
  .display_date {
    color: $darkRed;
  }
}
.sat {
  color: $blue !important;
}
.sun {
  color: $darkRed !important;
}
.offDay {
  background-color: $grey-400 !important;
}

.update-checkbox {
  margin-bottom: 8px;
  :deep(.q-checkbox__inner) {
    min-width: clamp(1vw, 1.2vw, 1.5vw);
    width: clamp(1vw, 1.2vw, 1.5vw);
    height: clamp(1vw, 1.2vw, 1.5vw);
    .q-checkbox__bg {
      width: 100%;
      height: 100%;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
  @media screen and (max-width: 1024px) {
    margin-bottom: 4px;
  }
}
  

.border-left { border-left: 1px solid #bdbdbd; }
.border-right { border-right: 1px solid #bdbdbd; }
.border-top { border-top: 1px solid #bdbdbd; }
.border-bottom { border-bottom: 1px solid #bdbdbd; }
</style>
