/**
 * Extracts the leading alphabetical prefix (category code) from a string.
 *
 * This function is useful for parsing codes where a category is represented
 * by one or more letters at the start of the string, followed by numbers.
 *
 * Examples:
 * - "TRA100" => "TRA"
 * - "TR9"    => "TR"
 * - "A1"     => "A"
 * - "123ABC" => "" (no leading letters)
 *
 * @param input - The input string containing the category prefix and numbers.
 * @returns The leading letters from the beginning of the string.
 */
export function extractCategoryPrefix(input: string): string {
  const match = input.match(/^([A-Za-z]+)/)
  return match?.[1] || ''
}

/**
 * Extracts the trailing sequence / counter part from a request code.
 *
 * **Intended format:** `<prefix>-<sequence>`
 * - Example: `"RQ250718-256"`  → `"256"`
 *
 * **Behavior rules:**
 * 1. If the input is `null`, `undefined`, or an empty string, returns `undefined`.
 * 2. If the input is a **number**, it is converted directly to a string.
 * 3. If the input is a **string** containing at least one hyphen (`-`), the function returns
 *    the *second* segment (index `1`) after splitting on `-`.
 * 4. If there is no second segment, the original string is returned (acts as a graceful fallback).
 *
 * @param requestCode A request code like `"RQ250718-256"` or a plain number (e.g. `256`).
 * @returns The extracted sequence portion (e.g. `"256"`), the original string if no second segment exists, or `undefined` if input is nullish/empty.
 *
 * @example
 * extractRequestSequence("RQ250718-256") // "256"
 * @example
 * extractRequestSequence(256)            // "256"
 * @example
 * extractRequestSequence("ABC-XYZ")      // "XYZ"
 * @example
 * extractRequestSequence("NOHYPHEN")     // "NOHYPHEN"
 * @example
 * extractRequestSequence("")             // undefined
 * @example
 * extractRequestSequence(null)           // undefined
 */
export function extractRequestSequence(requestCode: string | number | null | undefined): string | undefined {
  if (requestCode == null) return undefined // null or undefined
  if (requestCode === '') return undefined // empty string

  if (typeof requestCode === 'number') {
    return requestCode.toString()
  }

  const parts = requestCode.split('-')
  // Need at least two parts; second part is the sequence we want
  if (parts.length > 1 && parts[1]) {
    return parts[1]
  }

  // Fallback: return the original string (no hyphen or missing second segment)
  return requestCode
}
