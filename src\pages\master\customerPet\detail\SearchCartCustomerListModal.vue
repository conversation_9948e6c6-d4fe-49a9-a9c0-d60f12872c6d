<script setup lang="ts">
import { onMounted, ref } from 'vue'
import MtModalHeader from '@/components/MtModalHeader.vue'
import UpdateCartHeaderModal from '@/pages/cart/UpdateCartHeaderModal.vue'
import MtTable2 from '@/components/MtTable2.vue'
import mtUtils from '@/utils/mtUtils'
import useCartStore from '@/stores/carts'
import useCustomerStore from '@/stores/customers'
import { getDateByFormat, getDateToday, getDaysBefore } from '@/utils/aahUtils'
import MtFormInputDate from '@/components/form/MtFormInputDate.vue'
import { useRouter } from 'vue-router'
import { formattedPrice } from '@/utils/helper'
import MtCustomerInfoLabel from '@/components/customers/MtCustomerInfoLabel.vue'

const emits = defineEmits(['close'])
const router = useRouter()
const cartStore = useCartStore()
const customerStore = useCustomerStore()

const carts = ref([])
const props = defineProps<{
  id_customer: string
}>()
const name_customer = ref(null)

const searchData = ref({
  number_cart: null,
  this_month: false,
  last_month: false,
  code_customer: '',
  id_customer: props.id_customer,
  name_customer: '',
  date_start: getDaysBefore(7),
  date_end: getDateToday()
})

const columns = [
  {
    name: 'number_cart',
    label: '会計番号',
    field: 'number_cart',
    align: 'center',
    style: 'width: 8%'
  },
  {
    name: 'date_cart',
    label: '会計日',
    field: 'date_cart',
    align: 'center',
    style: 'width: 8%'
  },
  {
    name: 'name_customer',
    label: 'オーナー',
    field: 'name_customer',
    align: 'left',
    style: 'width: 12%'
  },
  {
    name: 'name_employee_doctor',
    label: '会計作成者',
    field: 'name_employee_doctor',
    align: 'left',
    style: 'width: 12%'
  },
  {
    name: 'total_cd_disc',
    label: '個別割引',
    field: 'total_cd_disc',
    align: 'right',
    style: 'width: 8%'
  },
  {
    name: 'total_ch_disc_notax',
    label: '全体割引',
    field: 'total_ch_disc_notax',
    align: 'right',
    style: 'width: 8%'
  },
  {
    name: 'ins_target',
    label: '保険対象額',
    field: 'ins_target',
    align: 'right',
    style: 'width: 8%'
  },
  {
    name: 'total_amount_insured',
    label: '保険負担',
    field: 'total_amount_insured',
    align: 'right',
    style: 'width: 8%'
  },
  {
    name: 'bill',
    label: '顧客請求(税込)',
    field: 'bill',
    align: 'right',
    style: 'width: 8%'
  },
  {
    name: 'flg_insure_request',
    label: '保険請求',
    field: 'flg_insure_request',
    align: 'center',
    style: 'width: 5%'
  },
  {
    name: 'flg_completed',
    label: '完了',
    field: 'flg_completed',
    align: 'center',
    style: 'width: 5%'
  },
  {
    name: 'datetime_done',
    label: '完了時刻',
    field: 'datetime_done',
    align: 'center',
    style: 'width: 10%'
  }
]

const closeModal = () => {
  emits('close')
}
const onRowClick = async (data) => {
  await router.replace({
    name: 'SearchCartListDetail',
    query: { id_cart: data.id_cart }
  })
  await mtUtils.popup(UpdateCartHeaderModal, { data })
  await router.replace({ name: 'SearchCartList' })
}
const search = async () => {
  const resp = await cartStore.fetchCartsRaw({
    number_cart: searchData.value.number_cart,
    this_month: searchData.value.this_month,
    last_month: searchData.value.last_month,
    code_customer: searchData.value.code_customer,
    date_start: searchData.value.date_start,
    date_end: searchData.value.date_end,
    id_customer: searchData.value.id_customer,
    id_sp_clinic: true
  })
	carts.value = resp
}

const moveNext = (e) => {
  const inputs = Array.from(
    e.target.form.querySelectorAll('input[type="text"]')
  )
  const index = inputs.indexOf(e.target)
  if (index === 0) {
    inputs[index + 1].focus()
  } else {
    inputs[1].blur()
    search()
  }
}

const getCustomerInfoLabelProps = (row) => {
  return {
    code: row?.code_customer,
    fullKanaName: `${row?.name_kana_family} ${row?.name_kana_first}`,
    fullName: `${row?.name_family} ${row?.name_first}`,
    colorType: row?.type_customer_color
  }
}

onMounted(async () => {
  await search()
  const customer = await customerStore.selectCustomer(props.id_customer,true)
  name_customer.value = customer.name_customer_display
})
</script>

<template>
    <MtModalHeader @closeModal="closeModal">
      <q-toolbar class="text-primary q-pa-none">
        <q-toolbar-title class="title2 bold text-grey-900">
          {{ name_customer }} 様のお会計
        </q-toolbar-title>
        <div class="row">
          <div class="col">
            <div class="flex items-center no-wrap">
              <form class="flex items-center no-wrap">
                <MtFormInputDate
                  v-model:date="searchData.date_start"
                  outlined
                  type="date"
                  label="会計日：Start"
                  autofocus
                  tabindex="1"
                  @keydown.enter="moveNext"
                />
                <MtFormInputDate
                  v-model:date="searchData.date_end"
                  outlined
                  type="date"
                  label="会計日：End"
                  tabindex="2"
                  class="q-mx-md"
                  @keydown.enter="moveNext"
                />
              </form>
            </div>
          </div>
        </div>
        <q-btn
          @click="search"
          tabindex="3"
          unelevated
          color="grey-800"
          text-color="white"
        >
          <q-icon size="20px" name="search" />検索
        </q-btn>
      </q-toolbar>
    </MtModalHeader>
    <div class="row items-center justify-between q-px-lg q-py-sm">
      <div class="body1 regular text-grey-700">
        検索結果 :<span class="q-ml-sm">{{ carts.length }}件</span>
      </div>
    </div>
    <MtTable2
      :columns="columns"
      :rows="carts"
      :show-filter="false"
      row-key="name"
      :rowsBg="true"
      flat
      :style="{ height: 'calc(100vh - 180px)' }"
    >
      <template v-slot:row="{ row }">
        <td
          class="cursor-pointer"
          v-for="(col, index) in columns"
          :key="index"
          :class="{
            flg_cancel_row: row.flg_cancel,
            flg_complete_row: row.flg_completed
          }"
          @click="onRowClick(row)"
        >
          <div v-if="col.field == 'number_cart'" auto-width key="number_cart">
            <div class="body1 regular text-grey-900 text-center">
              CT-{{ row['number_cart'].split('-')[1] }}
            </div>
          </div>
          <div v-if="col.field == 'date_cart'" auto-width key="date_cart">
            <div class="body1 regular text-grey-900 text-center">
              <span class="text-center">
                {{
                  row['date_cart']
                    ? getDateByFormat(row['date_cart'])
                    : null
                }}
              </span>
            </div>
          </div>

          <div
            v-if="col.field == 'name_customer'"
            auto-width
            key="name_customer">
            <div class="column">
              <MtCustomerInfoLabel
                v-if="row.id_customer"
                :customer="getCustomerInfoLabelProps(row.customer)"
                show-customer-code />
            </div>
          </div>

          <div
            v-if="col.field === 'name_employee_doctor'"
            auto-width
            key="name_employee_doctor">
            {{ row.name_employee_doctor }}
          </div>

          <div v-if="col.field == 'bill'" auto-width key="bill">
            <div class="body1 regular text-grey-900 text-right">
              {{ row['bill'] ? formattedPrice(row['bill']) : 0 }}
            </div>
          </div>

          <div
            v-if="col.field == 'total_cd_disc'"
            key="total_cd_disc"
            auto-width
            class="text-right">
            {{ formattedPrice(row['total_cd_disc']) }}
          </div>

          <div
            v-if="col.field == 'total_ch_disc_notax'"
            key="total_ch_disc_notax"
            auto-width
            class="text-right">
            {{ formattedPrice(row['total_ch_disc_notax']) }}
          </div>

          <div
            v-if="col.field == 'total_amount_insured'"
            auto-width
            key="total_amount_insured"
            class="text-right">
            {{ formattedPrice(row['total_amount_insured']) }}
          </div>

          <div
            v-if="col.field == 'ins_target'"
            auto-width
            key="ins_target"
            class="text-right">
            {{ formattedPrice(row['ins_target']) }}
          </div>

          <div
            v-if="col.field == 'flg_insure_request'"
            auto-width
            key="flg_insure_request"
            class="text-center">
            {{ row['flg_insure_request'] == true ? '有' : '' }}
          </div>

          <div
            v-if="col.field == 'amount_unpaid'"
            auto-width
            key="amount_unpaid"
          >
            <div class="body1 regular text-grey-900">
              {{ formattedPrice(row['amount_unpaid']) }}
            </div>
          </div>

          <div v-if="col.field == 'flg_completed'" class="text-green text-center">
            <q-icon v-if="row[col.field]" size="22px" name="check_circle" />
          </div>

          <div v-if="col.field == 'datetime_done'" class="text-center" key="datetime_done" auto-width>
            <span>
              {{ row['datetime_done'] ? getDateByFormat(row['datetime_done'], 'YYYY/MM/DD HH:mm') : null }}
            </span>
          </div>
        </td>
      </template>
    </MtTable2>
</template>

<style lang="scss" scoped>
.tableBox {
  margin-top: 20px;
}

.statusBorders {
  border-radius: 24px;
  color: $white;
}

.typeCustomerColor {
  width: 15px;
  height: 15px;
  border-radius: 50%;
}
</style>
