<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import MtModalHeader from '@/components/MtModalHeader.vue'
import MtFormRadiobtn from '@/components/form/MtFormRadiobtn.vue'
import MInputTime from '@/components/form/MInputTime.vue'
import MtInputForm from '@/components/form/MtInputForm.vue'
import OptionModal from '@/components/OptionModal.vue'

import {
  EmployeeType
} from '@/types/types'
import mtUtils from '@/utils/mtUtils'
import dayjs from 'dayjs'
import { groupBy, map } from 'lodash'

import useEmployeeStore from '@/stores/employees'
import useWorkScheduleStore from '@/stores/work-schedules'
import { UpdateMode } from '@/stores/work-schedules'

const employeeStore = useEmployeeStore()
const workScheduleStore = useWorkScheduleStore()

const emits = defineEmits(['close'])
const closeModal = () => emits('close')

type typeBulkData = {
  checked: boolean
  id_employee: number
  id_employee_workschedule: number | null
  time_workschedule_start?: string
  time_workschedule_end?: string
  flg_whole_dayoff: boolean
  date_booking_special: string
}

interface Props {
  clinicId?: number | null
  bulkData: typeBulkData[],
   onSuccess: Function
}

const props = withDefaults(defineProps<Props>(), {
  bulkData: [],
  onSuccess: () => {}
})

const isLoading = ref(false)
const employeeOptions = ref<EmployeeType[]>([])
const selectedEmployees = ref<number[]>([])
const scheduleIdsToDelete = ref<number[]>([])

// constants
const restMinuteOptions = [15, 30, 45, 60]
const startingHoursAddOptions = [7, 8, 9, 10, 11, 12, 13]
const endingHoursAddOptions = [5, 6, 7, 8, 9]

const shiftForm = reactive({
  updateMode: UpdateMode.NORMAL,
  timeStart: '',
  timeEnd: '',
  breakTime: ''
})

const disableTimeUpdate = computed(() => shiftForm.updateMode === UpdateMode.OFF)

const updateShitFormTime = (val: UpdateMode) => {
  if(val === UpdateMode.OFF) {
    shiftForm.timeStart = ''
    shiftForm.timeEnd = ''
    shiftForm.breakTime = ''
  }
}

const setEndingTime = (hour: number) => {
  if(!shiftForm.timeStart) {
    return mtUtils.alert('Please select start time first')
  }
  
  const [startDateHours, startDateMinutes] = shiftForm.timeStart.split(':')
  const currentDate = dayjs().set('hour', startDateHours).set('minute', startDateMinutes)
  const newDate = currentDate.add(hour, 'hour')

  const formattedHour = String(newDate.hour()).padStart(2, '0')
  const formattedMinute = String(newDate.minute()).padStart(2, '0')

  shiftForm.timeEnd = `${formattedHour}:${formattedMinute}`
}

const openMenu = async () => {
  const menuOptions = [
    {
      title: 'URLコピー',
      name: 'url_copy',
      isChanged: false,
      attr: { class: null, clickable: true }
    },
    {
      title: '削除',
      name: 'delete',
      isChanged: false,
      attr: { class: null, clickable: true }
    },
    {
      title: '保存せずに閉じる',
      name: 'close',
      isChanged: false,
      attr: { class: null, clickable: true }
    }
  ]

  await mtUtils.littlePopup(OptionModal, {
    options: menuOptions
  })

  const selectedOption = menuOptions.find((i) => i.isChanged == true)

  if (selectedOption) {
    if (selectedOption.name == 'url_copy') {
      const url = window.location.href
      await navigator.clipboard.writeText(url)
      mtUtils.autoCloseAlert('URLをコピーしました。')
    } else if (selectedOption.name == 'delete') {
      deleteWorkSchedules()
    } else if (selectedOption.name == 'close') {
      closeModal()
    }
  }
}

const deleteWorkSchedules = async () => {
  if (scheduleIdsToDelete.value.length === 0) {
    mtUtils.autoCloseAlert('削除するシフトがありません。')
    return
  }

  try {
    isLoading.value = true

    const confirmed = await mtUtils.confirm2({
      message: '選択したシフトを削除しますか？',
      title: '確認',
      okLabel: '削除',
      cancelBtn: true,
      cancelBtnLabel: 'キャンセル'
    })

    if (!confirmed) {
      isLoading.value = false
      return
    }

    // Delete work schedules
    await workScheduleStore.deleteWorkSchedules(scheduleIdsToDelete.value)

    props.onSuccess()

    // Close the modal
    closeModal()
  } catch (error) {
    console.error('Failed to delete schedules:', error)
    mtUtils.autoCloseAlert('スケジュールの削除に失敗しました。')
  } finally {
    isLoading.value = false
  }
}

const submit = () => {
  const { bulkData, clinicId } = props
  if(bulkData && bulkData.length && bulkData.length > 0) {
    const grouped = groupBy(bulkData, 'id_employee')

    const employeeList = map(grouped, (items, key) => ({
      id_employee: Number(key),
      employee_workschedule_list: items.map((item) => ({
        id_employee_workschedule: item.id_employee_workschedule,
        type_weekday: item.type_weekday || 99,
        time_workschedule_start: shiftForm.updateMode === UpdateMode.OFF ? '00:00:00' : (shiftForm.timeStart || item.time_workschedule_start),
        time_workschedule_end: shiftForm.updateMode === UpdateMode.OFF ? '00:00:00' : (shiftForm.timeEnd || item.time_workschedule_end),
        flg_whole_dayoff: shiftForm.updateMode === UpdateMode.OFF ? true : false,
        date_booking_special: item.date_booking_special,
        min_rest: shiftForm.breakTime || 0
      })),
    }))
    const payload = {
      id_clinic: clinicId,
      employee_list: employeeList
    }

    workScheduleStore.createOrUpdateWorkSchedules({
      id_clinic: clinicId,
      employee_list: employeeList
    }).then(() => {
      const alertMessage = shiftForm.updateMode === UpdateMode.OFF ? '休みを適用しました！' : 'シフトを更新しました！'
      props.onSuccess()
      mtUtils.autoCloseAlert(alertMessage)
      closeModal()
    })
  }
}

const setStartTime = (hour: number) => {
  shiftForm.timeStart = `${String(hour).padStart(2, '0')}:00`
}

onMounted(async () => {
  // fetch employees
  await employeeStore.fetchEmployees()
  employeeOptions.value = employeeStore.getEmployees
    .filter((emp) => emp.flg_calendar)
    .map((emp) => ({
      label: emp.name_display,
      value: emp.id_employee
    }))
  
  const { bulkData } = props
  if(bulkData && bulkData.length && bulkData.length > 0) {
    let tempEmployeeSet = new Set([])
    bulkData.forEach((data: typeBulkData) => {
      tempEmployeeSet.add(data.id_employee)
      if(data.id_employee_workschedule) scheduleIdsToDelete.value.push(data.id_employee_workschedule)
    })
    selectedEmployees.value = Array.from(tempEmployeeSet)
  }

  if(bulkData.length === 1) {
    const record = bulkData[0]
    shiftForm.timeStart = record.time_workschedule_start
    shiftForm.timeEnd = record.time_workschedule_end
    shiftForm.updateMode = record.flg_whole_dayoff ? UpdateMode.OFF: UpdateMode.NORMAL
    shiftForm.breakTime = record.min_rest
  }
})

</script>
<template>
  <q-form @submit="submit">
    <MtModalHeader @closeModal="closeModal">
      <q-toolbar-title class="text-grey-900 title2 bold"> シフト時間 </q-toolbar-title>
      <q-btn round flat @click="openMenu" class="q-mx-sm" v-if="scheduleIdsToDelete.length > 0">
        <q-icon size="xs" name="more_horiz" />
      </q-btn>
    </MtModalHeader>
    <q-card-section class="q-mt-md q-px-md content">
      <div class="row q-col-gutter-md">
        <div class="col-12">
          <q-select
            v-model="selectedEmployees"
            :options="employeeOptions"
            clearable
            readonly
            dense
            label="myVetty指名表示スタッフ"
            multiple
            use-chips
            emit-value
            map-options
          />
        </div>
      </div>
      <div class="row q-col-gutter-md q-mt-md">
        <div class="col-12">
          <MtFormRadiobtn
            :val="UpdateMode.NORMAL"
            label="出勤"
            v-model:selected="shiftForm.updateMode"
            @update:selected="updateShitFormTime"
          />
          <MtFormRadiobtn
            :val="UpdateMode.OFF"
            label="休み"
            v-model:selected="shiftForm.updateMode"
            @update:selected="updateShitFormTime"
          />
        </div>
      </div>
      <div class="row q-col-gutter-md q-mt-md" v-if="!disableTimeUpdate">
        <div class="col-12">
          <MInputTime 
            class="time-field"
            v-model="shiftForm.timeStart" 
            label="開始時間" 
            :outlined="false" 
          />
          <div class="flex justify-end text-caption q-mt-sm">
            <span 
              v-for="(hour, index) in startingHoursAddOptions" 
              :key="hour"
              class="hover-primary"
              @click="() => {
                setStartTime(hour)
              }"
            >
              <span 
                style="border-radius: 4px;"
                class="bg-grey-200 q-pa-xs q-ml-sm hour cursor-pointer"
              >
                {{ hour }}h
              </span>
            </span>
          </div>
        </div>
        <div class="col-12">
          <MInputTime 
            class="time-field"
            v-model="shiftForm.timeEnd" 
            label="終了時間" 
            :outlined="false"
          />
          <div class="flex justify-end text-caption q-mt-sm">
            <span 
              v-for="(hour, index) in endingHoursAddOptions" 
              :key="hour"
              class="hover-primary"
              @click="() => {
                setEndingTime(hour)
              }"
            >
              <span 
                style="border-radius: 4px;"
                class="bg-grey-200 q-pa-xs q-ml-sm hour cursor-pointer"
              >
                +{{ hour }}h
              </span>
            </span>
          </div>
        </div>
        <div class="col-12">
          <MtInputForm
            type="text"
            label="休憩時間 (分)"
            v-model="shiftForm.breakTime"
          />
          <div class="flex justify-end text-caption q-mt-sm">
            <span 
              v-for="(time, index) in restMinuteOptions" 
              :key="time"
              class="hover-primary"
              @click="() => {
                shiftForm.breakTime = time
              }"
            >
              <span 
                style="border-radius: 4px;"
                class="bg-grey-200 q-pa-xs q-ml-sm minute cursor-pointer"
              >
                {{ time }}m
              </span>
            </span>
          </div>
        </div>
      </div>
    </q-card-section>
    <q-card-section class="bg-white q-bt">
      <div class="text-center modal-btn">
        <q-btn outline class="bg-grey-100 text-grey-800" @click="closeModal()">
          <span>キャンセル</span>
        </q-btn>
        <q-btn unelevated color="primary" class="q-ml-md" type="submit" :loading="isLoading">
          <span>保存</span>
        </q-btn>
      </div>
    </q-card-section>
  </q-form>
</template>
<style lang="scss" scoped>
.time-field {
  padding-bottom: 0;
}
.hour, .minute {
  position: relative;
  z-index: 999;
}
</style>