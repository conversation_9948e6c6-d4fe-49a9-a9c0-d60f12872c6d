import { defineStore } from 'pinia'
import { api } from '@/boot/axios'

export const usePushNotificationStore = defineStore('push_notification', {
  state: () => ({
    pushNotifications: [],
  }),

  getters: {
    getPushNotifications: (state) => state.pushNotifications,
  },

  persist: false,

  actions: {
    fetchPushNotificationList(payload: any) {
      return new Promise((resolve, reject) => {
        api
          .get(`/push_notification`, { params: payload })
          .then((response) => {
            this.pushNotifications = response.data.data
            resolve(response)
          })
          .catch((error) => {
            reject(error)
          })
      })
    },

    updatePushNotification(id_push_notification: string, payload: any) {
      return new Promise((resolve, reject) => {
        api
          .put(`/push_notification/${id_push_notification}`, payload)
          .then((response) => {
            this.pushNotifications = response.data
            resolve(response)
          })
          .catch((error) => {
            reject(error)
          })
      })
    },

  }
})

export default usePushNotificationStore
