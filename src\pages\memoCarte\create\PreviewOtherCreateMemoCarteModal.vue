<script setup lang="ts">
import { computed } from 'vue';
import MemoCarteGrouped from '@/pages/request/detail/MemoCarteGrouped.vue';
import { dateFormat, formatDateWithTime } from '@/utils/aahUtils';
import { orderBy } from 'lodash';

const props = defineProps({
  data: {
    type: Object,
    required: true
  },
  id_pet: {
    type: String,
    required: true
  },
  id_customer: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['onCopyButtonClicked'])

const copyButtonClicked = (data: { field: string, content: string }) => {
  emit('onCopyButtonClicked', data)
}

const groupedCartes = computed(() => {
  const unorderedCartes = props.data?.reduce((acc, item) => {
    const dateInsert = dateFormat(item.date_insert)
    const dateTimeInsert = item.datetime_group_carte
    if (!acc[dateInsert]) {
      acc[dateInsert] = {
        others: {}
      }
    }
    if (!acc[dateInsert].others[dateTimeInsert]) {
      acc[dateInsert].others[dateTimeInsert] = {
        grouped_cartes: false,
        date_insert: dateInsert,
        memo_carte_list: [],
        lab_result_list: {},
        medical_condition: [],
        pet_bio: {},
      }
    }

    if (item.memo_carte) {
      acc[dateInsert].others[dateTimeInsert].memo_carte_list.push({
        ...item.memo_carte,
        group_carte: item.group_carte
      })

      if (item.memo_carte.memo_other && (item.memo_carte?.memo_sbj || item?.memo_carte?.memo_ass || item?.memo_carte?.memo_obj)) {
        acc[dateInsert].others[dateTimeInsert].grouped_cartes = true
      }
    }
    if (item.lab_result) {
      const dateTime = formatDateWithTime(
        item.lab_result.datetime_registered
      )
      if (
        !acc[dateInsert].others[dateTimeInsert].lab_result_list[
          item.lab_result.id_category2_lab
        ]
      )
        acc[dateInsert].others[dateTimeInsert].lab_result_list[
          item.lab_result.id_category2_lab
        ] = {}
      if (
        !acc[dateInsert].others[dateTimeInsert].lab_result_list[
          item.lab_result.id_category2_lab
        ][item.lab_result.id_cm_device]
      )
        acc[dateInsert].others[dateTimeInsert].lab_result_list[
          item.lab_result.id_category2_lab
        ][item.lab_result.id_cm_device] = {}
      if (
        !acc[dateInsert].others[dateTimeInsert].lab_result_list[
          item.lab_result.id_category2_lab
        ][item.lab_result.id_cm_device][dateTime]
      )
        acc[dateInsert].others[dateTimeInsert].lab_result_list[
          item.lab_result.id_category2_lab
        ][item.lab_result.id_cm_device][dateTime] = []

      acc[dateInsert].others[dateTimeInsert].lab_result_list[
        item.lab_result.id_category2_lab
      ][item.lab_result.id_cm_device][dateTime].push(item.lab_result)

      acc[dateInsert].others[dateTimeInsert].lab_result_list[
        item.lab_result.id_category2_lab
      ][item.lab_result.id_cm_device][dateTime] = orderBy(acc[dateInsert].others[dateTimeInsert].lab_result_list[
        item.lab_result.id_category2_lab
      ][item.lab_result.id_cm_device][dateTime], ['lab_set.display_order', 'lab_device.display_order', 'lab.display_order'], ['asc', 'asc', 'asc'])
    }

    if (item.pet_bio) {
      acc[dateInsert].others[dateTimeInsert].pet_bio = item.pet_bio
    }

    if (item.medical_condition) {
      acc[dateInsert].others[dateTimeInsert].medical_condition.push(item.medical_condition)
    }

    if ((acc[dateInsert].others[dateTimeInsert].memo_carte_list.length > 0 && (
      acc[dateInsert].others[dateTimeInsert].pet_bio?.id_pet_bio_info ||
      acc[dateInsert].others[dateTimeInsert].medical_condition.length > 0
    )) || item.type_carte == 2) {
      acc[dateInsert].others[dateTimeInsert].grouped_cartes = true
    }

    return acc
  }, {})

  return Object.fromEntries(
    Object.entries(unorderedCartes).sort((a, b) => new Date(b[0]) - new Date(a[0]))
  )
})

</script>
<template>
  <div class="q-mt-md">
    <div v-if="groupedCartes">
      <div class="q-mr-xs q-ml-xs" v-for="(date_insert, index) in Object.keys(groupedCartes)" :key="`section-${index}`">
        <template v-if="groupedCartes[date_insert]" v-for="dt_insert in Object.keys(groupedCartes[date_insert].others)">
          <MemoCarteGrouped
            :data="groupedCartes[date_insert].others[dt_insert]"
            :date="dt_insert"
            :id_customer="props.id_customer"
            :id_pet="props.id_pet"
            :clicked="false"
            :show_copy_button="true"
            :show_lab_result="true"
            @on-copy-button-clicked="copyButtonClicked"
          />
        </template>
      </div>
    </div>
    <p v-else class="q-pt-md q-pl-md text-grey-500">登録がありません。</p>
  </div>
</template>