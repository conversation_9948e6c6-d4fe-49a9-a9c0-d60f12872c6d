<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'

const props = defineProps({
  label: {
    type: String,
    required: true
  },
  isActive: {
    type: Boolean,
    default: false
  },
  deviceId: {
    type: String,
    required: true
  }
})

const canvas = ref<HTMLCanvasElement | null>(null)
const audioContext = ref<AudioContext | null>(null)
const analyser = ref<AnalyserNode | null>(null)
const dataArray = ref<Uint8Array | null>(null)
const animationFrame = ref<number | null>(null)
let mediaStream: MediaStream | null = null
let phase = 0

const startMicrophone = async () => {
  if (!props.deviceId) {
    return
  }

  try {
    stopMicrophone()
    
    mediaStream = await navigator.mediaDevices.getUserMedia({ 
      audio: {
        deviceId: { exact: props.deviceId },
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true
      }
    })
    
    if (audioContext.value) {
      await audioContext.value.close()
    }
    
    audioContext.value = new AudioContext()
    analyser.value = audioContext.value.createAnalyser()
    analyser.value.fftSize = 32
    dataArray.value = new Uint8Array(analyser.value.frequencyBinCount)
    
    const source = audioContext.value.createMediaStreamSource(mediaStream)
    source.connect(analyser.value)
    
    draw()
  } catch (err) {
    console.error('Error in startMicrophone:', err)
  }
}

const resizeCanvas = () => {
  if (!canvas.value) {
    return
  }
  
  const container = canvas.value.parentElement
  if (!container) {
    return
  }
  
  canvas.value.width = container.offsetWidth
  canvas.value.height = container.offsetHeight
  
  if (props.isActive) {
    draw()
  }
}

// Add resize observer
onMounted(() => {
  resizeCanvas()
  
  const observer = new ResizeObserver(resizeCanvas)
  if (canvas.value?.parentElement) {
    observer.observe(canvas.value.parentElement)
  }
  
  onBeforeUnmount(() => {
    observer.disconnect()
  })
})

const draw = () => {
  if (!canvas.value || !analyser.value || !dataArray.value) return
  
  const ctx = canvas.value.getContext('2d')
  if (!ctx) return

  const drawFrame = () => {
    if (!analyser.value || !dataArray.value || !ctx) return
    
    animationFrame.value = requestAnimationFrame(drawFrame)
    
    analyser.value.getByteFrequencyData(dataArray.value)
    
    // Clear canvas
    ctx.fillStyle = '#e8f0fe'
    ctx.fillRect(0, 0, canvas.value!.width, canvas.value!.height)
    
    const frequencies = Array.from(dataArray.value.slice(0, 8))
    const average = frequencies.reduce((a, b) => a + b, 0) / frequencies.length
    const normalizedAverage = Math.pow(average / 255, 2.5)
    
    // Wave parameters
    const minHeight = canvas.value!.height * 0.1 // Base water level 30%
    const maxHeight = canvas.value!.height * 0.99 // Max water level 90%
    const currentHeight = minHeight + ((maxHeight - minHeight) * normalizedAverage)
    
    const baseAmplitude = canvas.value!.height * 0.02 
    const maxWaveAmplitude = canvas.value!.height * 0.75 
    const currentAmplitude = baseAmplitude + ((maxWaveAmplitude - baseAmplitude) * normalizedAverage)

    const points = []
    const width = canvas.value!.width
    const height = canvas.value!.height
    
    // Generate standing wave points
    const segments = 40
    for (let i = 0; i <= segments; i++) {
      const x = (width * (i / segments))
      const baseY = height - currentHeight
      
      // Basic wave
      const wave1 = Math.sin(x / width * Math.PI * 2) * Math.cos(phase) * 2.0 
      const wave2 = Math.sin(x / width * Math.PI * 3) * Math.cos(phase * 1.2) * 1.8
      const wave3 = Math.sin(x / width * Math.PI * 4) * Math.cos(phase * 0.8) * 1.5
      
      const wave = (wave1 + wave2 + wave3) / 2.5
      
      const y = baseY + (wave * currentAmplitude)
      points.push({ x, y })
    }

    // Draw wave
    ctx.beginPath()
    ctx.moveTo(0, height)
    ctx.lineTo(points[0].x, points[0].y)
    
    // Smooth curve
    for (let i = 0; i < points.length - 1; i++) {
      const xc = (points[i].x + points[i + 1].x) / 2
      const yc = (points[i].y + points[i + 1].y) / 2
      ctx.quadraticCurveTo(points[i].x, points[i].y, xc, yc)
    }

    ctx.lineTo(points[points.length - 1].x, points[points.length - 1].y)
    ctx.lineTo(width, height)
    ctx.lineTo(0, height)
    ctx.closePath()

    const baseOpacity = 0.3
    const maxOpacity = 0.6
    const currentOpacity = baseOpacity + ((maxOpacity - baseOpacity) * normalizedAverage)
    ctx.fillStyle = `rgba(26, 115, 232, ${currentOpacity})`
    ctx.fill()

    phase += 0.05 + (normalizedAverage * 0.6)
  }
  
  drawFrame()
}

const stopMicrophone = () => {
  if (mediaStream) {
    mediaStream.getTracks().forEach(track => track.stop())
    mediaStream = null
  }
  
  if (animationFrame.value) {
    cancelAnimationFrame(animationFrame.value)
    animationFrame.value = null
  }
  
  if (canvas.value) {
    const ctx = canvas.value.getContext('2d')
    if (ctx) {
      ctx.clearRect(0, 0, canvas.value.width, canvas.value.height)
    }
  }
}

watch(
  () => props.isActive,
  (newValue) => {
    if (newValue) {
      nextTick(() => {
        resizeCanvas()
        startMicrophone()
      })
    } else {
      stopMicrophone()
    }
  }
)

watch(() => props.deviceId, async (newDeviceId) => {
  if (props.isActive && newDeviceId) {
    stopMicrophone()
    await startMicrophone()
  }
}, { immediate: true })

onBeforeUnmount(() => {
  stopMicrophone()
  if (audioContext.value) {
    audioContext.value.close()
  }
})
</script>

<template>
  <div class="mic-indicator" :class="{ 'is-active': isActive }">
    <canvas ref="canvas" class="wave-background"></canvas>
    <div class="mic-content">
      <div class="mic-icon">
        <q-icon name="mic" size="20px" />
      </div>
      <div class="mic-label">{{ label }}</div>
    </div>
  </div>
</template>
<style scoped lang="scss">
.mic-indicator {
  background-color: #f1f5f9;
  border-radius: 8px;
  padding: 5px 24px;
  width: 100%;
  min-height: 16px;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;

  &.is-active {
    background-color: transparent;
  }
}

.wave-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

.mic-content {
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
}

.mic-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 35px; 
  height: 35px; 
  background-color: #e2e8f0;
  border-radius: 50%;
  color: #64748b;

  .is-active & {
    background-color: $primary;
    color: white;
  }
}

.mic-label {
  flex: 1;
  font-size: 14px; 
  color: #1e293b;
  font-weight: 500; 
}

.wave-container {
  width: 60px;
  height: 24px;
  display: flex;
  align-items: center;
  
  canvas {
    width: 100%;
    height: 100%;
  }
}
</style>
