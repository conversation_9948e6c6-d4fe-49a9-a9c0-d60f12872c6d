<script setup lang="ts">
import { onMounted, onUnmounted, reactive, ref, computed } from 'vue'
import MtModalHeader from '@/components/MtModalHeader.vue'
import MtInputForm from '@/components/form/MtInputForm.vue'
import MtPetFilterSelect from '@/components/form/MtPetFilterSelect.vue'
import MtFormPullDown from '@/components/form/MtFormPullDown.vue'
import MtFormInputDate from '@/components/form/MtFormInputDate.vue'
import { formatDateWithTime, getDateToday } from '@/utils/aahUtils'
import useIllnessHistoryStore from '@/stores/illness-history'
import useClinicalFilesStore from '@/stores/clinical-files'
import useCustomerStore from '@/stores/customers'
import { storeToRefs } from 'pinia'
import mtUtils from '@/utils/mtUtils'
import aahMessages from '@/utils/aahMessages'
import aahValidations from '@/utils/aahValidations'
import OptionModal from '@/components/OptionModal.vue'
import MtFormMultipleSelection from '@/components/form/MtFormMultipleSelection.vue'
import { Platform, QUploader } from 'quasar'
import DoubleZoomImageModal from '@/pages/message/DoubleZoomImageModal.vue'
import { typeDiagnosticInfo, typeFile, typeProvider } from '@/utils/enum'
import DicomViewerModal from './DicomViewerModal.vue'
import { ClinicalFile } from '@/types/types'
import { convertBlobToBase64 } from '@/utils/convertBlobToBase64'
import dayjs from 'dayjs'
import useMemoCarteStore from '@/stores/memo-cartes'
import FabricMemoCarteModal from '@/pages/memoCarte/FabricMemoCarteModal.vue'
import { event_bus } from '@/utils/eventBus'
import MtPetInfo from '@/components/MtPetInfo.vue'
import SearchComparisonClinicalFileModal from '@/pages/petInfo/diagnostic/SearchComparisonClinicalFileModal.vue'
import GetClinicalFilePdf from '@/pages/petInfo/diagnostic/GetClinicalFilePdf.vue'
import UpdateInfoListModal from '@/pages/info/UpdateInfoListModal.vue'

const emits = defineEmits(['close'])
const props = withDefaults(defineProps<{
  data: object;
  onCompleteCallback: Function;
  onTitleClick: Function;
  allData: Array<any>;
  showUserInfo: boolean;
  enableTitleClickFlg: boolean;
  enableFileCompare: boolean;
  refreshFlg: boolean
}>(), {
  data: () => ({}),
  onCompleteCallback: () => {},
  onTitleClick: () => {},
  allData: () => [],
  showUserInfo: false,
  enableTitleClickFlg: false,
  enableFileCompare: true,
  refreshFlg: true
})
const illnessHistoryStore = useIllnessHistoryStore()
const clinicalFilesStore = useClinicalFilesStore()

const customerStore = useCustomerStore()
const memoCarteStore = useMemoCarteStore()

const petList: any = ref([])
const data = ref({
  id_clinical_file: null,
  id_customer: null,
  id_pet: null,
  file_path: null,
  id_pet_illness_history: [],
  type_provider: 1,
  name_file: null,
  type_file: 1,
  type_receive_format: 2,
  type_diagnostic_info: null,
  memo_file_storage: null,
  id_employee_supplier: null,
  name_supplier_other: null,
  datetime_receive: getDateToday(),
  id_clinic: null,
  flg_delete: false
})
const filePaths = ref<Array<string | ArrayBuffer | null>>([])
const multipleData = ref<ClinicalFile[]>([
  {
    id_clinical_file: null,
    id_customer: null,
    id_pet: null,
    file_path: null,
    id_pet_illness_history: [],
    type_provider: 1,
    name_file: null,
    type_file: 1,
    type_receive_format: 2,
    type_diagnostic_info: null,
    memo_file_storage: null,
    id_employee_supplier: null,
    name_supplier_other: null,
    datetime_receive: getDateToday(),
    id_clinic: null
  }
])
const currentMultipleData = ref(0)
const defaultEmployee = JSON.parse(localStorage.getItem('id_employee'))

const uploadNew = ref(false)
const doSubmit = ref(false)
const f_status = ref('unchanged')
const previewImage = ref(false)
const isDragging = ref(false)
const selectedTypeDiagnostics = ref([])
const history = ref([])
const isEdit = ref(false)
const petName = ref('')
const uploader = ref(null)
const { getCustomer } = storeToRefs(customerStore)
const petIllnessHistoryList = ref([])
const petIllnessHistoryListDefault = reactive<any>([])
const petIllnessHistorySelected = ref([])
const uploadedFileUrl = ref('')
const cfPdfConfirmationDialog = ref(false)

const mainFileVideo = ref<HTMLVideoElement | null>(null)
const comparisonVideoRef = ref<HTMLVideoElement | null>(null)

const submit = () => {
  if (f_status.value === 'unchanged') {
    delete multipleData.value[0].file_path
  }

  const payload: ClinicalFile[] = multipleData.value.map((data: ClinicalFile) => {
    const payloadData = { ...data };
    if (selectedTypeDiagnostics.value.length > 0) {
      if (typeof selectedTypeDiagnostics.value[0] === 'number') {
        payloadData.type_diagnostic_info = selectedTypeDiagnostics.value.join(',');
      } else {
        const t = selectedTypeDiagnostics.value.map((item) => item.value);
        payloadData.type_diagnostic_info = t.join(',');
      }
    } else {
      delete payloadData.type_diagnostic_info;
    }
    if (Array.isArray(payloadData.id_pet_illness_history)) {
      if (payloadData.id_pet_illness_history.length > 0) {
        payloadData.id_pet_illness_history = payloadData.id_pet_illness_history.join(',');
      }
    }
    if (payloadData.datetime_receive) {
      payloadData.datetime_receive = formatDateWithTime(payloadData.datetime_receive, 'YYYY/MM/DD HH:mm:ss');
    }

    return payloadData;
  });

  payload.forEach((data, index) => {
    if (data.id_clinical_file) {
      doSubmit.value = false;
      clinicalFilesStore
        .updateClinicalFile(data.id_clinical_file, data)
        .then(async () => {
          const actions = []
          actions.push(memoCarteStore.fetchMemoCarteV1({
            id_pet: data.id_pet,
            id_customer: data.id_pet,
          }))
          if(props.refreshFlg) {
            actions.push(clinicalFilesStore.fetchClinicalFiles({
              id_pet: data.id_pet,
            }))
          }
          await Promise.all(actions);
          if (props.onCompleteCallback) {
            props.onCompleteCallback(data);
          }
          event_bus.emit('reloadLeft');
          emits('close');
          await mtUtils.autoCloseAlert(aahMessages.success);
        })
        .finally(() => {
          doSubmit.value = true;
        });
    } else {
      doSubmit.value = false;
      data.file_index = index
      clinicalFilesStore
        .submitClinicalFile(data)
        .then(async () => {
          await Promise.all([
            memoCarteStore.fetchMemoCarteV1({
              id_pet: data.id_pet,
              id_customer: data.id_pet,
            }),
            clinicalFilesStore.fetchClinicalFiles({
              id_pet: data.id_pet,
            }),
          ]);
          if (props.onCompleteCallback) {
            props.onCompleteCallback(clinicalFilesStore.recentClinicalFile.data);
          }
          emits('close');
          await mtUtils.autoCloseAlert(aahMessages.success);
        })
        .finally(() => {
          doSubmit.value = true;
        });
    }
  });
};

const openImageViewModal = async (file, play: boolean) => {
  const files = props.allData
    ?.filter((item) => item.type_file == 1 || item.type_file == 2)
    ?.map((item) => ({ ...item, content_type: 'image' }))
  const currentIndex = files?.findIndex((item) => item.id_clinical_file === multipleData.value[0].id_clinical_file)

  await mtUtils.imageViewPopup(DoubleZoomImageModal, {
    files,
    currentIndex,
    singleImage: false,
    index: 0,
    comparison: 'clinical_file',
  })
}
const openDicomViewModal = async (file) => {
  await mtUtils.popup(DicomViewerModal, {
    dicom_url: file,
    id_clinical_file: data.value.id_clinical_file,
    id_pet: data.value.id_pet,
    persistent: true
  })
}

const handleFileUploaded = async (files: readonly File[]): Promise<void> => {
  if (files.length > 10) {
    uploader?.value?.reset()
    mtUtils.autoCloseAlert('同時アップロードは最大10点です')
    return
  }
  const promises = files.map((fileObject, index) => {
    return onFileAdded(fileObject, index)
  })
  await Promise.all(promises).then(() => {
    if(promises.length > 1) submit()
  })
}

const onFileAdded = async (fileObject: File, index: number) => {
  if (!fileObject.size) {
    return mtUtils.autoCloseAlert('アップロードするファイルを選択してください')
  }

  previewImage.value = true
  doSubmit.value = true
  f_status.value = 'changed'

  // generate multiple payload start on index 1 and above
  if (index) {
    generateMultiplePayload()
  }

  try {
    if (fileObject.type.startsWith('image/')) {
      multipleData.value[index].name_file = fileObject.name
      multipleData.value[index].file_path = fileObject
      multipleData.value[index].type_file = 1
      if (fileObject.__img) {
        const filePath = await convertBlobToBase64(fileObject.__img.src)
        filePaths.value[index] = filePath
      } else {
        const filePath = URL.createObjectURL(fileObject)
        filePaths.value[index] = filePath
      }
    } else if (fileObject.type.startsWith('video/')) {
      multipleData.value[index].name_file = fileObject.name
      multipleData.value[index].file_path = fileObject
      multipleData.value[index].type_file = 2
      const filePath = URL.createObjectURL(fileObject)
      filePaths.value[index] = filePath
    } else if (fileObject.type.includes('dicom') || fileObject.name.endsWith('.dcm')) {
      multipleData.value[index].name_file = fileObject.name
      multipleData.value[index].file_path = fileObject
      multipleData.value[index].type_file = 3
      const filePath = await URL.createObjectURL(fileObject)
      filePaths.value[index] = filePath
    } else if (
      fileObject.type.startsWith('audio/') ||
      fileObject.type.includes('/pdf') ||
      fileObject.type.includes('text/csv') ||
      fileObject.type.includes('/doc') ||
      fileObject.type.includes('/docx') ||
      fileObject.type.includes('document') ||
      fileObject.type.includes('sheet')
    ) {
      multipleData.value[index].name_file = fileObject.name
      multipleData.value[index].file_path = fileObject
      multipleData.value[index].type_file = 99
      const filePath = URL.createObjectURL(fileObject)
      filePaths.value[index] = filePath
    } else {
      mtUtils.alert('エラー', 'ファイルがサポートされていません').then(() => {
        onFileRemoved(index)
        uploader.value.reset()
      })
    }
    isDragging.value = false
  } catch (error) {
    console.error('Error processing file: ', error)
  }
}

const onFileRemoved = async (index: number, isNeedConfirmation: boolean = false) => {
  if (isNeedConfirmation) {
    await mtUtils
      .confirm('このファイルを削除してもよろしいですか？削除後に別のファイルを追加できます。', '削除の確認')
      .then((confirmation) => {
        if (!confirmation) {
          return false
        }
      })
  }
  multipleData.value[index].file_path = null
  multipleData.value[index].name_file = null
  multipleData.value[index].type_file = null
  multipleData.value.length = 0
  filePaths.value.length = 0
  previewImage.value = false
  doSubmit.value = false
  f_status.value = 'removed'
  isEdit.value = false
  uploadNew.value = true
  generateMultiplePayload()
}
const goSwitch = (direction: "next" | "previous") => {
  if (props.allData?.length) {
    const currentIndex = props.allData?.findIndex(
      (item) => item.id_clinical_file === multipleData.value[0].id_clinical_file
    )
    if (direction === 'next') {
      if (props.allData[currentIndex + 1]) init(props.allData[currentIndex + 1])
    } else if (direction === 'previous') {
      if (props.allData[currentIndex - 1]) init(props.allData[currentIndex - 1])
    }

    autoPlayMainFileVideo()
  } else if (multipleData.value?.length) {
    switchPreviewData(direction)
  }
}
const closeModal = () => {
  emits('close')
}
const setOptionsForPetList = () => {
  petList.value = getCustomer.value?.pets

  // If there is only one pet, set it as the default
  if (getCustomer.value?.pets.length === 1) {
    data.value.id_pet = getCustomer.value?.pets[0].id_pet
  }
}
const openMenu = async () => {
  let menuOptions = [
     {
      title: '印刷する',
      name: 'cfPdf',
      isChanged: false,
      attr: {
        class: null,
        clickable: true
      }
    },
    {
      title: '削除する',
      name: 'delete',
      isChanged: false,
      attr: {
        class: null,
        clickable: true
      }
    }
  ]
  await mtUtils.littlePopup(OptionModal, { options: menuOptions })

  let selectedOption = menuOptions.find((i) => i.isChanged == true)

  if (selectedOption) {
    if(selectedOption.name === 'cfPdf') {
      cfPdfConfirmationDialog.value = true
    }
    if (selectedOption.name == 'delete') {
      await mtUtils
        .confirm(aahMessages.delete_ask, aahMessages.delete)
        .then((confirmation) => {
          if (confirmation) {
            clinicalFilesStore
              .destroyClinicalFile(data.value.id_clinical_file)
              .then(async () => {
                await Promise.all([
                  memoCarteStore.fetchMemoCarteV1({
                    id_pet: data.value.id_pet
                  }),
                  clinicalFilesStore.fetchClinicalFiles({
                    id_pet: data.value.id_pet
                  }),
                  illnessHistoryStore.selectIllnessHistory(
                    data.value.id_pet_illness_history?.[0]
                  )
                ])
                if (props.onCompleteCallback) {
                  props.onCompleteCallback(
                    // clinicalFilesStore.recentClinicalFile.data
                    data.value
                  )
                }
                emits('close')
                mtUtils.autoCloseAlert(aahMessages.success)
              })
          }
        })
    }
  }
}
const init = (newData: ClinicalFile = null) => {
  petList.value = getCustomer.value?.pets
  // data.value = JSON.parse(JSON.stringify(newData ? newData : props.data))
  multipleData.value[currentMultipleData.value] = JSON.parse(
    JSON.stringify(newData ? newData : props.data)
  )

  // if image is exist from api response
  if (props.data?.file_path) {
    doSubmit.value = true
    previewImage.value = true
    isEdit.value = true
    filePaths.value[currentMultipleData.value] = props.data?.file_path
  }

  // if image is exist from api response
  if (newData?.file_path) {
    doSubmit.value = true
    previewImage.value = true
    isEdit.value = true
    filePaths.value[currentMultipleData.value] = newData?.file_path
  }

  if (props.data?.type_diagnostic_info) {
    if (props.data?.type_diagnostic_info.length > 0) {
      props.data?.type_diagnostic_info.map((item) => {
        let a = typeDiagnosticInfo.find((option) => option.value == item)
        selectedTypeDiagnostics.value.push(a)
      })
    } else {
      const type_diagnostic = typeDiagnosticInfo.find((option) => option.value == props.data?.type_diagnostic_info)
      selectedTypeDiagnostics.value.push(type_diagnostic)
    }
  }

  if(newData) {
    if (newData?.type_diagnostic_info) {
      selectedTypeDiagnostics.value.length = 0
      if (newData?.type_diagnostic_info.length > 0) {
        newData.type_diagnostic_info.map((item) => {
          let a = typeDiagnosticInfo.find((option) => option.value == item)
          selectedTypeDiagnostics.value.push(a)
        })
      } else {
        const type_diagnostic = typeDiagnosticInfo.find((option) => option.value == newData?.type_diagnostic_info)
        selectedTypeDiagnostics.value.push(type_diagnostic)
      }
    }
    else {
      selectedTypeDiagnostics.value.length = 0
    }
  }
  petName.value = props.data?.id_pet
  if (props.data?.id_pet_illness_history) {
    const id_pet_illness_history_list = [...data.value.id_pet_illness_history]
    data.value.id_pet_illness_history.length = 0
    illnessHistoryStore.getIllnessHistorys.forEach((item: any) => {
      id_pet_illness_history_list.forEach((id: string) => {
        if (id == item.id_pet_illness_history) {
          data.value.id_pet_illness_history.push(item.id_pet_illness_history)
        }
      })
    })
  }
  data.value.id_clinic = data.value.id_clinic
  if (!isEdit.value) {
    multipleData.value[currentMultipleData.value].type_file = 1
    multipleData.value[currentMultipleData.value].type_provider = 1
    multipleData.value[currentMultipleData.value].type_receive_format = 2
    multipleData.value[currentMultipleData.value].datetime_receive =
      dayjs().format('YYYY/MM/DD HH:mm:ss')
  }
}

const switchPreviewData = (direction: 'next' | 'previous') => {
  if (direction === 'next') {
    if (currentMultipleData.value !== multipleData.value.length - 1) {
      currentMultipleData.value++
    }
  }
  if (direction === 'previous') {
    if (currentMultipleData.value !== 0) {
      currentMultipleData.value--
    }
  }
}

const generateMultiplePayload = () => {
  const idPetIllnessHistory = []
  if (props.data.id_pet_illness_history) {
    const id_pet_illness_history_list = [...props.data.id_pet_illness_history]
    illnessHistoryStore.getIllnessHistorys.forEach((item: any) => {
      id_pet_illness_history_list.forEach((id: string) => {
        if (id == item.id_pet_illness_history) {
          idPetIllnessHistory.push(item.id_pet_illness_history)
        }
      })
    })
  }
  filePaths.value.push('')

  return multipleData.value.push({
    ...props.data,
    id_pet_illness_history: idPetIllnessHistory,
    datetime_receive: dayjs().format('YYYY/MM/DD HH:mm:ss')
  })
}

const onDrop = (e: DragEvent) => {
  e.preventDefault()
  const files = Array.from(e.dataTransfer?.files || [])
  if (files.length > 0) {
    handleFileUploaded(files)
  }
}

const onDragOver = (e: DragEvent) => {
  e.preventDefault()
  isDragging.value = true
}

const onDragEnter = (e: DragEvent) => {
  e.preventDefault()
  isDragging.value = true
}

const onDragLeave = (e: DragEvent) => {
  e.preventDefault()
  isDragging.value = false
}

const openEditModal = async () => {
  await mtUtils.fullHeightPopup(FabricMemoCarteModal, {
    id_memo_carte: null,
    id_customer: data.value?.id_customer,
    id_pet: data.value?.id_pet,
    isDirectSubmit: true,
    id_pet_illness_history: [illnessHistoryStore.getIllnessHistory?.id_pet_illness_history],
    imageUrl: filePaths.value[currentMultipleData.value],
    isEdit: true,
    id_clinical_file: data.value?.id_clinical_file
  })
  closeModal()
}

const updateClinicalFile = async () => {
  if (data.value.id_clinical_file) {
    data.value = {
      ...data.value,
      id_pet_illness_history: data.value.id_pet_illness_history.join(','),
      flg_delete: true
    }
    await clinicalFilesStore.updateClinicalFile(data.value.id_clinical_file, data.value)
    closeModal()
  }
}

const rightFormExpand = ref(true)
const toggleRightForm = () => {
  rightFormExpand.value = !rightFormExpand.value
}

// Image Tools Module
let defaultImageState = {
  rotation: 0,
  scale: 1,
  translateX: 0,
  translateY: 0,
  dragging: false,
  startX: 0,
  startY: 0,
  lastTouchDistance: 0,
  lastTouchX: 0,
  lastTouchY: 0,
  lastTapTime: 0
}
const imageStates = Array.from({ length: 4 }, () =>
  reactive({...defaultImageState})
);
const maxBoundary = 360;
const clamp = (value: number, min: number, max: number) => {
  return Math.min(Math.max(value, min), max)
};

const imageStyle = (index: number) => {
  const img = imageStates[index]
  return {
    transform: `rotate(${img.rotation}deg) scale(${img.scale}) translate(${img.translateX}px, ${img.translateY}px)`,
    transition: img.dragging ? "none" : "transform 0.3s ease-in-out",
    cursor: img.scale > 1 ? (img.dragging ? 'grabbing' : 'grab') : 'zoom-in'
  }
}

const rotateImage = (index: number) => {
  imageStates[index].rotation += 90
}

const onClickZoomImage = (index: number) => {
  const img = imageStates[index]
  if (img.scale === 1) {
    img.scale = 3;
  }
}

const zoomImage = (index: number, event: WheelEvent) => {
  event.preventDefault();
  const img = imageStates[index]
  let zoomFactor = event.deltaY < 0 ? 0.5 : -0.5;
  let newScale = clamp(img.scale + zoomFactor, 1, 4); // Limit zoom between 1x and 4x
  img.scale = newScale;

  // Reset panning if zoom is at 1
  if (img.scale === 1) {
    resetPosition(index);
  }

  applyBoundaries(index);
};

const applyBoundaries = (index: number) => {
  const img = imageStates[index]
  img.translateX = clamp(img.translateX, -maxBoundary, maxBoundary);
  img.translateY = clamp(img.translateY, -maxBoundary, maxBoundary);
};

const startPan = (index: number, event: MouseEvent) => {
  const img = imageStates[index]
  if (img.scale > 1) {
    img.dragging = true;
    img.startX = event.clientX - img.translateX;
    img.startY = event.clientY - img.translateY;
    document.addEventListener("mousemove", (e) => panImage(index, e));
    document.addEventListener("mouseup", () => stopPan(index));
  }
};
const panImage = (index: number, event: MouseEvent) => {
  const img = imageStates[index]
  if (img.dragging) {
    img.translateX = clamp(event.clientX - img.startX, -maxBoundary, maxBoundary);
    img.translateY = clamp(event.clientY - img.startY, -maxBoundary, maxBoundary);
  }
};
const stopPan = (index: number) => {
  const img = imageStates[index]
  img.dragging = false;
  document.removeEventListener("mousemove", (e) => panImage(index, e));
  document.removeEventListener("mouseup", () => stopPan(index));
};

const resetZoom = (index: number) => {
  imageStates[index].scale = 1;
  resetPosition(index);
};
const resetPosition = (index: number) => {
  const img = imageStates[index]
  img.translateX = 0;
  img.translateY = 0;
};

const retryKey = ref(0)
let retryCount = 0
const maxRetries = 3

function onImageError() {
  if (retryCount < maxRetries) {
    retryCount++
    retryKey.value++ // Triggers q-img to re-render
    console.warn(`Retrying image load... Attempt #${retryCount}`)
  } else {
    console.error('Image failed to load after max retries')
  }
}

function onImageLoad() {
  retryCount = 0 // Reset retry counter if load succeeds
}

// Touch Gestures (Pinch Zoom & Dragging)
const DOUBLE_TAP_DELAY = 300;
const touchStart = (index: number, event: TouchEvent) => {
  const img = imageStates[index]
  if (event.touches.length === 1) {
    const currentTime = new Date().getTime();
    const timeDiff = currentTime - (img.lastTapTime || 0);

    if (timeDiff < DOUBLE_TAP_DELAY) {
      // Double tap detected
      resetPosition(index);
      img.lastTapTime = 0;
      return;
    }

    img.lastTapTime = currentTime;

    if (img.scale > 1) {
      img.lastTouchX = event.touches[0].clientX - img.translateX;
      img.lastTouchY = event.touches[0].clientY - img.translateY;
    }
  } else if (event.touches.length === 2) {
    img.lastTouchDistance = getTouchDistance(event.touches);
  }
};
const touchMove = (index: number, event: TouchEvent) => {
  const img = imageStates[index]
  if (event.touches.length === 2) {
    const newDistance = getTouchDistance(event.touches);
    const zoomFactor = (newDistance - img.lastTouchDistance) * 0.005;
    let newScale = Math.min(Math.max(img.scale + zoomFactor, 1), 4);
    img.scale = newScale;
    img.lastTouchDistance = newDistance;

    if (img.scale === 1) {
      resetPosition(index);
    }

    applyBoundaries(index);
  } else if (event.touches.length === 1 && img.scale > 1) {
    img.translateX = clamp(event.touches[0].clientX - img.lastTouchX, -maxBoundary, maxBoundary);
    img.translateY = clamp(event.touches[0].clientY - img.lastTouchY, -maxBoundary, maxBoundary);
  }
};
const touchEnd = (index: number) => {
  imageStates[index].lastTouchDistance = 0;
  imageStates[index].lastTapTime = 0;
};

const getTouchDistance = (touches) => {
  const dx = touches[0].clientX - touches[1].clientX;
  const dy = touches[0].clientY - touches[1].clientY;
  return Math.sqrt(dx * dx + dy * dy);
};

// Comparison Module
const isCompareView = ref(false)
const is4SlidesCompareView = ref(false)
const toggleCompareView = () => {
  isCompareView.value = !isCompareView.value
}

const comparisonFiles = ref()
const fourViewModeComparionsFiles = ref()
const fourViewModeStartIndex = ref(0)
const comparisonIdx = ref(0)
const comparisonSrc = ref('')

const show4slidesView = computed(() => {
  return isCompareView.value && comparisonFiles.value.filter((v) => v.type_file === 1).length > 3
})

const onCompareFile = async (isComparing: boolean) => {
  if (isComparing) {
    clinicalFilesStore.resetSelectedClinicalFileComparison()
    await mtUtils.popup(SearchComparisonClinicalFileModal, {})
    if (clinicalFilesStore.getSelectedClinicalFileComparison.length > 0) {
      comparisonFiles.value = clinicalFilesStore.getSelectedClinicalFileComparison
      fourViewModeComparionsFiles.value = [...comparisonFiles.value].sort((a, b) => 
        new Date(b.datetime_receive).getTime()  - new Date(a.datetime_receive).getTime()
      )
      toggleCompareView()
  
      comparisonIdx.value = 0
      comparisonSrc.value = comparisonFiles.value?.[0]
      autoPlayComparisonVideo()
    }
  } else {
    toggleCompareView()
  }
}
const comparisonNavigate = (direction: 'next' | 'previous') => {
  comparisonSrc.value = ''

  if (direction === 'next') {
    comparisonIdx.value = (comparisonIdx.value + 1) % comparisonFiles.value.length;
  } else {
    comparisonIdx.value = 
      (comparisonIdx.value - 1 + comparisonFiles.value.length) % comparisonFiles.value.length;
  }

  comparisonSrc.value = comparisonFiles.value[comparisonIdx.value]
  autoPlayComparisonVideo()
}

const autoPlayMainFileVideo = () => {
  setTimeout(() => {
    if(
      multipleData.value?.[currentMultipleData.value]?.type_file == 2 &&
      mainFileVideo.value
    ) {
        mainFileVideo.value.play()
      }
  }, 1000)
}
const autoPlayComparisonVideo = () => {
  setTimeout(() => {
    if(
      comparisonSrc.value?.type_file === 2 &&
      comparisonVideoRef.value
    ) {
        comparisonVideoRef.value.play()
      }
  }, 1000)
}

const openPdfInNewTab = (url: string) => {
  window.open(url, '_blank')
}

const toggle4SlidesView = () => {
  for(const imageState of imageStates) {
    Object.assign(imageState, defaultImageState)
  }
  is4SlidesCompareView.value = !is4SlidesCompareView.value
}

const generateClinicalFilePdf = (mode: 'download' | 'sendMyVetty') => {
  let clinicalFilesData = []
  if(is4SlidesCompareView.value) {
    clinicalFilesData = fourViewModeComparionsFiles.value.slice(0, 4)
      .map((el: ClinicalFile, idx) => ({ id_clinical_file: el.id_clinical_file, datetime_receive: el.datetime_receive, style: imageStyle(idx) }))
  } else if(isCompareView.value) {
    clinicalFilesData = [
      { id_clinical_file: multipleData.value[currentMultipleData.value].id_clinical_file, datetime_receive: multipleData.value[currentMultipleData.value].datetime_receive, style: imageStyle(0) },
      { id_clinical_file: comparisonFiles.value[comparisonIdx.value].id_clinical_file, datetime_receive: comparisonFiles.value[comparisonIdx.value].datetime_receive, style: imageStyle(1) }
    ]
  } else {
    clinicalFilesData = [{ 
      id_clinical_file: multipleData.value[currentMultipleData.value].id_clinical_file, 
      datetime_receive: multipleData.value[currentMultipleData.value].datetime_receive, 
      style: imageStyle(0) 
    }]
  }
  const props = {
    idPet: data.value.id_pet,
    clinicalFilesData
  }
  if(mode === 'sendMyVetty') {
    props.callback = fillPdfToPPs
  }
  mtUtils.pdfRender(GetClinicalFilePdf, props)
}

const fillPdfToPPs = (pdfBlob: Blob, pdfFileName: string = '') => {
  const name_customer = getCustomer.value.name_customer_display
  const message_content = `${name_customer} 様<br><br>検査結果を以下に添付させていただきます。<br>ご確認をお願いいたします。 `
  mtUtils.popup(UpdateInfoListModal, {
    predefinedFile: pdfBlob,
    predefinedMessage: message_content,
    customerObj: getCustomer.value,
    disablePetAndCustomerSelect: true
  })
}

onMounted(async () => {
  if (props.data.id_clinical_file) {
    data.value = props.data
    isEdit.value = true
    rightFormExpand.value = false
  } else {
    uploadNew.value = true
    data.value.id_clinic = JSON.parse(JSON.stringify(props.data.id_clinic))
  }
  init()
  event_bus.on('updateClinicalFile', async () => { await updateClinicalFile() })
  document.addEventListener("mouseup", () => stopPan(0));
  
  autoPlayMainFileVideo()
})

onUnmounted(() => {
  event_bus.off('updateClinicalFile')
  document.removeEventListener("mouseup", () => stopPan(0));
})
</script>
<template>
  <section
    class="column bg-black full-height clinical-files"
    :class="{ 'drag-over': isDragging }"
    @dragenter.prevent="onDragEnter"
    @dragleave.prevent="onDragLeave"
    @dragover.prevent="onDragOver"
    @drop.prevent="onDrop"
  >
    <q-icon
      v-show="isDragging"
      class="upload-icon"
      size="xl"
      name="cloud_upload"
    />
    <MtModalHeader class="col-auto" style="display: none" @closeModal="closeModal">
      <q-toolbar-title class="text-grey-900 title2 bold q-pa-none">
        関連資料
      </q-toolbar-title>
      <MtPetFilterSelect
        v-model:selecting="petName"
        :pet-list="petList"
        label="ペット名"
        class="q-mx-md"
        readonly
      />
      <q-btn v-if="isEdit" flat round @click="openMenu" class="q-mx-md">
        <q-icon size="xs" name="more_horiz" />
      </q-btn>
      <q-btn v-if="isEdit" flat round @click="openEditModal" class="q-mx-md">
        <q-icon size="xs" name="edit" />
      </q-btn>
      
    </MtModalHeader>

    <q-card-section v-if="!isDragging" class="col row gap-4 full-height text-white q-pa-none relative-position">
      <!-- Pet Info and modal menu / close -->
      <div
        class="row justify-between items-start q-pa-sm absolute-top"
        style="z-index: 2;"
      >
        <section class="col flex">
          <q-card
            style="
              background: rgba(255, 255, 255, 0.65);
              backdrop-filter: blur(4px);
              -webkit-backdrop-filter: blur(4px);
              border-radius: 8px;
            "
          >
            <q-card-section class="q-pa-sm" style="opacity: 1">
              <MtPetInfo :enable-title-click-flg="props.enableTitleClickFlg" :on-title-click="props.onTitleClick" version="v2" class="ellipsis full-width" />
            </q-card-section>
          </q-card>
        </section>
        <section class="col-auto text-right">
          <q-btn
            v-if="isEdit && Platform.is.ipad && filePaths[currentMultipleData]?.includes('.pdf')"
            flat
            round
            @click="openPdfInNewTab(filePaths[currentMultipleData])"
          >
            <q-icon size="xs" name="picture_as_pdf" />
          </q-btn>
          <q-btn 
            v-if="show4slidesView"
            class="q-ml-md" 
            @click="toggle4SlidesView"
          >
            <q-icon name="looks_4" />
          </q-btn>
          <q-btn
            v-if="isEdit"
            class="q-mx-md"
            flat
            round
            @click="openMenu"
          >
            <q-icon size="xs" color="white" name="more_horiz" />
          </q-btn>
          <q-btn
            flat
            round
            @click="closeModal"
          >
            <q-icon size="xs" color="white" name="close" />
          </q-btn>
        </section>
      </div>
      <!-- Right Side Form -->
      <Transition v-if="!isCompareView" name="fade-slide" mode="out-in">
        <div
          class="absolute-right text-white flex q-px-sm"
          style="
            top: 76px;
            z-index: 2;
          "
          :style="{
            width: rightFormExpand ? '320px' : 'max-content',
            bottom: rightFormExpand ? '8px' : 'unset'
          }"
        >
          <q-card
            v-if="rightFormExpand"
            class="col"
            style="
              background: rgba(255, 255, 255, 0.65);
              backdrop-filter: blur(4px);
              -webkit-backdrop-filter: blur(4px);
              border-radius: 8px;
            "
          >
            <q-card-section class="q-pa-sm text-black full-height column" style="opacity: 1">
              <q-btn
                class="q-mb-md self-start"
                color="white"
                text-color="black"
                size="sm"
                round
                @click="toggleRightForm"
              >
                <q-icon size="xs" name="arrow_forward_ios" />
              </q-btn>
              <q-scroll-area class="col full-height card-form__scroll">
                <div class="col-auto q-mb-sm" style="max-width: 288px">
                  <MtFormInputDate
                    label="受領日時"
                    v-model:date="
                      multipleData[currentMultipleData].datetime_receive
                    "
                  />
                </div>
                <div class="col-auto q-mb-sm" style="max-width: 288px">
                  <MtFormPullDown
                    label="ファイル区分"
                    v-model:selected="multipleData[currentMultipleData].type_file"
                    :options="typeFile"
                  />
                </div>
                <div class="col-auto q-mb-sm" style="max-width: 288px">
                  <MtFormPullDown
                    label="情報元区分"
                    v-model:selected="
                      multipleData[currentMultipleData].type_provider
                    "
                    :options="typeProvider"
                  />
                </div>
                <div class="col-auto q-mb-sm" style="max-width: 288px">
                  <MtFormMultipleSelection
                    label="現疾患・既往歴"
                    v-model="
                      multipleData[currentMultipleData].id_pet_illness_history
                    "
                    required
                    show-quick-illness-history
                    :options="illnessHistoryStore.getAllIllnessHistorys"
                    :rules="[aahValidations.validationRequired]"
                  />
                </div>
                <div class="col-auto q-mb-sm" style="max-width: 288px">
                  <MtFormMultipleSelection
                    label="臨床データ区分（複数可）"
                    v-model="selectedTypeDiagnostics"
                    required
                    :options="typeDiagnosticInfo"
                    option-label="label"
                    option-value="value"
                    :storeLabel="true"
                  />
                </div>
                <q-scroll-area class="col full-height column column-scroll__container q-mb-sm" style="max-width: 288px">
                  <MtInputForm
                    v-model="multipleData[currentMultipleData].memo_file_storage"
                    type="text"
                    label="臨床ファイルメモ"
                    autogrow
                    hide-bottom-space
                  />
                </q-scroll-area>
                <div class="col-auto row justify-end" style="max-width: 288px">
                  <q-btn
                    v-if="isEdit && multipleData[currentMultipleData].type_file == 1"
                    class="q-mr-auto"
                    flat
                    @click="openEditModal"
                  >
                    <q-icon size="xs" name="edit" class="q-mr-xs" />
                    書き込み
                  </q-btn>
                  <q-btn
                    color="primary"
                    unelevated
                    :disable="!doSubmit"
                    @click="submit"
                  >
                    <span>保存</span>
                  </q-btn>
                </div>
              </q-scroll-area>
            </q-card-section>
          </q-card>
          <section
            v-else
            class="q-pa-sm column full-width"
          >
            <q-btn
              class="self-end"
              color="white"
              text-color="black"
              size="sm"
              round
              @click="toggleRightForm"
            >
              <q-icon size="xs" name="arrow_forward_ios" style="transform: rotate(180deg)" />
            </q-btn>
          </section>
        </div>
      </Transition>
      <!-- Bottom File Nav / Tools -->
      <div
        v-if="previewImage && !isCompareView"
        class="absolute-bottom-right flex justify-end q-pa-sm"
        style="
          width: 320px;
          left: 50%;
          transform: translate(-50%, 0);
          z-index: 5;
        "
      >
        <div
          class="col-3 col-md-2 q-pa-sm row justify-center items-center"
          style="
            width: 100%;
            background: rgba(255, 255, 255, 0.65);
            backdrop-filter: blur(4px);
            border-radius: 50px;
          "
        >
          <q-btn
            v-if="(props.allData && props.allData?.length > 1) || (multipleData && multipleData?.length > 1)"
            class="q-mr-sm"
            color="white"
            text-color="black"
            size="sm"
            round
            @click="goSwitch('previous')"
          >
            <q-icon size="xs" name="play_arrow" style="transform: scaleX(-1)" />
          </q-btn>
          <q-btn
            v-if="multipleData[currentMultipleData].type_file == 1"
            class="q-mr-sm"
            color="white"
            text-color="black"
            round
            @click="rotateImage(0)"
          >
            <q-icon size="xs" name="rotate_right" />
          </q-btn>
          <q-btn
            class="q-mr-sm"
            color="white"
            text-color="black"
            round
            @click="onCompareFile(true)"
            v-if="props.enableFileCompare"
          >
            <q-icon size="xs" name="compare" />
          </q-btn>
          <q-btn
            class="q-mr-sm"
            color="white"
            text-color="red"
            round
            @click="onFileRemoved(currentMultipleData, true)"
          >
            <q-icon size="xs" name="delete" />
          </q-btn>
          <q-btn
            v-if="(props.allData && props.allData?.length > 1) || (multipleData && multipleData?.length > 1)"
            color="white"
            text-color="black"
            size="sm"
            round
            @click="goSwitch('next')"
          >
            <q-icon size="xs" name="play_arrow" />
          </q-btn>
        </div>
      </div>
      <!-- Clinical File Content -->
      <q-uploader
        v-if="!previewImage"
        v-model="data.file_path"
        label="ファイルを選択する"
        color="primary"
        ref="uploader"
        class="text-center q-mx-auto self-center hide-preview"
        flat
        square
        rounded
        multiple
        @added="handleFileUploaded"
        @removed="onFileRemoved(currentMultipleData, true)"
      />
      <section
        v-if="previewImage"
        class="col"
        :class="{'self-end row': isCompareView }"
        :style="{
          height: isCompareView ? 'calc(100% - 76px)' : '100%'
        }"
      >
        <div v-if="is4SlidesCompareView" class="slides-4-comparison-view">
          <div
            v-for="(_, idx) in 4"
            class="col row img-wrapper"
            @click="onClickZoomImage(idx)"
            @wheel="(e) => zoomImage(idx, e)"
            @mousedown="(e) => startPan(idx, e)"
            @dblclick="() => resetZoom(idx)"
            @touchstart="(e) => touchStart(idx, e)"
            @touchmove="(e) => touchMove(idx, e)"
            @touchend="() => touchEnd(idx)"
            :key="idx"
            style="width: 100%; height: 100%;"
          >
            <div class="file-receive-date absolute flex justify-center items-center text-center q-ma-md q-pa-sm">
              {{ fourViewModeComparionsFiles[(fourViewModeStartIndex + idx) % fourViewModeComparionsFiles.length]?.datetime_receive?.slice(0, 10) }}
            </div>
            <q-img
              :src="fourViewModeComparionsFiles[(fourViewModeStartIndex + idx) % fourViewModeComparionsFiles.length].file_path"
              spinner-color="white"
              fit="contain"
              class="col"
              :style="{
                ...imageStyle(idx),
                width: '100%',
                height: '100%',
                objectFit: 'contain',
                position: 'relative'
              }"
            >
            </q-img>
            <q-btn
              class="cursor-pointer"
              style="position: absolute; left: 50%; bottom: 20px; transform: translateX(-50%);"
              color="white"
              text-color="black"
              size="sm"
              round
              @click.stop="rotateImage(idx)"
            >
              <q-icon size="xs" name="rotate_right" />
            </q-btn>
          </div>
          <div
            v-if="
              fourViewModeComparionsFiles?.length > 4
            "
            class="absolute-bottom flex justify-center items-center q-pa-sm"
            style="
              width: 150px;
              left: 50%;
              transform: translate(-50%, -20px);
              background: rgba(255, 255, 255, 0.65);
              backdrop-filter: blur(4px);
              border-radius: 50px;
            "
          >
            <q-btn
              class="q-mr-md"
              color="white"
              text-color="black"
              size="sm"
              round
              @click="() => {
                if(fourViewModeStartIndex === 0) fourViewModeStartIndex = fourViewModeComparionsFiles.length - 1
                else fourViewModeStartIndex--
              }"
            >
              <q-icon size="xs" name="play_arrow" style="transform: scaleX(-1)" />
            </q-btn>
            <q-btn
              color="white"
              text-color="black"
              size="sm"
              round
              @click="fourViewModeStartIndex++"
            >
              <q-icon size="xs" name="play_arrow" />
            </q-btn>
          </div>
        </div>
        <div
          v-if="!is4SlidesCompareView"
          id="left-file__compare"
          class="col full-height row justify-center items-center relative-position"
          :class="{
            'q-mx-auto': isCompareView
          }"
        >
          <div
            v-if="multipleData[currentMultipleData].type_file == 1"
            ref="mainFileImageRef"
            class="row img-wrapper"
            :class="{
              'q-mx-auto': !isCompareView,
              'col': isCompareView || imageStates[0].scale > 1
            }"
            @click="onClickZoomImage(0)"
            @wheel="(e) => zoomImage(0, e)"
            @mousedown="(e) => startPan(0, e)"
            @dblclick="() => resetZoom(0)"
            @touchstart="(e) => touchStart(0, e)"
            @touchmove="(e) => touchMove(0, e)"
            @touchend="() => touchEnd(0)"
          >
            <div v-if="isCompareView" class="file-receive-date absolute flex justify-center items-center text-center q-ma-md q-pa-sm">
              {{ multipleData[currentMultipleData]?.datetime_receive?.slice(0, 10) }}
            </div>
            <q-img
              :key="retryKey"
              :src="filePaths[currentMultipleData]"
              :ratio="4/3"
              spinner-color="white"
              fit="contain"
              class="col"
              :style="imageStyle(0)"
              @error="onImageError"
              @load="onImageLoad"
            />
          </div>
          <video
            v-else-if="multipleData[currentMultipleData].type_file == 2"
            ref="mainFileVideo"
            :src="filePaths[currentMultipleData]"
            style="
              max-width: 100%;
              position: absolute;
            "
            :style="{
              height: isCompareView ? '100%' : '88%',
              bottom: isCompareView ? '0' : 'unset'
            }"
            controls
            playsinline
            muted
          />
          <iframe
            v-else-if="multipleData[currentMultipleData].type_file === 99"
            :src="(() => {
              if(Platform.is.ipad && filePaths[currentMultipleData]?.includes('.pdf')) {
                return `https://docs.google.com/gview?embedded=true&url=${encodeURIComponent(filePaths[currentMultipleData])}`
              } else {
                return filePaths[currentMultipleData]
              }
            })()"
            style="
              position: absolute;
              bottom: 0;
            "
            :style="{
              height: isCompareView ? '100%' : '88%',
              width: isCompareView ? '100%' : '60%'
            }"
            frameborder="0"
          ></iframe>
          <q-img
            v-else-if="
              (typeof multipleData[currentMultipleData].file_path ==
                'string' &&
                multipleData[currentMultipleData].file_path.includes(
                  '.mp3'
                )) ||
              (typeof multipleData[currentMultipleData].file_path ==
                'string' &&
                multipleData[currentMultipleData].file_path.includes(
                  '.wav'
                ))
            "
            :src="'/src/assets/img/clinicalFiles/audio.png'"
            spinner-color="white"
            class="cursor-pointer"
          />
          <q-img
            v-else-if="multipleData[currentMultipleData].type_file == 3"
            @click="
              openDicomViewModal(
                multipleData[currentMultipleData].file_path
              )
            "
            :src="multipleData[currentMultipleData].thumbnail_path"
            spinner-color="white"
            class="cursor-pointer"
          />
          <q-img
            v-else
            :src="'/src/assets/img/clinicalFiles/file.png'"
            spinner-color="white"
            class="cursor-pointer"
          />
          <div
            v-if="isCompareView"
            class="absolute-bottom flex justify-end q-pa-sm"
            style="
              width: 240px;
              left: 50%;
              bottom: 8px;
              transform: translate(-50%, 0);
              z-index: 5;
            "
          >
            <div
              class="col-3 col-md-2 q-pa-sm row justify-center items-center"
              style="
                width: 100%;
                background: rgba(255, 255, 255, 0.65);
                backdrop-filter: blur(4px);
                border-radius: 50px;
              "
            >
              <q-btn
                v-if="props.allData && props.allData?.length > 1"
                class="q-mr-sm"
                color="white"
                text-color="black"
                size="sm"
                round
                @click="goSwitch('previous')"
              >
                <q-icon size="xs" name="play_arrow" style="transform: scaleX(-1)" />
              </q-btn>
              <q-btn
                v-if="multipleData[currentMultipleData].type_file == 1"
                class="q-mr-sm"
                color="white"
                text-color="black"
                round
                @click="rotateImage(0)"
              >
                <q-icon size="xs" name="rotate_right" />
              </q-btn>
              <q-btn
                class="q-mr-sm"
                color="white"
                text-color="black"
                round
                @click="onCompareFile(false)"
              >
                <q-icon size="xs" name="pageview" />
              </q-btn>
              <q-btn
                v-if="props.allData && props.allData?.length > 1"
                color="white"
                text-color="black"
                size="sm"
                round
                @click="goSwitch('next')"
              >
                <q-icon size="xs" name="play_arrow" />
              </q-btn>
            </div>
          </div>
        </div>
        <div v-if="isCompareView && !is4SlidesCompareView" id="right-file__compare" class="col full-height row justify-center items-center relative-position q-ml-xs">
          <div
            v-if="comparisonSrc?.type_file == 1"
            class="col row img-wrapper"
            @click="onClickZoomImage(1)"
            @wheel="(e) => zoomImage(1, e)"
            @mousedown="(e) => startPan(1, e)"
            @dblclick="() => resetZoom(1)"
            @touchstart="(e) => touchStart(1, e)"
            @touchmove="(e) => touchMove(1, e)"
            @touchend="() => touchEnd(1)"
          >
            <div class="file-receive-date absolute flex justify-center items-center text-center q-ma-md q-pa-sm">
              {{ comparisonSrc?.datetime_receive?.slice(0, 10) }}
            </div>
            <q-img
              :src="comparisonSrc?.file_path"
              :ratio="4/3"
              spinner-color="white"
              fit="contain"
              class="col"
              :style="imageStyle(1)"
            />
          </div>
          <video
            v-else-if="comparisonSrc?.type_file == 2"
            ref="comparisonVideoRef"
            :src="comparisonSrc?.file_path"
            style="
              max-width: 100%;
              position: absolute;
            "
            controls
            playsinline
            muted
          />
          <iframe
            v-else-if="comparisonSrc?.type_file === 99"
            :src="comparisonSrc?.file_path"
            style="
              width: 100%;
              height: 100%;
              position: absolute;
              bottom: 0;
            "
            frameborder="0"
          ></iframe>
          <q-img
            v-else-if="
              (typeof comparisonSrc?.file_path ==
                'string' &&
                comparisonSrc?.file_path.includes(
                  '.mp3'
                )) ||
              (typeof comparisonSrc?.file_path ==
                'string' &&
                comparisonSrc?.file_path.includes(
                  '.wav'
                ))
            "
            :src="'/src/assets/img/clinicalFiles/audio.png'"
            spinner-color="white"
            class="cursor-pointer"
          />
          <q-img
            v-else-if="comparisonSrc?.type_file == 3"
            @click="
              openDicomViewModal(
                comparisonSrc?.file_path
              )
            "
            :src="comparisonSrc?.thumbnail_path"
            spinner-color="white"
            class="cursor-pointer"
          />
          <q-img
            v-else
            :src="'/src/assets/img/clinicalFiles/file.png'"
            spinner-color="white"
            class="cursor-pointer"
          />
          <div
            class="absolute-bottom flex justify-end q-pa-sm"
            style="
              width: 240px;
              left: 50%;
              bottom: 8px;
              transform: translate(-50%, 0);
              z-index: 5;
            "
          >
            <div
              v-if="
                comparisonFiles?.length > 1 || comparisonSrc?.type_file === 1
              "
              class="col-3 col-md-2 q-pa-sm row justify-center items-center gap-2"
              style="
                width: 100%;
                background: rgba(255, 255, 255, 0.65);
                backdrop-filter: blur(4px);
                border-radius: 50px;
              "
            >
              <q-btn
                v-if="comparisonFiles?.length > 1"
                class="q-mr-sm"
                color="white"
                text-color="black"
                size="sm"
                round
                @click="comparisonNavigate('previous')"
              >
                <q-icon size="xs" name="play_arrow" style="transform: scaleX(-1)" />
              </q-btn>
              <q-btn
                v-if="comparisonSrc?.type_file == 1"
                class="q-mr-sm"
                color="white"
                text-color="black"
                round
                @click="rotateImage(1)"
              >
                <q-icon size="xs" name="rotate_right" />
              </q-btn>
              <q-btn
                v-if="comparisonFiles?.length > 1"
                color="white"
                text-color="black"
                size="sm"
                round
                @click="comparisonNavigate('next')"
              >
                <q-icon size="xs" name="play_arrow" />
              </q-btn>
            </div>
          </div>
        </div>
      </section>
    </q-card-section>
  </section>
  <q-dialog v-model="cfPdfConfirmationDialog">
    <q-card class="q-pa-lg">
      <q-card-actions class="justify-end">
        <q-btn
          label="キャンセル"
          text-color="primary"
          outline
          color="white"
          @click="pdfConfirmationDialog = false"
          v-close-popup
        />
        <q-btn
          label="PDFダウンロード"
          color="primary"
          @click="generateClinicalFilePdf('download')"
          v-close-popup
        />
        <q-btn
          label="myVetty送信"
          color="primary"
          class="no-uppercase"
          @click="generateClinicalFilePdf('sendMyVetty')"
          v-close-popup
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>
<style lang="scss" scoped>
.upload-section {
  border: 1px dotted $grey-500;
  padding: 0;
  height: auto;
  cursor: pointer;
}

.rpd {
  width: 100%;
}

.rpd > img {
  width: 100%;
}

.q-uploader.hide-preview :deep(.q-uploader__list) {
  display: none;
}

.preview-button-prev {
  position: absolute;
  bottom: 50%;
  left: 12px;
  color: $white;
  background-color: $black;
  opacity: 0.4;
  z-index: 2;
}
.preview-button-next {
  position: absolute;
  bottom: 50%;
  right: 12px;
  color: $white;
  background-color: $black;
  opacity: 0.4;
  z-index: 2;
}
.clinical-files {
  border: 2px dashed transparent;
  transition: border-color 0.3s ease;
  position: relative;
  background-color: black;

  &.drag-over {
    border: 2px dashed $blue;
    background-color: lightblue !important;
  }

  .upload-icon {
    position: absolute;
    color: $blue;
    z-index: 2;
    background-color: rgb(62, 127, 255, 0.25);
    border-radius: 100%;
    padding: 8px;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%)
  }
}

.fade-slide-enter-active, .fade-slide-leave-active {
  transition: all 0.3s ease;
}

.fade-slide-enter-from, .fade-slide-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

.fade-slide-enter-to, .fade-slide-leave-from {
  opacity: 1;
  transform: translateY(0);
}

.card-form__scroll {
  :deep(.q-scrollarea__content) {
    display: flex;
    flex-direction: column;
  } 
}
.column-scroll__container {
  :deep(.q-scrollarea__container) {
    flex: 10000 1 0%;
    min-height: 0%;
    max-height: 100%
  }
}

.img-wrapper {
  width: 60%;
  height: 100%;
  transform-origin: center;
  overflow: hidden;
  touch-action: none;
}

.slides-4-comparison-view {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  width: 100%;
  height: 100%;
  .img-wrapper {
    position: relative;
    width: 100%;
  }
}

.file-receive-date {
  background: rgba(255, 255, 255, 0.65);
  backdrop-filter: blur(4px);
  border-radius: 50px;
  color: #222;
  font-size: 14px;
  border-radius: 5px;
  height: 36px;
  line-height: 1;
  z-index: 999;
}
</style>
