import { defineStore } from 'pinia'
import { ref } from 'vue'
import mtUtils from '@/utils/mtUtils'
import selectOptions from '@/utils/selectOptions'

// Booking Transaction API interfaces based on documentation
interface BookingSlot {
  id_booking_item: number
  id_employee_doctor?: number
  id_employee_staff?: number
  datetime_service_start: string // YYYY-MM-DD hh:mm:ss format
  datetime_service_end: string // YYYY-MM-DD hh:mm:ss format
  quantity?: number
  type_booking: number // 2=PPS TypeA, 3=PPS TypeB
  memo_service?: string
}

interface BookingTransactionRequest {
  id_clinic: number
  id_customer: number
  id_pet: number
  id_request?: number
  name_item_service?: string
  code_customer?: string
  booking_slots: BookingSlot[]
  event_type?: string // CREATE/UPDATE/RESCHEDULE/CANCEL/RESTORE
  update_availability?: boolean
}

interface BookingTransactionResponse {
  code: number
  success: boolean
  message: string
  data: {
    transaction_id: string
    event_type: string
    success: boolean
    request_summary: {
      id_clinic: number
      id_customer: number
      id_pet: number
      slots_count: number
      update_availability: boolean
    }
    booking_slots: Array<{
      id_service_detail: string
      id_booking_item: number
      datetime_service_start: string
      datetime_service_end: string
      status: string
      availability_updated: boolean
      conflicts: string[]
    }>
    summary: {
      total_slots_processed: number
      successful_slots: number
      failed_slots: number
      availability_updates_made: number
      warnings_count: number
      errors_count: number
      success_rate: number
    }
    warnings: string[]
    errors: string[]
  }
}

// Booking SD Records interfaces
interface BookingSDRecord {
  id_service_detail: string
  isu_name: string
  customer_name: string
  customer_code: string
  pet_name: string
  datetime_start: string
  datetime_end: string
  id_employee_doctor: number
  employee_doctor_name: string
}

interface BookingSDRecordsRequest {
  id_clinic: number
  date_start: string
  date_end?: string
  page?: number
  page_size?: number
  sort_by?: string
  sort_order?: string
  search_keyword?: string
}

interface BookingSDRecordsPagination {
  total_count: number
  total_pages: number
  current_page: number
  page_size: number
  has_next: boolean
  has_previous: boolean
}

interface BookingSDRecordsResponse {
  code: number
  success: boolean
  message: string
  data: {
    records: BookingSDRecord[]
    pagination: BookingSDRecordsPagination
    filters: {
      id_clinic: string
      date_start: string
      date_end: string
      search_keyword: string
      sort_by: string
      sort_order: string
    }
  }
}

const useBookingTransactionStore = defineStore('booking-transaction', () => {
  // State
  const loading = ref(false)
  const error = ref<string | null>(null)
  const lastTransactionResult = ref<BookingTransactionResponse['data'] | null>(null)
  
  // SD Records State
  const sdRecordsLoading = ref(false)
  const sdRecordsError = ref<string | null>(null)
  const sdRecords = ref<BookingSDRecord[]>([])
  const sdRecordsPagination = ref<BookingSDRecordsPagination | null>(null)

  // Getters
  const isLoading = () => loading.value
  const getError = () => error.value
  const getLastTransactionResult = () => lastTransactionResult.value
  
  // SD Records Getters
  const isSDRecordsLoading = () => sdRecordsLoading.value
  const getSDRecordsError = () => sdRecordsError.value
  const getSDRecords = () => sdRecords.value
  const getSDRecordsPagination = () => sdRecordsPagination.value

  /**
   * Create booking transaction using new API
   */
  const createBookingTransaction = async (request: BookingTransactionRequest): Promise<BookingTransactionResponse | null> => {
    try {
      loading.value = true
      error.value = null

      // Validate required parameters
      if (!request.id_clinic || !request.id_customer || !request.id_pet || !request.booking_slots?.length) {
        throw new Error('Required parameters: id_clinic, id_customer, id_pet, and booking_slots')
      }

      // Validate booking slots
      for (const slot of request.booking_slots) {
        if (!slot.id_booking_item || !slot.datetime_service_start || !slot.datetime_service_end || !slot.type_booking) {
          throw new Error('Invalid booking slot: missing required fields')
        }
      }

      // Set defaults/.
      const requestData = {
        ...request,
        event_type: request.event_type || 'CREATE',
        update_availability: request.update_availability !== false, // default to true
        booking_slots: request.booking_slots.map(slot => ({
          ...slot,
          quantity: slot.quantity || 1,
          type_booking: slot.type_booking || 2 // default to PPS TypeA
        }))
      }

      console.log('Creating booking transaction with data:', requestData)

      // Make API call
      const response = await mtUtils.callApi(
        selectOptions.reqMethod.POST,
        '/booking/tx-slots/create-update',
        requestData
      )

      // Process response
      if (response && response.success) {
        lastTransactionResult.value = response.data
        return response
      } else {
        throw new Error(response?.message || 'Failed to create booking transaction')
      }
    } catch (err: any) {
      const errorMessage = err.message || 'Error creating booking transaction'
      error.value = errorMessage
      console.error('Booking Transaction Store Error:', err)
      console.error('Request data:', request)
      throw new Error(errorMessage)
    } finally {
      loading.value = false
    }
  }


  /**
   * Fetch booking SD records
   */
  const fetchBookingSDRecords = async (request: BookingSDRecordsRequest): Promise<BookingSDRecordsResponse | null> => {
    try {
      sdRecordsLoading.value = true
      sdRecordsError.value = null

      // Validate required parameters
      if (!request.id_clinic || !request.date_start) {
        throw new Error('Required parameters: id_clinic and date_start')
      }

      // Build query parameters
      const queryParams = new URLSearchParams({
        id_clinic: request.id_clinic.toString(),
        date_start: request.date_start
      })

      // Add optional parameters
      if (request.date_end) queryParams.append('date_end', request.date_end)
      if (request.page) queryParams.append('page', request.page.toString())
      if (request.page_size) queryParams.append('page_size', request.page_size.toString())
      if (request.sort_by) queryParams.append('sort_by', request.sort_by)
      if (request.sort_order) queryParams.append('sort_order', request.sort_order)
      if (request.search_keyword) queryParams.append('search_keyword', request.search_keyword)

      console.log('Fetching booking SD records with params:', Object.fromEntries(queryParams))

      // Make API call
      const response = await mtUtils.callApi(
        selectOptions.reqMethod.GET,
        `/booking/sd-records/?${queryParams.toString()}`
      )

      // Process response
      if (response) {
        sdRecords.value = response.records || []
        sdRecordsPagination.value = response.pagination || null
        return response
      } else {
        throw new Error(response?.message || 'Failed to fetch booking SD records')
      }
    } catch (err: any) {
      const errorMessage = err.message || 'Error fetching booking SD records'
      sdRecordsError.value = errorMessage
      console.error('Booking SD Records Store Error:', err)
      sdRecords.value = []
      sdRecordsPagination.value = null
      throw new Error(errorMessage)
    } finally {
      sdRecordsLoading.value = false
    }
  }

  /**
   * Cancel booking transaction
   */
  const cancelBooking = async (serviceDetailId: string, reason?: string): Promise<any> => {
    try {
      loading.value = true
      error.value = null

      if (!serviceDetailId) {
        throw new Error('Service detail ID is required')
      }

      const requestData = {
        id_service_detail: serviceDetailId,
        memo_cancel: reason || '',
        update_availability: true
      }

      console.log('Cancelling booking with data:', requestData)

      // Make API call
      const response = await mtUtils.callApi(
        selectOptions.reqMethod.POST,
        '/booking/tx-slots/cancel',
        requestData
      )

      // Process response
      if (response && response.success) {
        return response
      } else {
        throw new Error(response?.message || 'Failed to cancel booking')
      }
    } catch (err: any) {
      const errorMessage = err.message || 'Error cancelling booking'
      error.value = errorMessage
      console.error('Booking Cancellation Store Error:', err)
      throw new Error(errorMessage)
    } finally {
      loading.value = false
    }
  }

  /**
   * Clear store data
   */
  const clearData = () => {
    error.value = null
    loading.value = false
    lastTransactionResult.value = null
  }

  /**
   * Clear SD records data
   */
  const clearSDRecordsData = () => {
    sdRecordsError.value = null
    sdRecordsLoading.value = false
    sdRecords.value = []
    sdRecordsPagination.value = null
  }

  /**
   * Helper function to format datetime for API
   */
  const formatDateTimeForAPI = (date: string, time: string): string => {
    // Convert to YYYY-MM-DD hh:mm:ss format required by API
    const dateTime = new Date(`${date}T${time}:00`)
    const year = dateTime.getFullYear()
    const month = String(dateTime.getMonth() + 1).padStart(2, '0')
    const day = String(dateTime.getDate()).padStart(2, '0')
    const hours = String(dateTime.getHours()).padStart(2, '0')
    const minutes = String(dateTime.getMinutes()).padStart(2, '0')
    const seconds = String(dateTime.getSeconds()).padStart(2, '0')
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  }

  /**
   * Helper function to create booking slot object
   */
  const createBookingSlot = (params: {
    bookingItemId: number
    employeeDoctorId?: number
    employeeStaffId?: number
    startDateTime: string
    endDateTime: string
    quantity?: number
    typeBooking?: number
    memo?: string
  }): BookingSlot => {
    return {
      id_booking_item: params.bookingItemId,
      id_employee_doctor: params.employeeDoctorId,
      id_employee_staff: params.employeeStaffId,
      datetime_service_start: params.startDateTime,
      datetime_service_end: params.endDateTime,
      quantity: params.quantity || 1,
      type_booking: params.typeBooking || 2, // default to PPS TypeA
      memo_service: params.memo
    }
  }

  return {
    // State
    loading,
    error,
    lastTransactionResult,
    sdRecordsLoading,
    sdRecordsError,
    sdRecords,
    sdRecordsPagination,
    
    // Getters
    isLoading,
    getError,
    getLastTransactionResult,
    isSDRecordsLoading,
    getSDRecordsError,
    getSDRecords,
    getSDRecordsPagination,
    
    // Actions
    createBookingTransaction,
    cancelBooking,
    fetchBookingSDRecords,
    clearData,
    clearSDRecordsData,
    
    // Helpers
    formatDateTimeForAPI,
    createBookingSlot
  }
})

export default useBookingTransactionStore