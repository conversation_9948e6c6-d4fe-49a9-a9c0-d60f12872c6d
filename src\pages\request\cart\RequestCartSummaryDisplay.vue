<script lang="ts" setup>
import { ref, computed } from 'vue'
import { storeToRefs } from 'pinia'

import useRequestStore from '@/stores/requests'

const requestStore = useRequestStore()
const { getCartSummary } = storeToRefs(requestStore)

const carts = computed(() => {
  const cartList = getCartSummary.value?.carts || []
  return [...cartList].sort((a, b) => {
    const [_prefixA, numberA] = a.number_cart.split('-')
    const [_prefixB, numberB] = b.number_cart.split('-')
    return parseInt(numberB) - parseInt(numberA)
  })
})

const currentIndex = ref(0)
const isExpanded = ref(false)

const currentCart = computed(() => {
  return carts.value.length > 0 ? carts.value[currentIndex.value] : {}
})

const otherCarts = computed(() => {
  return carts.value.filter((_, index) => index !== currentIndex.value)
})

function selectCart(cart: any) {
  const index = carts.value.findIndex(c => c.number_cart === cart.number_cart)
  if (index !== -1) {
    currentIndex.value = index
    isExpanded.value = false
  }
}

function shortenCartNumber(cartNumber: string): string {
  const parts = cartNumber.split('-')
  return parts.length > 1 ? `CT${parts[1]}` : cartNumber
}

function formatNumber(value: number): string {
  return Number(value).toFixed(0).replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

const totalBill = computed(() => {
  return carts.value.reduce((sum, cart) => sum + cart.bill, 0)
})

const totalInsurance = computed(() => {
  return carts.value.reduce((sum, cart) => sum + (cart.total_amount_insured || 0), 0)
})
</script>

<template>
  <div class="cart-summary" v-if="carts.length > 0">
    <div 
      class="cart-header q-pa-xs q-pl-sm flex items-center cursor-pointer" 
      @click="isExpanded = !isExpanded"
    >
    <div class="row full-width q-ml-sm">
        <div class="col-auto q-mr-sm">
          <div class="text-standard text-primary text-weight-medium">
             <q-btn round flat size="6px" class="bg-grey-9">
                <q-icon 
                  :name="isExpanded ? 'keyboard_arrow_down' : 'keyboard_arrow_up'" 
                  class="text-white" 
                  size="15px" 
                />
             </q-btn>
            {{ currentCart.name_pet }} ({{ shortenCartNumber(currentCart.number_cart) }})
          </div>
        </div>
        <div class="col-auto text-left text-black-7 text-summary-amount q-mr-sm">
          <span class="text-weight-bold">{{ formatNumber(currentCart.net_bill) }} 円</span>
        </div>
        <div class="col-auto text-left text-blue-7 text-standard">
          <q-icon
            name="health_and_safety"
            class="text-light-blue q-mr-xs"
          />
          <span class="text-weight-bold">
            {{ currentCart.total_amount_insured > 0 ? formatNumber(currentCart.total_amount_insured)  : '0' }} 円
          </span>
        </div>
      </div>
    </div>

    <!-- Expanded Content -->
    <template v-if="isExpanded">
      <div 
        v-if="otherCarts.length > 0" 
        class="cart-list"
      >
        <div 
          v-for="cart in otherCarts" 
          :key="cart.number_cart"
          @click="selectCart(cart)"
          class="cart-item q-pa-xs q-pl-sm cursor-pointer"
        >
          <div class="row full-width">
            <div class="col-auto q-mr-sm">
              <div class="text-standard text-primary text-weight-medium">
                <span class="q-ml-lg">
                  {{ cart.name_pet }} ({{ shortenCartNumber(cart.number_cart) }})
                </span>
              </div>
            </div>
            <div class="col-auto text-left text-black-7 text-summary-amount q-mr-sm">
              <span class="text-weight-bold">{{ formatNumber(cart.bill) }} 円</span>
            </div>
            <div class="col-auto text-left text-blue-7 text-standard">
              <q-icon
                name="health_and_safety"
                class="text-light-blue q-mr-xs"
              />
              <span class="text-weight-bold">
                {{ cart.total_amount_insured > 0 ? formatNumber(cart.total_amount_insured) : '0' }} 円
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Total - Only shown when expanded -->
      <div class="cart-total q-mt-sm q-pa-xs">
        <div class="row full-width">
          <div class="col-auto q-mr-lg">
            <span class="text-standard text-black text-weight-medium q-ml-lg q-mr-sm">
              オーナー合計:
            </span>
            <span class="text-weight-bold">
              {{ formatNumber(totalBill) }} 円
            </span>
          </div>
          <div class="col-auto q-mr-sm text-blue-7">
            <span class="text-left text-standard q-mr-sm">
              保険負担:
            </span>
            <span class="text-weight-bold">
              {{ formatNumber(totalInsurance) }} 円
            </span>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<style scoped>
.cart-summary {
  border-radius: 4px;
}

.cart-header {
  transition: background-color 0.3s;
}

.cart-header:hover {
  background: #f5f5f5;
}

.cart-item {
  transition: background-color 0.3s;
}

.cart-item:hover {
  background: #f5f5f5;
}

.cart-total {
  background: #f5f5f5;
}
.text-summary-amount {
  font-size: 14px;
}
.text-standard {
  font-size: 12px;
}
</style>
