<script lang="ts" setup>
import { computed, onMounted, ref, watch } from 'vue'
import mtUtils from '@/utils/mtUtils'
import selectOptions from '@/utils/selectOptions'
import { CustomerOption, ItemService } from '@/types'
import { ValidationRule } from 'quasar'

const emit = defineEmits<{
  (e: 'update:selecting', value: string): void
  (e: 'update:selectingWhole', value: CustomerOption): void
  (e: 'selectingWhole', value: CustomerOption[]): void
  (e: 'clear'): void
}>()

const props = withDefaults(
  defineProps<{
    preSelectedId?: string
    preselected?: ItemService
    label?: string
    mode?: string
    rules?: ValidationRule[]
    searchIcon?: boolean
    disable?: boolean
    multiple?: boolean
    outlined?: boolean
    applyDefaultClass?: boolean
    tabindex?: number
    style?: Object
    customOption?: boolean
    autofocus?: boolean
    isFullWidth?: boolean
    hideBottomSpace?: boolean
  }>(),
  {
    mode: '1',
    disable: false,
    multiple: false,
    outlined: false,
    applyDefaultClass: true,
    searchIcon: true,
    customOption: false,
    autofocus: false,
    isFullWidth: true,
  }
)

const inputValue = ref('')
const prevValue = ref('')
const options = ref([])

const model = ref('')
const modelArray = ref([])

const searchDropdownIcon = computed(() => {
  return props.searchIcon ? 'search' : ''
})

function filterFun(val: string, up: any, abort: any) {
  inputValue.value = val
  if (
    inputValue.value &&
    inputValue.value != '' &&
    inputValue.value.length >= 0
  ) {
    replaceOptions(up, abort)
  }
}

function popupHide(event: any) {
  var tabIndex = props.tabindex
  if (tabIndex) {
    for (var i = 0; i < 100; i++) {
      ++tabIndex
      var nextFoucusTarget = document.querySelector(`[tabindex="${tabIndex}"]`)
      if (nextFoucusTarget) {
        ;(nextFoucusTarget as HTMLElement).focus()
        break
      }
    }
  }
  // emit('hide', event) - svm - hide is not declared and I ma not sure what this for
}

async function replaceOptions(update: any = null, abort: any = null) {
  if (
    inputValue.value &&
    inputValue.value.length &&
    inputValue.value.length > 0
  ) {
    prevValue.value = inputValue.value
    const searchValue = inputValue.value.replace('　', ' ')

    let params = {}

    let resp = await mtUtils.callApi(
      selectOptions.reqMethod.GET,
      'mst/search-customer-option-list',
      {
        search_words: searchValue,
        no_pagination: searchValue.length > 2 ? true : false,
        ...params
      }
    )
    emit('selectingWhole', resp)
    if (abort) abort()
    if (resp) {
      options.value.length = 0
      let item_list: any = resp
      if (resp && item_list && item_list.length && item_list.length > 0) {
        if (update) {
          update(() => {
            options.value = item_list.map((item: ItemService) => {
              return {
                ...item,
                label: item.name_customer_search_option,
                value: item.id_customer
              }
            })
          })
        }
        emit('selectingWhole', resp)
      }
    }
  }

  if (options.value.length == 0) {
    if (abort) abort()
  }
}

const SearchItemNameInput = ref(null)

async function selected(value: any) {
  emit('update:selecting', value?.value ?? value)
  emit('update:selectingWhole', value)
}

watch(
  () => props.preSelectedId,
  async (nowValue, oldValue) => {
    if (props.preSelectedId) {
      let payload = {
        id_customer: props.preSelectedId,
      }
      const response = await mtUtils.callApi<CustomerOption[]>(
        selectOptions.reqMethod.GET,
        'mst/search-customer-option-list',
        payload
      )
      if (response && response?.length > 0) {
        model.value = response[0].name_customer_search_option
      }
    }
  },
  { immediate: true }
)

onMounted(async () => {
  if (props.preselected) {
    model.value = props?.preselected?.name_item_service ?? ''
  }
})
</script>

<template>
  <div :class="props.isFullWidth ? 'full-width' : ''" :style="props.style">
    <div
      :class="applyDefaultClass ? 'search-conds-area' : ''"
      class="row items-center"
    >
      <div v-if="!multiple" class="col">
        <q-select
          ref="SearchItemNameInput"
          v-model="model"
          :disable="props.disable"
          :dropdown-icon="searchDropdownIcon"
          :label="props.label"
          :loading="false"
          :multiple="props.multiple"
          :options="options"
          :outlined="props.outlined"
          :rules="props.rules"
          :tabindex="props.tabindex"
          :autofocus="props.autofocus"
          :hide-bottom-space="props.hideBottomSpace"
          :clearable="false"
          dense
          fill-input
          hide-dropdown-icon
          hide-selected
          input-debounce="500"
          name="qSelect"
          use-input
          @filter="filterFun"
          @update:model-value="selected"
          @popup-hide="popupHide"
        >
          <template v-slot:append>
          <!-- Custom Clear Icon -->
            <q-icon
              name="cancel"
              @click.stop="model = ''; selected(''); emit('clear')"
              class="cursor-pointer clear-icon"
              size="18px"
            />
            <q-icon name="arrow_drop_down" />
          </template>
          <template v-slot:loading>
            <div></div>
          </template>
          <template v-slot:no-option>
            <q-item>
              <q-item-section class="text-grey"> No results</q-item-section>
            </q-item>
          </template>
        </q-select>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.search-conds-area {
  background-color: #ffffff;
  height: 56px;
}

// タブが画面の外に見切れてしまうのを防ぐためX軸のpaddingを解除
.q-tab {
  padding: 0 !important;
}

.ns-btn {
  background-color: #333333;
  color: #ffffff;
}
</style>
