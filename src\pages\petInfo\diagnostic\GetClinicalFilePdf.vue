<script setup lang="ts">
import { ref, reactive, nextTick, onMounted } from 'vue'
import PdfExport from '@/pages/pdfExport/PdfExport.vue'
import { date } from 'quasar'

import useCustomerStore from '@/stores/customers'
import useClinicStore from '@/stores/clinics'
import useClinicalFilesStore from '@/stores/clinical-files'
import { storeToRefs } from 'pinia'

import {
  ClinicType,
  PetType
} from '@/types/types'
import {
  getImage,
  getFullPetName,
  getCurrentPetAge,
  concatenate,
  getDateToday
} from '@/utils/aahUtils'
import { 
  typePetGender 
} from '@/utils/enum'

const emits = defineEmits(['close'])
const close = () => emits('close')

const customerStore = useCustomerStore()
const { getCustomer } = storeToRefs(customerStore)
const clinicStore = useClinicStore()
const clinicalFilesStore = useClinicalFilesStore()

type ClinicalFileType = {
  id_clinical_file: number
  datetime_receive: string,
  styles: {
    transform: string
    transition: string
    cursor: string
  }
}
interface Props {
  callback?: (pdfBlob: Blob, pdfFileName: string) => void,
  idPet: number,
  clinicalFilesData: ClinicalFileType[]
}

const props = withDefaults(defineProps<Props>(), {
  idPet: '',
  clinicalFilesData: []
})

const exportPdf = ref()
const clinicData = ref<ClinicType | null>(null)
const petData = ref<PetType | null>(null)
const imageData = ref([])
const imagesRef = ref([])
const imageStyles = reactive(Array.from({ length: props.clinicalFilesData.length }, () => {}))

const fetchClinic = async () => {
  const idClinic = JSON.parse(localStorage.getItem('id_clinic'))
  let clinic:any = await clinicStore.fetchClinicById(idClinic)
  return clinic
}

const getPetGenderType = () => typePetGender.find((v: any) => v.value == petData.value?.type_pet_gender)?.label

const generateReport = () => {
  let imagePDFOptions: any = { quality: 0.85 }, jsPDFOptions: any = { orientation : 'landscape' }, pagesNumber: Number = 0
  if(props.callback){
    exportPdf.value.getPdfBlob(getPdfName(), pagesNumber, jsPDFOptions, imagePDFOptions).then((blob: Blob) => {
      props.callback(blob, `${getPdfName()}.pdf`)
      return close()
    })
  } else {
    exportPdf.value.generateReport(getPdfName(), pagesNumber, jsPDFOptions, imagePDFOptions)
  }
}

const getPdfName = () => {
  return `${date.formatDate(Date.now(), 'YYYYMMDD')}_cf_${getFullPetName(petData.value, getCustomer.value)}`
}

const init = async () => {
  await nextTick()
  clinicData.value = await fetchClinic()
  await nextTick()
  
  const promises = [
    clinicData.value?.logo_file_path1 ? getImage(clinicData.value.logo_file_path1) : Promise.resolve(null),
    clinicData.value?.logo_file_path2 ? getImage(clinicData.value.logo_file_path2) : Promise.resolve(null)
  ]

  const [logoPath1, logoPath2] = await Promise.allSettled(promises)
  clinicData.value.logo_file_path1 = logoPath1.status === 'fulfilled' ? logoPath1.value : null
  clinicData.value.logo_file_path2 = logoPath2.status === 'fulfilled' ? logoPath2.value : null

  await nextTick()
  generateReport()
  close()
}

type ImageStyle = {
  width: string
  height: string
  scaled: string
  naturalWidth?: number
  naturalHeight?: number
  transform?: string
  maxWidth?: string
  maxHeight?: string
}
const getBase64ImageStyle = (imageSrc: string): Promise<ImageStyle> => {
  const { clinicalFilesData } = props
  const containerHeight = clinicalFilesData.length > 2 ? 310 : 620
  return new Promise((resolve) => {
    const img = new Image()
    img.src = imageSrc
    img.onload = () => {
      const { naturalWidth, naturalHeight } = img
      if ((naturalWidth > containerHeight && clinicalFilesData.length !== 2) || naturalHeight > naturalWidth) {
        resolve({ height: '100%', width: 'auto', scaled: 'vertical', naturalWidth, naturalHeight })
      } else {
        resolve({ width: '100%', height: 'auto', scaled: 'horizontal', naturalWidth, naturalHeight })
      }
    }
    img.onerror = () => {
      resolve({ width: '100%', height: 'auto', scaled: 'horizontal' })
    }
  })
}

const updateDateOffset = (style: ImageStyle, idx: number) => {
  const scaleMatch = style.transform?.match(/scale\(([\d.]+)\)/)
  const scale = scaleMatch ? parseFloat(scaleMatch[1]) : 1

  if (scale > 1) {
    imageStyles[idx] = { top: '16px', left: '16px' }
    return
  }

  const imgElement = imagesRef.value[idx]
  if(imgElement) {
    const { clinicalFilesData } = props
    const containerWidth = clinicalFilesData.length === 1 ? 1080 : 500
    const containerHeight = clinicalFilesData.length <= 2 ? 560 : 280
    if(style.scaled == 'vertical') {
      imageStyles[idx] = { top: '16px', left: `${Math.max(20, ((containerWidth - imgElement.getBoundingClientRect().width) / 2) + 20)}px` }
    }
    else imageStyles[idx] = { left: '16px', top: `${Math.max(20, ((containerHeight - imgElement.getBoundingClientRect().height) / 2) + 16)}px` }
  }
}

onMounted(async() => {
  const { idPet, clinicalFilesData } = props
  const pet = getCustomer.value.pets.find((pet: PetType) => pet.id_pet === idPet)
  if(pet) {
    petData.value = pet 
  }
  const clinicalIds = clinicalFilesData.map((el) => el.id_clinical_file)
  const response = await clinicalFilesStore.fetchBase64ClinicalFiles(clinicalFilesData.map((clinicalFileData: ClinicalFileType) => clinicalFileData.id_clinical_file))
  if(response.data && response.data.data) {
    const clinicalImages = response.data.data.sort((a, b) => clinicalIds.indexOf(a.id_clinical_file) - clinicalIds.indexOf(b.id_clinical_file))
    const imagesContent = await Promise.all(clinicalImages.map(async (clinicalFile) => {
      const record = clinicalFilesData.find((clinicalFileData: ClinicalFileType) =>
        clinicalFileData.id_clinical_file === clinicalFile.id_clinical_file
      )

      return {
        ...clinicalFile,
        datetime_receive: record?.datetime_receive,
        style: {
          ...(record?.style || {}),
          ...(await getBase64ImageStyle(clinicalFile.image))
        }
      }
    }))
    imageData.value.push(...imagesContent)
  }
  init()
})
</script>
<template>
  <div class="q-pa-md page-inner-body">
    <PdfExport :generateOnePDF="false" ref="exportPdf" orientation="landscape" />
    <q-card id="element-to-print" style="max-width: 1200px; margin: auto" class="bg-white q-pa-none" square>
      <div class="card-pdf-main q-px-md q-pt-md" style="height: 792.96px; overflow: hidden">
        <div class="flex column justify-between" style="height: 100%;">
        <div class="flex justify-between font-12px">
          <img
            v-if="clinicData?.logo_file_path2 || clinicData?.logo_file_path1"
            :src="clinicData?.logo_file_path2 || clinicData?.logo_file_path1"
            :style="{
              maxWidth: '70px',
              height: '60px',
              objectFit: 'contain',
            }"
            class="q-mr-sm"
          />
          <div>
            <span>{{ getFullPetName(petData, getCustomer) }}</span>
            <span class="q-ml-md">{{ getCurrentPetAge(petData) }}</span>
            <span class="q-ml-md">{{ getPetGenderType() }}</span>
          </div>
        </div>
        <div style="flex-grow: 1; overflow: hidden;">
          <div class="q-mt-lg flex justify-center single-image" v-if="imageData.length === 1">
            <div class="relative-position" style="height: 620px; width: 100%; overflow: hidden;">
              <div
                :style="{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center'
                }"
              >
                <img
                  :src="imageData[0].image"
                  :ref="el => imagesRef[0] = el"
                  :load="updateDateOffset(imageData[0].style, 0)"
                  :style="{
                    ...imageData[0].style,
                    maxWidth: '100%',
                    maxHeight: '100%',
                    objectFit: 'contain'
                  }"  
                />
                <div class="file-receive-date absolute flex justify-center items-center text-center q-ma-md q-pa-sm" :style="imageStyles[0]">
                  {{ imageData[0].datetime_receive.slice(0, 10) }}
                </div>
              </div>
            </div>
          </div>
          <div class="q-mt-lg grid grid-2" v-else-if="imageData.length === 2">
            <div
              class="col-6 relative-position"
              v-for="(img, idx) in imageData"
              :key="idx"
               style="height: 600px; overflow: hidden;"
            >
              <div
                :style="{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center'
                }"
              >
                <img
                  :src="img.image"
                  :ref="el => imagesRef[idx] = el"
                  :load="updateDateOffset(img.style, idx)"
                  :style="{
                    ...img.style,
                    maxWidth: '100%',
                    maxHeight: '100%',
                    objectFit: 'contain'
                  }"  
                />
                <div class="file-receive-date absolute flex justify-center items-center text-center q-ma-md q-pa-sm" :style="imageStyles[idx]">
                  {{ img.datetime_receive.slice(0, 10) }}
                </div>
              </div>
            </div>
          </div>
          <div class="q-mt-md row q-col-gutter-md" v-else-if="imageData.length >= 3" style="height: 634px;">
            <div 
              class="col-6 q-pa-sm relative-position"
              v-for="(img, idx) in imageData.slice(0, 4)"
              style="height: 300px; overflow: hidden;"
              :key="idx"
            >
              <div
                :style="{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center'
                }"
              >
                <img
                  :src="img.image"
                  :ref="el => imagesRef[idx] = el"
                  :load="updateDateOffset(img.style, idx)"
                  :style="{
                    ...img.style,
                    maxWidth: '100%',
                    maxHeight: '100%',
                    objectFit: 'contain'
                  }"
                />
                <div class="file-receive-date absolute flex justify-center items-center text-center q-ma-md q-pa-sm" :style="imageStyles[idx]">
                  {{ img.datetime_receive.slice(0, 10) }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="q-mb-md flex justify-between items-end font-10px">
          <div>
            <div>{{ clinicData?.name_clinic_display }}</div>
            <div>
              {{ concatenate(clinicData?.address_prefecture || '', clinicData?.address_city || '', clinicData?.address_other || '') }}
            </div>
            <div>
              <span>TEL: {{ clinicData?.phone1 }}</span>
            </div>
          </div>
          <div>出力日: {{ getDateToday() }}</div>
        </div>
        </div>
      </div>
    </q-card>
  </div>
</template>
<style src="../../pdfExport/style.scss" lang="scss" scoped></style>
<style lang="scss" scoped>
.grid {
  display: grid;
  grid-gap: 20px;
  &.grid-2 {
    grid-template-columns: repeat(2, 1fr);
    :deep(img) {
      max-height: 650px;
    }
  }
  &.grid-4 {
    grid-template-columns: repeat(2, 1fr);
    :deep(img) {
      max-height: 300px;
    }
  }
}
.file-receive-date {
  background: rgba(255, 255, 255, 0.65);
  backdrop-filter: blur(4px);
  color: #222;
  font-size: 14px;
  border-radius: 5px;
  height: 36px;
  line-height: 1;
  z-index: 999;
}
</style>