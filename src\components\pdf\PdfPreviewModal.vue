<script setup lang="ts">
import { ref, computed } from 'vue'
import { Platform } from 'quasar'
import MtModalHeader from '@/components/MtModalHeader.vue'

interface Props {
  pdfPath: string
  fileName?: string
}

const props = defineProps<Props>()
const emits = defineEmits(['close'])

const closeModal = () => emits('close')

// Compute the iframe source based on platform
const iframeSrc = computed(() => {
  if (Platform.is.ipad && props.pdfPath?.includes('.pdf')) {
    return `https://docs.google.com/gview?embedded=true&url=${encodeURIComponent(props.pdfPath)}`
  } else {
    return props.pdfPath
  }
})

const openPdfInNewTab = () => {
  window.open(props.pdfPath, '_blank')
}
</script>

<template>
  <section class="column bg-black full-height clinical-files">
    <MtModalHeader class="col-auto" style="display: none" @closeModal="closeModal">
      <q-toolbar-title class="text-grey-900 title2 bold q-pa-none">
        PDF Preview
      </q-toolbar-title>
    </MtModalHeader>

    <q-card-section class="col row gap-4 full-height text-white q-pa-none relative-position">
      <!-- Header controls -->
      <div
        class="row justify-between items-start q-pa-sm absolute-top"
        style="z-index: 2;"
      >
        <section class="col flex">
          <q-card
            style="
              background: rgba(255, 255, 255, 0.65);
              backdrop-filter: blur(4px);
              -webkit-backdrop-filter: blur(4px);
              border-radius: 8px;
            "
          >
            <q-card-section class="q-pa-sm" style="opacity: 1">
              <div class="text-body1 text-grey-900 ellipsis full-width">
                {{ fileName || 'PDF Preview' }}
              </div>
            </q-card-section>
          </q-card>
        </section>
        <section class="col-auto text-right">
          <q-btn
            v-if="Platform.is.ipad"
            flat
            round
            @click="openPdfInNewTab"
          >
            <q-icon size="xs" name="picture_as_pdf" />
          </q-btn>
          <q-btn
            flat
            round
            @click="closeModal"
          >
            <q-icon size="xs" color="white" name="close" />
          </q-btn>
        </section>
      </div>

      <!-- PDF Content -->
      <section
        class="col"
        style="height: 100%"
      >
        <div
          id="left-file__compare"
          class="col full-height row justify-center items-center relative-position"
        >
          <iframe
            :src="iframeSrc"
            style="
              position: absolute;
              bottom: 0;
            "
            :style="{
              height: '88%',
              width: '60%'
            }"
            frameborder="0"
          ></iframe>
        </div>
      </section>
    </q-card-section>
  </section>
</template>

<style lang="scss" scoped>
.clinical-files {
  .q-btn {
    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }
  }
}
</style>
