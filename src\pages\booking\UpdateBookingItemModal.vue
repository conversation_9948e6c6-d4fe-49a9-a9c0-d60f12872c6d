<script setup lang="ts">
import { computed, onMounted, reactive, ref, watch } from 'vue'
import MtModalHeader from '@/components/MtModalHeader.vue'
import MtToolTipsSmall from '@/components/toolTips/MtToolTipsSmall.vue'
import MtFormInputDate from '@/components/form/MtFormInputDate.vue'
import { Platform } from 'quasar'
import mtUtils from '@/utils/mtUtils'
import {
  reservationHelperContents1,
  reservationHelperContents2,
  reservationHelperContents5,
  reservationHelperContents6,
  reservationHelperContents7,
  reservationHelperContents8
} from '@/utils/menuHelperContents'
// @ts-ignore
import DynamicFormManager from '@/components/form/DynamicFormManager.vue'
import useBookingItemStore, { BookingItemResponse } from '@/stores/booking-items'
import useEmployeeStore from '@/stores/employees'
import { useTerm } from '@/stores/term'
// stores
const bookingItemStore = useBookingItemStore()
const employeeStore = useEmployeeStore()
const termStore = useTerm()
const emits = defineEmits(['close', 'init'])
const closeModal = () => {
  emits('close')
}

// get props from parent
const props = defineProps<{
  itemService: any
  bookingItemId: number
}>()

const loading = ref(false)

// Add computed property for term options
const termOptions = computed(() => {
  const options = termStore.terms.map((term) => ({
    label: term.title_term,
    value: term.id_term
  }))
  options.push({ label: 'Create New Term', value: 'create_new' })
  return options
})


const submit = async () => {
  if (!bookingItem.value && !props.itemService) return

  try {
    loading.value = true

    // Ensure id_item_service is provided (from props or current data)
    const itemServiceId =
      props.itemService?.id_item_service ||
      bookingItem.value?.item_service?.id_item_service ||
      bookingItem.value?.booking_item?.id_item_service

    if (!itemServiceId) {
      mtUtils.autoCloseAlert('Item service ID is required', 'warning')
      loading.value = false
      return
    }

    let termIdToSubmit: string | number | null = null

    if (selectedTermId.value === 'create_new') {
      // Create new term
      const newTermData = {
        title_term: title_term.value || '',
        memo_term: content_term.value || '',
        flg_available: Number(flg_available.value)
      }
      await termStore.submitTerm(newTermData)
      if (termStore.recentTerm) {
        termIdToSubmit = termStore.recentTerm.id_term
        // Fetch terms again to update the list with the new term
        await termStore.fetchTerms()
      } else {
        mtUtils.autoCloseAlert('Failed to create new term.', 'error')
        loading.value = false
        return
      }
    } else if (selectedTermId.value) {
      termIdToSubmit = selectedTermId.value
      // Check if the selected term's details were modified and need updating
      const selectedTermInStore = termStore.terms.find((t) => t.id_term === selectedTermId.value)
      if (
        selectedTermInStore &&
        (selectedTermInStore.title_term !== title_term.value ||
          selectedTermInStore.memo_term !== content_term.value ||
          selectedTermInStore.flg_available !== Boolean(flg_available.value))
      ) {
        const updatedTermData = {
          title_term: title_term.value || '',
          memo_term: content_term.value || '',
          flg_available: Number(flg_available.value)
        }
        await termStore.updateTerm(selectedTermId.value, updatedTermData)
        // Fetch terms again to update the list with the new term
        await termStore.fetchTerms()
      }
    }

    // Prepare the data structure for the API
    const bookingItemData: any = {
      id_clinic: bookingItem.value?.booking_item?.id_clinic || props.itemService?.id_clinic,
      id_item_service: itemServiceId,
      type_booking_method:
        typeof type_booking_method.value === 'string'
          ? convertTypeBookingMethod(type_booking_method.value)
          : bookingItem.value?.booking_item?.type_booking_method || null,
      flg_business_hour_ignored: Number(flg_unavailable.value),
      flg_booking_available_item: Number(booking_available.value),
      flg_book_per_employee: Number(book_per_employee.value),
      days_bookable_after: Number(days_bookable_after.value) || null,
      days_bookable_before: Number(days_bookable_before.value) || null,
      hours_bookable_before: Number(hours_bookable_before.value) || null,
      label_unavailable: flg_unavailable_message.value || null,
      flg_additional_questions: Number(set_pickup_time.value),
      flg_inapp_purchase: Number(flg_inapp_purchase.value),
      flg_open_message: Number(flg_open_message.value),
      date_apply_bgn: date_apply_bgn.value || null,
      date_apply_end: date_apply_end.value || null,
      term: termIdToSubmit
        ? {
            id_term: termIdToSubmit,
            title_term: title_term.value || null,
            memo_term: content_term.value || null,
            flg_available: Number(flg_available.value)
          }
        : null, // Set term to null if no term is selected/created
      // The API expects these to be JSON strings with properly formatted data
      json_additional_questions: JSON.stringify(
        additional_questions.value.length > 0 ? additional_questions.value : []
      ),

      // Add json_booking_item with pickup time settings
      json_booking_item: JSON.stringify({
        additional_info: [
          {
            key: 'set_pickup_time',
            value: enable_time_range.value
          },
          {
            key: 'pickup_time_mode',
            value: pickup_mode.value || null
          },
          {
            key: 'pickup_start_time',
            value: pickup_start_time.value || null
          },
          {
            key: 'pickup_end_time',
            value: pickup_end_time.value || null
          },
          {
            key: 'pickup_lead_time',
            value: pickup_lead_time.value || null
          }
        ]
      })
    }

    // Only include bookable_employee_list if there are actual employees to include
    const employeeList = prepareBookableEmployeeList()
    if (employeeList.length > 0) {
      bookingItemData.bookable_employee_list = employeeList
    }

    console.log('Submitting booking item data:', bookingItemData)

    let response
    if (props.bookingItemId) {
      // Update existing booking item
      bookingItemData.id_booking_item = props.bookingItemId
      response = await bookingItemStore.updateBookingItem(props.bookingItemId, bookingItemData)
    } else {
      // Create new booking item
      response = await bookingItemStore.createBookingItem(bookingItemData)
    }

    if (response) {
      mtUtils.autoCloseAlert(
        '',
        props.bookingItemId ? 'Booking item updated successfully' : 'Booking item created successfully'
      )
      emits('init')
      closeModal()
    }
  } catch (error) {
    console.error('Error saving booking item:', error)
    mtUtils.autoCloseAlert('Failed to save booking item. Please try again later.')
  } finally {
    loading.value = false
  }
}

// Helper function to convert string type_booking_method to number
const convertTypeBookingMethod = (method: string): number => {
  switch (method) {
    case 'time_spec':
      return 1
    case 'range':
      return 2
    case 'tentative':
      return 3
    case 'none':
      return 4
    default:
      return 2
  }
}

// Helper function to prepare the bookable employee list
const prepareBookableEmployeeList = () => {
  // Create list of employee entries for each selected employee
  return employee_id.value.map((empId) => ({
    id_booking_item_employee: bookingItem.value?.bookable_employee_list?.find(
      (e) => e.booking_item_employee.id_employee_book === Number(empId)
    )?.booking_item_employee.id_booking_item_employee,
    id_employee_book: Number(empId), // Convert string to number
    flg_booking_available_employee: 1,
    flg_workschedule_considered: 1,
    flg_workschedule_per_item_considered: 0,
    employee_workschedule_list: []
  }))
}

const isIpad = computed(() => {
  return Platform?.is.ipad
})

const openHelpMenu1 = async () => {
  await mtUtils.smallPopup(MtToolTipsSmall, {
    title: reservationHelperContents1.reservationViewPage.title,
    content: reservationHelperContents1.reservationViewPage.content
  })
}

const openHelpMenu2 = async () => {
  await mtUtils.smallPopup(MtToolTipsSmall, {
    title: reservationHelperContents2.reservationViewPage.title,
    content: reservationHelperContents2.reservationViewPage.content
  })
}

const openHelpMenu5 = async () => {
  await mtUtils.smallPopup(MtToolTipsSmall, {
    title: reservationHelperContents5.reservationViewPage.title,
    content: reservationHelperContents5.reservationViewPage.content
  })
}

const openHelpMenu6 = async () => {
  await mtUtils.smallPopup(MtToolTipsSmall, {
    title: reservationHelperContents6.reservationViewPage.title,
    content: reservationHelperContents6.reservationViewPage.content
  })
}

const openHelpMenu7 = async () => {
  await mtUtils.smallPopup(MtToolTipsSmall, {
    title: reservationHelperContents7.reservationViewPage.title,
    content: reservationHelperContents7.reservationViewPage.content
  })
}

const openHelpMenu8 = async () => {
  await mtUtils.smallPopup(MtToolTipsSmall, {
    title: reservationHelperContents8.reservationViewPage.title,
    content: reservationHelperContents8.reservationViewPage.content
  })
}

// Extract reactive variables for use in the form
const bookingItem = ref<BookingItemResponse | null>(null)
const booking_available = ref(false)
const book_per_employee = ref(false)
const employee_id = ref<string[]>([])
const days_bookable_after = ref<number | null>(60)
const days_bookable_before = ref<number | null>(1)
const hours_bookable_before = ref<number | null>(1)
const type_booking_method = ref<string | null>(null)
const enable_time_range = ref(false)
const pickup_mode = ref<string | null>('sequential')
const pickup_lead_time = ref<number | null>(15)
const pickup_start_time = ref<string | null>('')
const pickup_end_time = ref<string | null>('')
const flg_unavailable = ref(false)
const flg_inapp_purchase = ref(false)
const flg_open_message = ref(false)
const flg_unavailable_message = ref<string | null>('')
const set_pickup_time = ref(false)
const flg_available = ref(false)
const title_term = ref<string | null>('')
const content_term = ref<string | null>('')
const date_apply_bgn = ref<string | null>('')
const date_apply_end = ref<string | null>('')

// Add a computed property for the editor to handle null values
const editorContent = computed({
  get: () => content_term.value || '',
  set: (val) => {
    content_term.value = val
  }
})

// Define a new ref for selected term ID
const selectedTermId = ref<string | null>(null)

// Define the structure for our form fields
interface FormField {
  key: string
  type: string
  label: string
  required: boolean
  options?: Array<{ value: string; label: string }>
}

// Replace the simple array with a properly formatted array for the form manager
const additional_questions = ref<FormField[]>([])

const employees = computed(() => {
  return employeeStore.employees.map((employee) => ({
    label: employee.name_display,
    value: String(employee.id_employee)
  }))
})

const selectingEmployee = (value: string[]) => {
  employee_id.value = value
}

// Radio options
const bookingMethodOptions = [
  { label: '時間指定', value: 'time_spec' },
  { label: '時間帯', value: 'range' },
  { label: '仮予約', value: 'tentative' },
  { label: '時間指定なし', value: 'none' }
]

const pickupModeOptions = [
  { label: '順次', value: 'sequential' },
  { label: '固定', value: 'fixed' }
]

const addQuestion = () => {
  additional_questions.value.push({
    key: '',
    type: 'radio',
    label: '',
    required: false,
    options: []
  })
}

const deleteQuestion = (index: number) => {
  additional_questions.value.splice(index, 1)
}

const leadTimeOptions = [
  { label: '15分', value: 15 },
  { label: '30分', value: 30 },
  { label: '45分', value: 45 },
  { label: '60分', value: 60 }
]

// Replace the current mapApiDataToForm function
const mapApiDataToForm = () => {
  if (!bookingItem.value) return

  // Map boolean values directly
  booking_available.value = Boolean(bookingItem.value.booking_item?.flg_booking_available_item)
  book_per_employee.value = Boolean(bookingItem.value.booking_item?.flg_book_per_employee)
  flg_unavailable.value = Boolean(bookingItem.value.booking_item?.flg_business_hour_ignored)
  flg_inapp_purchase.value = Boolean(bookingItem.value.booking_item?.flg_inapp_purchase)
  flg_open_message.value = Boolean(bookingItem.value.booking_item?.flg_open_message)
  set_pickup_time.value = Boolean(bookingItem.value.booking_item?.flg_additional_questions)

  // Map employee if available
  if (bookingItem.value.bookable_employee_list?.length) {
    // Extract employee IDs from the response and convert to strings
    employee_id.value = bookingItem.value.bookable_employee_list.map((emp) =>
      String(emp.booking_item_employee.id_employee_book_id)
    )

    // Make sure we have employee data loaded for display
    if (!employeeStore.employees.length) {
      employeeStore.fetchEmployees({ flg_calendar: 1 })
    }
  } else {
    // Reset to empty array if no employees
    employee_id.value = []
  }

  // Map booking method - set to null if undefined or 0
  if (bookingItem.value.booking_item?.type_booking_method) {
    switch (bookingItem.value.booking_item?.type_booking_method) {
      case 1:
        type_booking_method.value = 'time_spec'
        break
      case 2:
        type_booking_method.value = 'range'
        break
      case 3:
        type_booking_method.value = 'tentative'
        break
      case 4:
        type_booking_method.value = 'none'
        break
      default:
        type_booking_method.value = null
    }
  } else {
    type_booking_method.value = null
  }

  // Map numeric values with defaults if empty or 0
  days_bookable_after.value = bookingItem.value.booking_item?.days_bookable_after || 60
  days_bookable_before.value = bookingItem.value.booking_item?.days_bookable_before || 1
  hours_bookable_before.value = bookingItem.value.booking_item?.hours_bookable_before || 1

  // Map text values with defaults if empty
  flg_unavailable_message.value = bookingItem.value.booking_item?.label_unavailable || ''

  // Map date values
  date_apply_bgn.value = bookingItem.value.booking_item?.date_apply_bgn || ''
  date_apply_end.value = bookingItem.value.booking_item?.date_apply_end || ''

  // Map additional questions if available
  if (bookingItem.value.booking_item?.json_additional_questions) {
    try {
      // Handle string format - parse it if it's a string
      let parsedQuestions: FormField[] = []
      const rawData = bookingItem.value.booking_item.json_additional_questions

      if (typeof rawData === 'string') {
        // Replace single quotes with double quotes for valid JSON
        const jsonString = rawData.replace(/'/g, '"').replace(/True/g, 'true').replace(/False/g, 'false')
        try {
          const jsonData = JSON.parse(jsonString)

          if (Array.isArray(jsonData)) {
            // Check if it's already in the expected format with type and options
            if (jsonData.length > 0 && 'type' in jsonData[0]) {
              parsedQuestions = jsonData as FormField[]
            } else {
              // Convert from old format to new format
              parsedQuestions = jsonData.map((q: any) => ({
                key: q.key || '',
                type: 'radio',
                label: q.label || q.key || '',
                required: false,
                options: q.value
                  ? q.value
                      .toString()
                      .split(',')
                      .map((v: string) => ({
                        value: v.trim(),
                        label: v.trim()
                      }))
                  : []
              }))
            }
          }
        } catch (parseError) {
          console.error('Error parsing json_additional_questions:', parseError)
        }
      } else if (Array.isArray(rawData)) {
        // Convert array to FormField[] if needed
        if (rawData.length > 0 && 'type' in rawData[0]) {
          parsedQuestions = rawData as unknown as FormField[]
        } else {
          // Convert old format array to FormField[]
          parsedQuestions = rawData.map((q: any) => ({
            key: q.key || '',
            type: 'radio',
            label: q.label || q.key || '',
            required: false,
            options: q.value
              ? q.value
                  .toString()
                  .split(',')
                  .map((v: string) => ({
                    value: v.trim(),
                    label: v.trim()
                  }))
              : []
          }))
        }
      }

      additional_questions.value = parsedQuestions
    } catch (error) {
      console.error('Error processing json_additional_questions:', error)
      additional_questions.value = []
    }
  } else {
    additional_questions.value = []
  }

  // Map booking_item json data if available
  if (bookingItem.value.booking_item?.json_booking_item) {
    try {
      let parsedBookingItem: any = bookingItem.value.booking_item.json_booking_item

      if (typeof parsedBookingItem === 'string') {
        // Replace single quotes with double quotes for valid JSON
        const jsonString = parsedBookingItem.replace(/'/g, '"').replace(/True/g, 'true').replace(/False/g, 'false')
        parsedBookingItem = JSON.parse(jsonString)
      }

      const additionalInfo = parsedBookingItem.additional_info || []

      // Find values in the additional_info array
      const findValue = (key: string): any => {
        const item = additionalInfo.find((info: any) => info.key === key)
        return item ? item.value : null
      }

      enable_time_range.value = Boolean(findValue('set_pickup_time'))
      pickup_mode.value = findValue('pickup_time_mode') || 'sequential'
      pickup_start_time.value = findValue('pickup_start_time') || ''
      pickup_end_time.value = findValue('pickup_end_time') || ''
      pickup_lead_time.value = Number(findValue('pickup_lead_time')) || 15
    } catch (error) {
      console.error('Error parsing json_booking_item:', error)
    }
  }

  // Map term data
  if (bookingItem.value.booking_item?.term) {
    selectedTermId.value = String(bookingItem.value.booking_item.term.id_term) // Ensure selectedTermId is set
    flg_available.value = Boolean(bookingItem.value.booking_item.term.flg_available)
    title_term.value = bookingItem.value.booking_item.term.title_term || ''
    content_term.value = bookingItem.value.booking_item.term.memo_term || ''
  } else {
    selectedTermId.value = null // Explicitly set to null if no term
    flg_available.value = false
    title_term.value = ''
    content_term.value = ''
  }
}

// Function to handle updates from DynamicFormManager
const handleAdditionalQuestionsUpdate = (updatedFields: FormField[]) => {
  additional_questions.value = updatedFields
}

// on mounted
onMounted(async () => {
  // Fetch employees data first to ensure we have it available
    await employeeStore.fetchEmployees({ flg_calendar: 1 })

  // Fetch terms
  await termStore.fetchTerms()

  if (props.bookingItemId) {
    // get booking item data
    await bookingItemStore.fetchBookingItem(props.bookingItemId)
    bookingItem.value = bookingItemStore.currentBookingItem

    // Map API data to form fields
    if (bookingItem.value) {
      mapApiDataToForm()
      // Set the selectedTermId if a term is associated with the booking item
      if (bookingItem.value.booking_item?.term?.id_term) {
        selectedTermId.value = String(bookingItem.value.booking_item.term.id_term)
      }
    }
  }
})

// Add this at the top of the script, with other reactive refs
const editorColor = ref('#000000')

// Add these watches after onMounted
// Watch for changes to book_per_employee
watch(
  () => book_per_employee.value,
  (newValue) => {
    // If book_per_employee is unchecked, clear employee selection
    if (!newValue) {
      employee_id.value = []
    }
  }
)

// Watch for changes to set_pickup_time
watch(
  () => set_pickup_time.value,
  (newValue) => {
    // If set_pickup_time is unchecked, clear additional questions
    if (!newValue) {
      additional_questions.value = []
    }
  }
)

// Watch for changes to enable_time_range
watch(
  () => enable_time_range.value,
  (newValue) => {
    // If enable_time_range is unchecked, clear pickup time related fields
    if (!newValue) {
      pickup_mode.value = 'sequential'
      pickup_start_time.value = ''
      pickup_end_time.value = ''
      pickup_lead_time.value = 15
    }
  }
)

// Watch for changes to selectedTermId
watch(
  () => selectedTermId.value,
  (newTermId) => {
    if (newTermId) {
      const selectedTerm = termStore.terms.find((term) => term.id_term === newTermId)
      if (selectedTerm) {
        title_term.value = selectedTerm.title_term
        content_term.value = selectedTerm.memo_term
        flg_available.value = selectedTerm.flg_available
      } else if (newTermId === 'create_new') {
        // Clear fields for new term creation
        title_term.value = ''
        content_term.value = ''
        flg_available.value = false
      }
    } else {
      // Clear fields if no term is selected
      title_term.value = ''
      content_term.value = ''
      flg_available.value = false
    }
  }
)

const memoPpsRefEditor = ref()
const memoPpsEditorForeColor = ref('#000000')

const colorClicked = () => {
  const edit = memoPpsRefEditor.value
  edit.runCmd('foreColor', memoPpsEditorForeColor.value)
  edit.focus()
}
</script>

<template>
  <q-form @submit.prevent="submit">
    <MtModalHeader @closeModal="closeModal">
      <q-toolbar-title class="text-grey-900 title2 bold"> 予約商品設定 </q-toolbar-title>
    </MtModalHeader>
    <q-card-section class="q-mt-md q-px-xl content" :class="{ 'q-mt-md': isIpad }">
      <div class="row q-col-gutter-md">
        <!-- 予約可 -->
        <div class="col-3">
          <q-checkbox v-model="booking_available" label="予約可能" val="1" keep-color />
        </div>
        <div class="col-9">
          <div class="q-mt-sm">
            <span class="text-subtitle2">予約の取得方法</span>
            <q-btn dense flat round @click="openHelpMenu1" class="q-mx-sm grey-800">
              <q-icon size="24px" name="help_outline" />
            </q-btn>
          </div>
          <q-option-group
            v-model="type_booking_method"
            :options="bookingMethodOptions"
            inline
            class="q-gutter-md"
            color="primary"
          />
        </div>
      </div>

      <div class="row q-col-gutter-md">
        <!-- 指名あり -->
        <div class="col-3">
          <q-checkbox v-model="book_per_employee" label="指名あり" keep-color />
        </div>
        <div class="col-4" v-if="book_per_employee">
          <q-select
            v-model="employee_id"
            :options="employees"
            clearable
            dense
            label="myVetty指名表示スタッフ"
            multiple
            use-chips
            emit-value
            map-options
            @update:model-value="selectingEmployee"
            :tabindex="4"
          />
        </div>
      </div>

      <!-- 予約可能期間 -->
      <div class="items-end q-mt-md row q-col-gutter-md">
        <div class="col-3">
          <span class="text-subtitle2">予約可能期間の指定</span>
        </div>
        <div class="items-center col-8 row q-col-gutter-sm">
          <!-- 日後から -->
          <q-input
            v-model.number="days_bookable_after"
            type="number"
            dense
            flat
            class="text-center underline-input"
            style="width: 72px"
          />

          <span>〜</span>

          <!-- 日前まで -->
          <q-input
            v-model.number="days_bookable_before"
            type="number"
            dense
            flat
            class="text-center underline-input"
            style="width: 72px"
          />
          <span>日前まで予約可能</span>
        </div>

        <!-- 時間前まで only when applicable -->
        <div class="col-3"></div>
        <div v-if="(days_bookable_after ?? 0) < 1" class="items-center q-mt-sm col-8 row q-col-gutter-sm">
          <div class="flex items-center gap-sm" style="margin-left: 100px">
            <q-input
              v-model.number="hours_bookable_before"
              type="number"
              dense
              flat
              class="text-center underline-input"
              style="width: 72px"
            />
            <span class="text-grey-500">時間前まで予約可能</span>
          </div>
        </div>
      </div>

      <!-- お迎え時間 -->
      <div class="q-mt-md row q-col-gutter-md">
        <!-- Enable pickup time -->
        <div class="col-3">
          <q-checkbox v-model="enable_time_range" label="お迎え時間を設定する" keep-color />
        </div>
        <div class="col-9" v-if="enable_time_range">
          <!-- Mode selector -->
          <q-option-group v-model="pickup_mode" :options="pickupModeOptions" inline class="q-gutter-md" />

          <!-- 固定 mode: start/end time -->
          <div v-if="pickup_mode === 'fixed'" class="items-center q-mt-sm row q-col-gutter-sm">
            <q-input
              v-model="pickup_start_time"
              type="time"
              flat
              dense
              class="underline-input"
              placeholder="お迎え時間start"
              style="width: 20%"
            />
            <span>〜</span>
            <q-input
              v-model="pickup_end_time"
              type="time"
              flat
              dense
              class="underline-input"
              placeholder="お迎え時間end"
              style="width: 20%"
            />
          </div>

          <!-- 順次 mode: lead‐time select + help -->
          <div v-else-if="pickup_mode === 'sequential'" class="items-center q-mt-sm row q-col-gutter-sm">
            <q-select
              v-model="pickup_lead_time"
              :options="leadTimeOptions"
              flat
              dense
              class="underline-input col-3"
              placeholder="お迎え余裕時間"
            />
            <q-btn dense flat round @click="openHelpMenu2" class="q-mx-sm grey-800">
              <q-icon size="24px" name="help_outline" />
            </q-btn>
          </div>
        </div>
      </div>

      <!-- トグル群 -->
      <div class="q-mt-md row q-col-gutter-md">
        <div>
          <q-checkbox v-model="flg_unavailable" label="診療時間内の予約にする" keep-color />
          <!-- help menu -->
          <q-btn dense flat round @click="openHelpMenu5" class="q-mx-sm grey-800">
            <q-icon size="24px" name="help_outline" />
          </q-btn>
        </div>
        <div>
          <q-checkbox v-model="flg_inapp_purchase" label="myVetty決済を利用する" keep-color />
        </div>
        <div>
          <q-checkbox v-model="flg_open_message" label="トークルームの利用" keep-color />
          <!-- help menu -->
          <q-btn dense flat round @click="openHelpMenu6" class="q-mx-sm grey-800">
            <q-icon size="24px" name="help_outline" />
          </q-btn>
        </div>
      </div>

      <!-- tel -->
      <div class="q-mt-md row q-col-gutter-md">
        <div class="flex col-12">
          <q-input v-model="flg_unavailable_message" label="予約不可の場合の表示" dense style="width: 20%" />
          <q-btn dense flat round @click="openHelpMenu7" class="q-mx-sm grey-800">
            <q-icon size="24px" name="help_outline" />
          </q-btn>
        </div>
      </div>

      <div class="items-end q-mt-md row q-col-gutter-md">
        <div class="flex col-12">
          <q-checkbox v-model="set_pickup_time" label="追加質問を利用する" keep-color />
          <q-btn dense flat round @click="openHelpMenu8" class="q-mx-sm grey-800">
            <q-icon size="24px" name="help_outline" />
          </q-btn>
        </div>
      </div>

      <!-- 追加質問 -->
      <div class="q-mt-lg" v-if="set_pickup_time">
        <DynamicFormManager v-model="additional_questions" @update:modelValue="handleAdditionalQuestionsUpdate" />
      </div>

      <!-- 利用規約 -->
      <div class="q-mt-md row q-col-gutter-md">
        <div class="col-3">
          <q-checkbox v-model="flg_available" label="利用規約を表示する" keep-color />
        </div>
        <div class="col-4">
          <q-select
            v-if="flg_available"
            v-model="selectedTermId"
            :options="termOptions"
            label="利用規約を選択"
            dense
            emit-value
            map-options
            clearable
          />
        </div>
        <div class="col-4" v-if="flg_available">
          <q-input v-model="title_term" label="タイトル" dense />
        </div>
      </div>

      <div class="q-mt-sm row q-col-gutter-md" v-if="flg_available">
        <div class="col-12">
          <q-editor
            :toolbar="[
              ['left', 'center', 'right', 'justify'],
              ['bold', 'italic', 'strike', 'underline'],
              ['undo', 'redo'],
              ['token'],
              [
                {
                  label: $q.lang.editor.formatting,
                  icon: $q.iconSet.editor.formatting,
                  list: 'no-icons',
                  options: ['p', 'h2', 'h3', 'h4', 'h5']
                }
              ]
            ]"
            ref="memoPpsRefEditor"
            toolbar-bg="primary"
            toolbar-text-color="white"
            toolbar-toggle-color="accent-700"
            height="40vh"
            class="editor"
            v-model="editorContent"
          >
            <template v-slot:token>
              <q-color
                v-model="memoPpsEditorForeColor"
                no-header
                no-footer
                @click="colorClicked()"
                default-view="palette"
                :palette="['#000000', '#FF0000', '#0000FF', '#008000', '#505050']"
                unelevated
                class="bg-primary q-mt-sm color-picker"
              />
            </template>
          </q-editor>
        </div>
      </div>

      <!-- 適用期間 -->
      <div class="q-mt-lg row q-col-gutter-md">
        <div class="col-12 col-md-8 row q-col-gutter-sm">
          <MtFormInputDate
            v-model:date="date_apply_bgn"
            label="運用開始日"
            class="col"
            :rules="[(val) => !date_apply_end || val <= date_apply_end || '終了日は開始日以降を選択してください']"
          />
          <span>〜</span>
          <MtFormInputDate
            v-model:date="date_apply_end"
            label="運用終了日"
            class="col"
            :rules="[(val) => !date_apply_bgn || val >= date_apply_bgn || '開始日は終了日以前を選択してください']"
          />
        </div>
      </div>
    </q-card-section>

    <q-card-section class="bg-white q-bt">
      <div class="text-center modal-btn">
        <q-btn outline class="bg-grey-100 text-grey-800" @click="closeModal()">
          <span>キャンセル</span>
        </q-btn>
        <q-btn unelevated color="primary" class="q-ml-md" type="submit" :loading="loading">
          <span>保存</span>
        </q-btn>
      </div>
    </q-card-section>
  </q-form>
</template>

<style lang="scss" scoped>
.mt-small-popup {
  border: $grey-800 1px solid;
  border-radius: 6px;
  background-color: $white;
  width: 260px !important;
}

.assort-drip-text-1:after {
  content: 'mL';
  top: 65% !important;
}

.assort-drip-text-2:after {
  content: 'mg';
  top: 65% !important;
}

.c-grid-w {
  display: grid;
  grid-template-columns: 3fr 1fr;
  column-gap: 10px;
}
.item-service-img {
  border-radius: 20px;
  position: absolute;
  right: 20px;
  top: 20px;
}
.price-bold {
  font-weight: bold;
}
.highlight {
  background-color: #fff9c4;
}
.active-section {
  padding: 1rem;
}
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: baseline;
  margin-bottom: 0.5rem;
}
.editor {
  line-height: 1.7;
  word-break: break-all;
  :deep(.q-editor__toolbar-group) {
    &:last-child {
      margin-left: -90px;
    }
  }

  :deep(.q-editor__content) {
    padding: 16px;
  }

  .color-picker {
    max-width: 20px;
    box-shadow: none;
    border-radius: 0;
  }
}
</style>
