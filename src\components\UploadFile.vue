<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue'
import { imageResize } from '@/utils/helper'
import mtUtils from '@/utils/mtUtils'
import PdfThumbnail from '@/components/pdf/PdfThumbnail.vue'
import PdfPreviewModal from '@/components/pdf/PdfPreviewModal.vue'
import ImagePreviewModal from '@/components/ImagePreviewModal.vue'

const emits = defineEmits(['fileUploaded', 'fileRemoved'])

const maxFileSizeinMb = 10 * 1024 * 1024

interface fileProps {
  multiple: boolean
  accept: string
  maxFiles: number
  rules: Array<any>
  autofocus: boolean
  clearable: boolean
  loading: boolean
  maxFileSize: number
  fileUrl: string | Blob
  isOnlyView: boolean
  smallTumbnail: boolean
  cardThumbnail: boolean
  disabledPreview: boolean
}

const props = withDefaults(defineProps<fileProps>(), {
  multiple: false,
  accept: 'image/*,.pdf',
  maxFiles: 1,
  rules: [],
  autofocus: false,
  clearable: false,
  loading: false,
  maxFileSize: maxFileSizeinMb,
  fileUrl: '',
  isOnlyView: false,
  smallTumbnail: false,
  cardThumbnail: false,
  disabledPreview: false
})

const qFilePicker = ref()
const modelValue = ref()
const filePath = ref<string | null>(null), fileName = ref<string>('')

// Computed properties for file type detection
const isPdfFile = computed(() => {
  return filePath.value && filePath.value.includes('.pdf')
})

const isImageFile = computed(() => {
  return filePath.value && !filePath.value.includes('.pdf')
})

const cleanFilePath = computed(() => {
  return filePath.value ? filePath.value.split('#')[0] : ''
})

// PDF preview functionality
const openPdfPreview = async () => {
  if(props.disabledPreview) return
  if (isPdfFile.value && cleanFilePath.value) {
    await mtUtils.popup(PdfPreviewModal, {
      pdfPath: cleanFilePath.value,
      fileName: fileName.value
    }, true)
  }
}

// Image preview functionality
const openImagePreview = async () => {
  if(props.disabledPreview) return
  if (isImageFile.value && cleanFilePath.value) {
    await mtUtils.popup(ImagePreviewModal, {
      imagePath: cleanFilePath.value,
      fileName: fileName.value
    }, true)
  }
}

const handleFileUpload = async (file: File) => {
  if (file.type.startsWith('image/')) {
    const i = await getResizedImage(file)
    filePath.value = URL.createObjectURL(i)
    filePath.value += '#type=.img'
  } else if (file.type === 'application/pdf') {
    // For PDF files, create object URL without hash for PdfThumbnail component
    filePath.value = URL.createObjectURL(file)
    filePath.value += '#type=.pdf'
  } else {
    // For other file types
    filePath.value = URL.createObjectURL(file)
    filePath.value += '#type=.other'
  }
  fileName.value = file.name
  emits('fileUploaded', file)
}

type rejectType = {
  failedPropValidation: string,
  file: File
}
const handleUploadReject = (reject: rejectType[]) => {
   const alertMessage:string = reject[0].failedPropValidation 
   mtUtils.alert(alertMessage)
}

const removeFile = () => {
  filePath.value = null
  emits('fileRemoved')
}

const getResizedImage = (file: any) => {
  return Promise.resolve(imageResize(file))
}

const getFileName = (filePath: string): string => {
  const pathParts = filePath.split('/')
  let fileName = pathParts[pathParts.length - 1]
  if(fileName.includes('?')) fileName = fileName.split('?')[0]
  return fileName
}

onMounted(() => {
  if(props.fileUrl) {
    filePath.value = props.fileUrl
    fileName.value = getFileName(props.fileUrl)
  }
})
</script>

<template>
   <div>
      <template v-if="!filePath">
        <q-file
          v-model="modelValue"
          :multiple="multiple"
          :accept="accept"
          :max-files="maxFiles"
          :autofocus="autofocus"
          :clearable="clearable"
          :loading="loading"
          :max-file-size="maxFileSize"
          ref="qFilePicker"
          @update:model-value="handleFileUpload"
          @rejected="handleUploadReject"
          class="qFilePicker"
        >
          <template v-slot:default>
            <q-responsive
              :ratio="smallTumbnail ? 3/2 : cardThumbnail ? 16/9 : 4/3"
              :class="smallTumbnail ? 'small-thumbnail-picker' : cardThumbnail ? 'card-thumbnail-picker' : 'full-width'"
              :style="smallTumbnail ? 'width: 60px; height: 40px;' : cardThumbnail ? 'width: 100%; height: 115px;' : ''"
            >
              <q-btn
                unelevated
                color="grey-300"
                text-color="grey-800"
                :class="smallTumbnail ? 'small-thumbnail-btn' : cardThumbnail ? 'card-thumbnail-btn' : 'full-width q-pa-xl'"
                @click="$refs.qFilePicker.pickFiles()"
                :style="smallTumbnail ? 'width: 60px; height: 40px; padding: 4px;' : cardThumbnail ? 'width: 100%; height: 115px; padding: 0;' : 'width: 100%'"
              >
                <q-icon :size="smallTumbnail ? '12px' : cardThumbnail ? '16px' : '20px'" name="add" />
              </q-btn>
            </q-responsive>
          </template>
        </q-file>
      </template>
      <template v-else>
        <div
          class="relative-position text-center"
          :class="{ 'small-thumbnail': smallTumbnail, 'card-thumbnail': cardThumbnail }"
          :style="smallTumbnail ? 'width: 60px; height: 40px;' : cardThumbnail ? 'width: 100%; height: 115px;' : 'width: 100%; height: 100%;'"
        >
          <!-- Image Preview -->
          <div v-if="isImageFile" class="image-preview-container cursor-pointer" @click="openImagePreview">
            <q-img
              :src="filePath"
              spinner-color="white"
              :class="smallTumbnail ? 'small-thumbnail-img' : cardThumbnail ? 'card-thumbnail-img' : 'full-width full-height'"
              :style="{ backgroundColor: 'gray' }"
              :ratio="smallTumbnail ? 3/2 : cardThumbnail ? 16/9 : 4/3"
              :fit="'cover'"
            />
          </div>
          <!-- PDF Preview -->
          <template v-else-if="isPdfFile">
            <div
              class="pdf-preview-container cursor-pointer"
              :class="{ 'small-thumbnail-pdf': smallTumbnail, 'card-thumbnail-pdf': cardThumbnail }"
              @click="openPdfPreview"
            >
              <PdfThumbnail :pdfPath="cleanFilePath" :cardThumbnail="cardThumbnail" />
            </div>
          </template>
          <!-- Other File Types -->
           <q-badge
            v-if="!props.isOnlyView"
             color="red"
             floating
             transparent
             class="cursor-pointer"
             @click="removeFile"
            >
              <q-icon name="close" />
            </q-badge>
          </div>
      </template>
   </div>
</template>
<style lang="scss" scoped>
.qFilePicker {
   :deep(.q-field__native) {
     display: none;
   }
}

.pdf-preview-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s ease;
    border-radius: 12px!important;


  &:hover {
    .pdf-preview-overlay {
      opacity: 1;
    }
  }

  :deep(canvas) {
    max-width: 100%;
    max-height: 200px;
    border-radius: 12px!important;
    transition: all 0.3s ease;
  }
}

.image-preview-container {
  width: 100%;
  height: 100%;
  display: flex;
  border-radius: 12px!important;
  max-width: 100%;
  max-height: 200px;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  :deep(.q-img) {
    border-radius: 8px;
    transition: all 0.3s ease;
  }
}

.small-thumbnail {
  .image-preview-container {
    &:hover {
      transform: scale(1.05);
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    }
  }
}

.small-thumbnail-img {
  width: 60px !important;
  height: 40px !important;
  border-radius: 4px;

  :deep(.q-img__content) {
    border-radius: 4px;
  }
}

.small-thumbnail-pdf {
  width: 60px;
  height: 40px;

  :deep(canvas) {
    max-width: 60px !important;
    max-height: 40px !important;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  }
}

.small-thumbnail-picker {
  width: 60px !important;
  height: 40px !important;
}

.small-thumbnail-btn {
  border-radius: 4px;
  min-height: 40px;
  font-size: 10px;
}

.pdf-preview-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 8px;
  backdrop-filter: blur(2px);
}

.pdf-filename {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  padding: 4px 8px;
  backdrop-filter: blur(4px);
  border: 1px solid #e0e0e0;

  .text-caption {
    font-size: 12px;
    color: #666;
    font-weight: 500;
  }
}

.other-file-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border-radius: 8px;
  border: 2px dashed #ccc;
  min-height: 150px;
}

.card-thumbnail {
  width: 100%;
  height: 115px; // 16:9 ratio for 280px width
  border-radius: 0;
  overflow: hidden;
}

.card-thumbnail-picker {
  width: 100% !important;
  height: 115px !important; // 16:9 ratio
  border-radius: 0;
}

.card-thumbnail-btn {
  width: 100% !important;
  height: 115px !important; // 16:9 ratio
  border-radius: 0;
  padding: 0 !important;
}

.card-thumbnail-img {
  width: 100%;
  height: 115px; // 16:9 ratio
  border-radius: 0;
  object-fit: cover;
}

.card-thumbnail-pdf {
  width: 100%;
  height: 115px; // 16:9 ratio
  border-radius: 0;
  overflow: hidden;

  :deep(canvas) {
    width: 100% !important;
    height: 140px !important;
    max-width: 100% !important;
    max-height: 140px !important;
    border-radius: 0;
    object-fit: cover;
    display: block;
  }
}
</style>