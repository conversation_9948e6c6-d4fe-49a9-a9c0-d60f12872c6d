<script setup lang="ts">
import { computed, onMounted, reactive, ref, watch } from 'vue'
import MtModalHeader from '@/components/MtModalHeader.vue'
import MtToolTipsSmall from '@/components/toolTips/MtToolTipsSmall.vue'
import OptionModal from '@/components/OptionModal.vue'
import MInputTime from '@/components/form/MInputTime.vue'
import selectOptions from '@/utils/selectOptions'
import { Platform } from 'quasar'
import mtUtils from '@/utils/mtUtils'
import { reservationHelperContents3 } from '@/utils/menuHelperContents'
import useBusinessHourSlot from '@/stores/business-hour-slots'
import useBookingItemStore, {
  BookingItem,
  BusinessDay,
  DayItem,
  BusinessHourSlot,
  BookingDaySlot
} from '@/stores/booking-items'
import useBusinessDay from '@/stores/business-day'
import { storeToRefs } from 'pinia'
import aahValidations from '@/utils/aahValidations'
import { typeBusinessDay, typeWeekday, timeHour, timeMinute } from '@/utils/enum'

const props = defineProps({
  id_clinic: {
    type: Number,
    required: true
  },
  id_item_service: {
    type: Number,
    required: true
  },
  selectedDay: {
    type: Object,
    default: null
  },
  id_booking_item: {
    type: Number,
    required: true
  },
  init: {
    type: Function,
    default: null
  }
})

const emits = defineEmits(['close', 'init'])

const businessHourSlotStore = useBusinessHourSlot()
const bookingItemStore = useBookingItemStore()
const businessDayStore = useBusinessDay()
const { getBusinessDays } = storeToRefs(businessDayStore)
const businessHourSlots: any = ref([])
const isUpdate = ref(false)
// Days array from API (will be populated in onMounted)
const days = ref<DayItem[]>([
  { id: 1, name: '月', checked: false, type_weekday: 11, id_business_day: null },
  { id: 2, name: '火', checked: false, type_weekday: 12, id_business_day: null },
  { id: 3, name: '水', checked: false, type_weekday: 13, id_business_day: null },
  { id: 4, name: '木', checked: false, type_weekday: 14, id_business_day: null },
  { id: 5, name: '金', checked: false, type_weekday: 15, id_business_day: null },
  { id: 6, name: '土', checked: false, type_weekday: 16, id_business_day: null },
  { id: 7, name: '日', checked: false, type_weekday: 17, id_business_day: null },
  { id: 8, name: '祝日', checked: false, type_weekday: 18, id_business_day: null }
])

const closeModal = () => {
  emits('close')
}

onMounted(async () => {
  // Fetch data from API - only business hour slots and business days
  await businessHourSlotStore.fetchPreparationBusinessHourSlots()
  await businessHourSlotStore.fetchBusinessHourSlots({})
  await businessDayStore.fetchBusinessDays()

  // Set isUpdate based on the selectedDay prop
  isUpdate.value = !!props.selectedDay

  // Initialize with an empty business hour range
  businessHourRanges.value = [
    {
      start: '',
      end: '',
      id: 1
    }
  ]

  // Update days with data from API after fetch
  if (getBusinessDays.value && getBusinessDays.value.length > 0) {
    getBusinessDays.value.forEach((day: BusinessDay) => {
      const existingDayIndex = days.value.findIndex((d) => d.type_weekday === day.type_weekday)
      if (existingDayIndex !== -1) {
        days.value[existingDayIndex].id_business_day = day.id_business_day
      }
    })
  }

  // Map day.id to type_weekday
  const weekdayMap = {
    monday: 11,
    tuesday: 12,
    wednesday: 13,
    thursday: 14,
    friday: 15,
    saturday: 16,
    sunday: 17,
    holiday: 18,
    common: 1
  }

  let typeWeekday: number | null = null

  // If a specific day is selected, mark it as checked
  if (props.selectedDay) {
    typeWeekday = weekdayMap[props.selectedDay.id as keyof typeof weekdayMap]
    if (typeWeekday) {
      // Find and check the day in the days array
      const dayIndex = days.value.findIndex((day) => day.type_weekday === typeWeekday)
      if (dayIndex !== -1) {
        days.value[dayIndex].checked = true
      }
    }
  }

  if (businessHourSlotStore.getAllBusinessHourSlots) {
    businessHourSlots.value = businessHourSlotStore.getAllBusinessHourSlots.map((slot: BusinessHourSlot) => ({
      label: slot.label,
      value: slot.value
    }))
  }

  // Fetch booking item data for update state
  if (isUpdate.value && props.id_item_service) {
    try {
      // Fetch booking item data by filter
      await bookingItemStore.fetchBookingItemByFilter({
        id_item_service: props.id_item_service
      })

      // Process only if we have a booking item and type weekday
      if (bookingItemStore.currentBookingItem && typeWeekday) {
        // Find slots for the selected weekday
        let slots: BookingDaySlot[] = []

        // Get the appropriate slots based on the weekday
        const booking_day_slot_list = bookingItemStore.currentBookingItem.booking_day_slot_list
        if (booking_day_slot_list) {
          switch (typeWeekday) {
            case 1: // Common
              slots = booking_day_slot_list.common_slots || []
              break
            case 11: // Monday
              slots = booking_day_slot_list.monday_slots || []
              break
            case 12: // Tuesday
              slots = booking_day_slot_list.tuesday_slots || []
              break
            case 13: // Wednesday
              slots = booking_day_slot_list.wednesday_slots || []
              break
            case 14: // Thursday
              slots = booking_day_slot_list.thursday_slots || []
              break
            case 15: // Friday
              slots = booking_day_slot_list.friday_slots || []
              break
            case 16: // Saturday
              slots = booking_day_slot_list.saturday_slots || []
              break
            case 17: // Sunday
              slots = booking_day_slot_list.sunday_slots || []
              break
            case 18: // Holiday
              slots = booking_day_slot_list.special_day_list?.filter((slot) => slot.type_weekday === 99) || []
              break
          }
        }

        // If we have slots, populate the form with them
        if (slots.length > 0) {
          // Clear the default slot
          dataBookingSlot.value = []

          // Add each slot to the form
          slots.forEach((slot) => {
            dataBookingSlot.value.push({
              time_bookable_start: formatTimeForDisplay(slot.time_bookable_start),
              time_bookable_end: formatTimeForDisplay(slot.time_bookable_end),
              slot_max: slot.slot_max !== undefined ? slot.slot_max : null
            })
          })

          // If slots have consistent slot_max values, set the form.slot_max
          const slotMaxValues = dataBookingSlot.value.map((slot) => slot.slot_max)
          const uniqueSlotMaxValues = [...new Set(slotMaxValues)]

          if (uniqueSlotMaxValues.length === 1 && uniqueSlotMaxValues[0] !== null) {
            form.slot_max = uniqueSlotMaxValues[0]

            // Determine interval type based on slots
            if (slots.length > 1) {
              // Check if slots have consistent intervals
              const startTimes = slots.map((slot) =>
                convertTimeToMinutes(formatTimeForDisplay(slot.time_bookable_start))
              )
              const endTimes = slots.map((slot) => convertTimeToMinutes(formatTimeForDisplay(slot.time_bookable_end)))

              // Calculate interval for each consecutive pair
              const intervals = []
              for (let i = 0; i < startTimes.length - 1; i++) {
                if (endTimes[i] === startTimes[i + 1]) {
                  intervals.push(endTimes[i] - startTimes[i])
                }
              }

              // If all intervals are the same, set the interval type
              if (intervals.length > 0 && new Set(intervals).size === 1) {
                const interval = intervals[0]

                // Set interval type based on the calculated interval
                if (interval === 15) {
                  form.intervalType = '15'
                } else if (interval === 20) {
                  form.intervalType = '20'
                } else if (interval === 30) {
                  form.intervalType = '30'
                } else if (interval === 60) {
                  form.intervalType = '60'
                } else {
                  form.intervalType = 'custom'
                  form.intervalCustom = interval
                }
              }
            }
          }
        }
      }
    } catch (error) {
      mtUtils.autoCloseAlert('予約時間枠の取得に失敗しました。')
      console.error(error)
    }
  }
})

const isIpad = computed(() => {
  return Platform.is.ipad
})

// Add a computed property to check if any days are selected
const hasSelectedDays = computed(() => {
  return days.value.some((day) => day.checked)
})

// Add a computed property for slot max visibility
const showSlotMax = computed(() => {
  return form.intervalType !== 'none'
})

const businessForm = reactive({
  id_business_hour_slot: '' as string | number
})

const intervals = ref([
  { id: 1, name: 'なし', value: 'none' },
  { id: 2, name: '15分', value: '15' },
  { id: 3, name: '20分', value: '20' },
  { id: 4, name: '30分', value: '30' },
  { id: 5, name: '60分', value: '60' },
  { id: 6, name: '指定', value: 'custom' }
])

const form = reactive({
  intervalType: 'none',
  intervalCustom: null as null | number,
  slot_max: null as null | number
})

// Data booking slot
const dataBookingSlot = ref<
  {
    time_bookable_start: string
    time_bookable_end: string
    slot_max: number | null
  }[]
>([
  {
    time_bookable_start: '',
    time_bookable_end: '',
    slot_max: null
  }
])

const addBookingSlot = () => {
  // set default start from previous end
  if (dataBookingSlot.value.length > 0) {
    const previousEnd = dataBookingSlot.value[dataBookingSlot.value.length - 1].time_bookable_end
    const defaultStart = previousEnd ? formatTimeForDisplay(previousEnd) : ''
    const previousSlotMax = dataBookingSlot.value[dataBookingSlot.value.length - 1].slot_max
    const defaultSlotMax = form.intervalType === 'custom' ? form.intervalCustom : previousSlotMax

    dataBookingSlot.value.push({
      time_bookable_start: defaultStart,
      time_bookable_end: '',
      slot_max: defaultSlotMax
    })
  } else {
    dataBookingSlot.value.push({
      time_bookable_start: '',
      time_bookable_end: '',
      slot_max: null
    })
  }
}

const deleteBookingSlot = async (index: number) => {
  dataBookingSlot.value.splice(index, 1)
}

const openHelpMenu = () => {
  mtUtils.smallPopup(MtToolTipsSmall, {
    text: reservationHelperContents3.reservationViewPage.title,
    content: reservationHelperContents3.reservationViewPage.content
  })
}

// Add these helper functions before validateForm
const isValidTimeFormat = (time: string): boolean => {
  // Check if time matches HH:MM format and is a valid time
  const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/
  return timeRegex.test(time)
}

const convertTimeToMinutes = (time: string): number => {
  const [hours, minutes] = time.split(':').map(Number)
  return hours * 60 + minutes
}

const isSlotOverlapping = (slot1Start: string, slot1End: string, slot2Start: string, slot2End: string): boolean => {
  const start1 = convertTimeToMinutes(slot1Start)
  const end1 = convertTimeToMinutes(slot1End)
  const start2 = convertTimeToMinutes(slot2Start)
  const end2 = convertTimeToMinutes(slot2End)

  return start1 < end2 && start2 < end1
}

const validateForm = () => {
  // Basic validation - different for update vs create state
  if (!isUpdate.value) {
    // Only validate business hour slot in create mode
    if (!businessForm.id_business_hour_slot) {
      mtUtils.autoCloseAlert('診療時間枠を選択してください。')
      return false
    }

    // Validate business hour ranges in create mode
    let hasValidRange = false
    for (const range of businessHourRanges.value) {
      if (range.start && range.end) {
        hasValidRange = true

        // Validate time format
        if (!isValidTimeFormat(range.start) || !isValidTimeFormat(range.end)) {
          mtUtils.autoCloseAlert('時刻は HH:MM 形式で入力してください。')
          return false
        }

        // Check that end time is after start time
        if (range.start >= range.end) {
          mtUtils.autoCloseAlert('終了時刻は開始時刻より後である必要があります。')
          return false
        }

        // Check if duration is not more than 24 hours
        const startMinutes = convertTimeToMinutes(range.start)
        const endMinutes = convertTimeToMinutes(range.end)
        if (endMinutes - startMinutes > 24 * 60) {
          mtUtils.autoCloseAlert('診療時間は24時間を超えることはできません。')
          return false
        }
      } else if ((range.start && !range.end) || (!range.start && range.end)) {
        // Missing either start or end time
        mtUtils.autoCloseAlert('開始時刻と終了時刻の両方を設定してください。')
        return false
      }
    }

    if (!hasValidRange) {
      mtUtils.autoCloseAlert('少なくとも1つの有効な診療時間を設定してください。')
      return false
    }
  }

  // Check if any day is selected - required for both create and update
  const anyDaySelected = days.value.some((day) => day.checked)
  if (!anyDaySelected) {
    mtUtils.autoCloseAlert('少なくとも1日を選択してください。')
    return false
  }

  // Validate time slots - required for both create and update
  if (dataBookingSlot.value.length === 0) {
    mtUtils.autoCloseAlert('少なくとも1つの予約時間枠を設定してください。')
    return false
  }

  for (let i = 0; i < dataBookingSlot.value.length; i++) {
    const slot = dataBookingSlot.value[i]

    if (!slot.time_bookable_start || !slot.time_bookable_end) {
      mtUtils.autoCloseAlert('すべての時間枠に開始時刻と終了時刻を設定してください。')
      return false
    }

    // Validate time format
    if (!isValidTimeFormat(slot.time_bookable_start) || !isValidTimeFormat(slot.time_bookable_end)) {
      mtUtils.autoCloseAlert('時刻は HH:MM 形式で入力してください。')
      return false
    }

    // Check that end time is after start time
    if (slot.time_bookable_start >= slot.time_bookable_end) {
      mtUtils.autoCloseAlert('終了時刻は開始時刻より後である必要があります。')
      return false
    }

    // Check if duration is not more than 24 hours
    const startMinutes = convertTimeToMinutes(slot.time_bookable_start)
    const endMinutes = convertTimeToMinutes(slot.time_bookable_end)
    if (endMinutes - startMinutes > 24 * 60) {
      mtUtils.autoCloseAlert('予約時間枠は24時間を超えることはできません。')
      return false
    }

    // Check for overlapping slots
    for (let j = 0; j < dataBookingSlot.value.length; j++) {
      if (i !== j) {
        const otherSlot = dataBookingSlot.value[j]
        if (
          isSlotOverlapping(
            slot.time_bookable_start,
            slot.time_bookable_end,
            otherSlot.time_bookable_start,
            otherSlot.time_bookable_end
          )
        ) {
          mtUtils.autoCloseAlert('予約時間枠が重複しています。')
          return false
        }
      }
    }

    // Validate slot_max is a positive number or null
    if (slot.slot_max !== null && slot.slot_max !== undefined) {
      const slotMax = Number(slot.slot_max)
      if (isNaN(slotMax) || slotMax <= 0) {
        mtUtils.autoCloseAlert('予約可能枠数は正の数である必要があります。')
        return false
      }
    }
  }

  return true
}

// Add this function before submit
const checkExistingBookingSlots = async () => {
  try {
    // If we don't have an id_booking_item, it's a new item with no existing data
    if (!bookingItemStore.currentBookingItem?.booking_item.id_booking_item) {
      return false
    }

    // Get the current booking item data
    if (!bookingItemStore.currentBookingItem?.booking_day_slot_list) return false

    const {
      common_slots = [],
      monday_slots = [],
      tuesday_slots = [],
      wednesday_slots = [],
      thursday_slots = [],
      friday_slots = [],
      saturday_slots = [],
      sunday_slots = [],
      special_day_list = []
    } = bookingItemStore.currentBookingItem.booking_day_slot_list

    // Check if there are any existing slots for the selected days
    const selectedDays = days.value.filter((day) => day.checked)

    for (const day of selectedDays) {
      switch (day.type_weekday) {
        case 1: // Common for all days
          if (common_slots.length > 0) return true
          break
        case 11: // Monday
          if (monday_slots.length > 0) return true
          break
        case 12: // Tuesday
          if (tuesday_slots.length > 0) return true
          break
        case 13: // Wednesday
          if (wednesday_slots.length > 0) return true
          break
        case 14: // Thursday
          if (thursday_slots.length > 0) return true
          break
        case 15: // Friday
          if (friday_slots.length > 0) return true
          break
        case 16: // Saturday
          if (saturday_slots.length > 0) return true
          break
        case 17: // Sunday
          if (sunday_slots.length > 0) return true
          break
        case 18: // Holiday
          if (special_day_list.length > 0) return true
          break
      }
    }

    return false
  } catch (error) {
    console.error('Error checking existing booking slots:', error)
    return false
  }
}

const submit = async () => {
  if (!validateForm()) return

  try {
    // Check for existing data only when updating
    if (isUpdate.value) {
      const hasExistingData = await checkExistingBookingSlots()

      // Show confirmation dialog if there's existing data
      if (hasExistingData) {
        const confirmed = await mtUtils.confirm2({
          message: 'この操作は当該日のデータを上書きします。\n削除しますか？',
          title: '確認',
          okLabel: '削除',
          cancelBtn: true,
          cancelBtnLabel: 'キャンセル'
        })

        if (!confirmed) return
      }
    }

    // Organize booking slots by weekday based on the PRD structure
    const booking_day_slot_list = {
      common_slots: [] as BookingDaySlot[],
      monday_slots: [] as BookingDaySlot[],
      tuesday_slots: [] as BookingDaySlot[],
      wednesday_slots: [] as BookingDaySlot[],
      thursday_slots: [] as BookingDaySlot[],
      friday_slots: [] as BookingDaySlot[],
      saturday_slots: [] as BookingDaySlot[],
      sunday_slots: [] as BookingDaySlot[],
      special_day_list: [] as BookingDaySlot[]
    }

    // Add selected day slots based on user selections
    const selectedDays = days.value.filter((day) => day.checked)

    // Add all generated booking slots if they exist
    if (dataBookingSlot.value.length > 0) {
      // Process each booking slot in dataBookingSlot
      for (const slot of dataBookingSlot.value) {
        // Only process slots with both start and end time
        if (!slot.time_bookable_start || !slot.time_bookable_end) continue

        // For each selected day, create a slot with this booking time
        for (const day of selectedDays) {
          // Create slot entry for this day with this time range
          const slotEntry = {
            type_weekday: day.type_weekday,
            time_bookable_start: formatTimeForBackend(slot.time_bookable_start),
            time_bookable_end: formatTimeForBackend(slot.time_bookable_end),
            slot_max: slot.slot_max
          }

          // Add to the appropriate day slot list
          switch (day.type_weekday) {
            case 1: // Common for all days
              booking_day_slot_list.common_slots.push(slotEntry)
              break
            case 11: // Monday
              booking_day_slot_list.monday_slots.push(slotEntry)
              break
            case 12: // Tuesday
              booking_day_slot_list.tuesday_slots.push(slotEntry)
              break
            case 13: // Wednesday
              booking_day_slot_list.wednesday_slots.push(slotEntry)
              break
            case 14: // Thursday
              booking_day_slot_list.thursday_slots.push(slotEntry)
              break
            case 15: // Friday
              booking_day_slot_list.friday_slots.push(slotEntry)
              break
            case 16: // Saturday
              booking_day_slot_list.saturday_slots.push(slotEntry)
              break
            case 17: // Sunday
              booking_day_slot_list.sunday_slots.push(slotEntry)
              break
            case 18: // Holiday
              booking_day_slot_list.special_day_list.push({
                ...slotEntry,
                type_weekday: 99,
                date_booking_special: new Date().toISOString().split('T')[0]
              })
              break
          }
        }
      }
    }

    try {
      // Get the booking item ID if in update mode
      const bookingItemId = bookingItemStore.currentBookingItem?.booking_item.id_booking_item

      // If we have an existing booking item, update it using the dedicated endpoint
      const response = await bookingItemStore.updateBookingDaySlots(bookingItemId, {
        booking_day_slot_list: booking_day_slot_list
      })

      if (response) {
        mtUtils.autoCloseAlert('予約時間枠が更新されました。')
        if (props.init) {
          props.init()
        } else {
          emits('init')
        }
        closeModal()
      }
    } catch (error) {
      mtUtils.autoCloseAlert('予約時間枠の保存に失敗しました。もう一度お試しください。')
      console.error(error)
    }
  } catch (error) {
    mtUtils.autoCloseAlert('設定の保存に失敗しました。もう一度お試しください。')
    console.error(error)
  }
}

// Add a function to generate all booking slots based on current form settings
const generateBookingSlots = () => {
  if (!businessForm.id_business_hour_slot) {
    mtUtils.autoCloseAlert('診療時間枠を選択してください。')
    return
  }

  // Reset all data
  dataBookingSlot.value = []

  // First generate the base time slots from business hour slot
  generateTimeSlotsFromBusinessHour()

  // Then apply interval slots if needed
  if (form.intervalType !== 'none' && form.intervalType !== '') {
    generateIntervalSlots()
  }

  mtUtils.autoCloseAlert('時間枠が生成されました。')
}

// Function to format time from input (HH:MM) to HH:MM:00 for backend
const formatTimeForBackend = (time: string): string => {
  if (!time) return ''
  return `${time}:00`
}

// Function to format time from backend (HH:MM:SS) to HH:MM for display
const formatTimeForDisplay = (time: string): string => {
  if (!time) return ''
  return time.substring(0, 5)
}

// Function to generate time slots based on selected business hour slot
const generateTimeSlotsFromBusinessHour = () => {
  if (!businessForm.id_business_hour_slot) return

  // Reset the dataBookingSlot
  dataBookingSlot.value = []

  // Function to add a slot if time values exist
  const addSlotIfValid = (start: string, end: string) => {
    if (start && end && start !== '00:00' && end !== '00:00') {
      dataBookingSlot.value.push({
        time_bookable_start: formatTimeForDisplay(start),
        time_bookable_end: formatTimeForDisplay(end),
        slot_max: form.slot_max
      })
    }
  }

  // Use the edited business hour ranges instead of directly from the business hour slot
  for (const range of businessHourRanges.value) {
    if (range.start && range.end) {
      addSlotIfValid(range.start, range.end)
    }
  }

  // If no valid slots were added, add an empty slot
  if (dataBookingSlot.value.length === 0) {
    dataBookingSlot.value.push({
      time_bookable_start: '',
      time_bookable_end: '',
      slot_max: form.slot_max
    })
  }
}

// Function to initialize business hour ranges when a business hour slot is selected
const onBusinessHourSlotChange = () => {
  if (!businessForm.id_business_hour_slot) return

  // Find the selected business hour slot
  const selectedSlot = businessHourSlotStore.state.business_hour_slots.find(
    (slot: any) => slot.id_business_hour_slot === Number(businessForm.id_business_hour_slot)
  )

  if (selectedSlot) {
    initBusinessHourRanges(selectedSlot)
  }
}

// Watch for changes to the selected business hour slot
watch(
  () => businessForm.id_business_hour_slot,
  (newValue) => {
    if (newValue) {
      onBusinessHourSlotChange()
    } else {
      // Clear business hour ranges if no slot is selected
      businessHourRanges.value = [
        {
          start: '',
          end: '',
          id: 1
        }
      ]
    }
  }
)

// Function to generate interval slots
const generateIntervalSlots = () => {
  if (form.intervalType === 'none' || dataBookingSlot.value.length === 0) return

  // Calculate interval in minutes
  const intervalMinutes = form.intervalType === 'custom' ? Number(form.intervalCustom) : parseInt(form.intervalType)

  if (!intervalMinutes || intervalMinutes <= 0) return

  // Process each slot
  const newDataBookingSlot = []

  // Format time to HH:MM
  const formatTimeHHMM = (hours: number, minutes: number): string => {
    return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`
  }

  for (const slot of dataBookingSlot.value) {
    if (!slot.time_bookable_start || !slot.time_bookable_end) continue

    // Parse time strings to minutes since midnight
    let startHours = 0
    let startMinutes = 0
    let endHours = 0
    let endMinutes = 0

    // Parse HH:MM format
    if (slot.time_bookable_start.includes(':')) {
      const parts = slot.time_bookable_start.split(':')
      startHours = parseInt(parts[0])
      startMinutes = parseInt(parts[1])
    }

    if (slot.time_bookable_end.includes(':')) {
      const parts = slot.time_bookable_end.split(':')
      endHours = parseInt(parts[0])
      endMinutes = parseInt(parts[1])
    }

    const startTotalMinutes = startHours * 60 + startMinutes
    const endTotalMinutes = endHours * 60 + endMinutes

    // Generate intervals
    for (let time = startTotalMinutes; time < endTotalMinutes; time += intervalMinutes) {
      const nextTime = time + intervalMinutes
      if (nextTime > endTotalMinutes) break

      // Format times as HH:MM
      const startTime = formatTimeHHMM(Math.floor(time / 60), time % 60)
      const endTime = formatTimeHHMM(Math.floor(nextTime / 60), nextTime % 60)

      newDataBookingSlot.push({
        time_bookable_start: startTime,
        time_bookable_end: endTime,
        slot_max: form.slot_max
      })
    }
  }

  // Replace the slots if we generated any
  if (newDataBookingSlot.length > 0) {
    dataBookingSlot.value = newDataBookingSlot
  }
}

// Add a function to add more time ranges
const addTimeRange = () => {
  // Add a new empty slot
  dataBookingSlot.value.push({
    time_bookable_start: '',
    time_bookable_end: '',
    slot_max: form.slot_max
  })
}

// Add a handler for slot_max changes
const updateAllSlotMax = () => {
  dataBookingSlot.value.forEach((slot: { slot_max: number | null }) => {
    slot.slot_max = form.slot_max
  })
}

// Add an array to store editable business hours that can be updated by the user
const businessHourRanges = ref<
  {
    start: string
    end: string
    id: number
  }[]
>([])

// Initialize business hour ranges from selected slot
const initBusinessHourRanges = (selectedSlot: any) => {
  businessHourRanges.value = []

  // Add each valid time range from the business hour slot
  if (
    selectedSlot.time_business_start1 &&
    selectedSlot.time_business_end1 &&
    selectedSlot.time_business_start1 !== '00:00:00' &&
    selectedSlot.time_business_end1 !== '00:00:00'
  ) {
    businessHourRanges.value.push({
      start: formatTimeForDisplay(selectedSlot.time_business_start1),
      end: formatTimeForDisplay(selectedSlot.time_business_end1),
      id: 1
    })
  }

  if (
    selectedSlot.time_business_start2 &&
    selectedSlot.time_business_end2 &&
    selectedSlot.time_business_start2 !== '00:00:00' &&
    selectedSlot.time_business_end2 !== '00:00:00'
  ) {
    businessHourRanges.value.push({
      start: formatTimeForDisplay(selectedSlot.time_business_start2),
      end: formatTimeForDisplay(selectedSlot.time_business_end2),
      id: 2
    })
  }

  if (
    selectedSlot.time_business_start3 &&
    selectedSlot.time_business_end3 &&
    selectedSlot.time_business_start3 !== '00:00:00' &&
    selectedSlot.time_business_end3 !== '00:00:00'
  ) {
    businessHourRanges.value.push({
      start: formatTimeForDisplay(selectedSlot.time_business_start3),
      end: formatTimeForDisplay(selectedSlot.time_business_end3),
      id: 3
    })
  }

  // If no valid ranges, add an empty one
  if (businessHourRanges.value.length === 0) {
    businessHourRanges.value.push({
      start: '',
      end: '',
      id: 1
    })
  }
}

// Function to add a new time range
const addBusinessHourRange = () => {
  // set default start from previous end
  if (businessHourRanges.value.length > 0) {
    const previousEnd = businessHourRanges.value[businessHourRanges.value.length - 1].end
    const defaultStart = previousEnd ? formatTimeForDisplay(previousEnd) : ''

    businessHourRanges.value.push({
      start: defaultStart,
      end: '',
      id: businessHourRanges.value.length + 1
    })
  } else {
    businessHourRanges.value.push({
      start: '',
      end: '',
      id: 1
    })
  }
}

// Function to remove a time range
const removeBusinessHourRange = (index: number) => {
  if (businessHourRanges.value.length > 1) {
    businessHourRanges.value.splice(index, 1)
    // Update the IDs
    businessHourRanges.value.forEach((range, i) => {
      range.id = i + 1
    })
  } else {
    mtUtils.autoCloseAlert('少なくとも1つの時間枠が必要です。')
  }
}

// Add a function to clear all booking slots
const clearBookingSlots = () => {
  dataBookingSlot.value = [
    {
      time_bookable_start: '',
      time_bookable_end: '',
      slot_max: null
    }
  ]
  mtUtils.autoCloseAlert('すべての時間枠がクリアされました。')
}

// Add a watcher for the form.slot_max to update all slots
watch(
  () => form.slot_max,
  (newValue) => {
    if (newValue !== null) {
      updateAllSlotMax()
    }
  }
)

// Add a watcher for form.intervalType
watch(
  () => form.intervalType,
  (newValue) => {
    if (newValue === 'none') {
      // Clear slot_max if 'none' is selected
      form.slot_max = null
    } else if (newValue === 'custom') {
      // Initialize custom interval if needed
      if (!form.intervalCustom) {
        form.intervalCustom = 15
      }
    }
  }
)

// Add a watcher for days.value
watch(
  () => days.value,
  () => {
    // If no days are selected, show an alert or take other action
    if (!hasSelectedDays.value && dataBookingSlot.value.length > 1) {
      // Only show warning if we have booking slots defined
      mtUtils.autoCloseAlert('少なくとも1日を選択してください。', 'warning')
    }
  },
  { deep: true }
)

const openMenu = async () => {
  const menuOptions = [
    {
      title: 'URLコピー',
      name: 'url_copy',
      isChanged: false,
      attr: { class: null, clickable: true }
    },
    {
      title: '削除',
      name: 'delete',
      isChanged: false,
      attr: { class: null, clickable: true }
    }
  ]

  await mtUtils.littlePopup(OptionModal, {
    options: menuOptions
  })

  let selectedOption = menuOptions.find((i) => i.isChanged == true)

  if (selectedOption) {
    if (selectedOption.name == 'url_copy') {
      const url = window.location.href
      await navigator.clipboard.writeText(url)
      mtUtils.autoCloseAlert('URLをコピーしました。')
    } else if (selectedOption.name == 'delete') {
      // Map day.id to type_weekday
      const weekdayMap = {
        monday: 11,
        tuesday: 12,
        wednesday: 13,
        thursday: 14,
        friday: 15,
        saturday: 16,
        sunday: 17,
        holiday: 18,
        common: 1
      }

      // Get type_weekday from the selectedDay prop or from selected days if no specific day is provided
      let type_weekday = null

      if (props.selectedDay) {
        // Use the day provided from parent component
        type_weekday = weekdayMap[props.selectedDay.id as keyof typeof weekdayMap]
      } else {
        // Check if any days are selected in the UI
        const selectedDays = days.value.filter((day) => day.checked)
        if (selectedDays.length === 0) {
          mtUtils.autoCloseAlert('少なくとも1日を選択してください。')
          return
        }
        // Use the first selected day's type_weekday
        type_weekday = selectedDays[0].type_weekday
      }

      if (!type_weekday) {
        mtUtils.autoCloseAlert('削除する曜日の情報が不正です。')
        return
      }

      const confirmation = await mtUtils.confirm2({
        message: '指定曜日の設定を削除しますか？',
        title: '確認',
        okLabel: '削除',
        cancelBtn: true,
        cancelBtnLabel: 'キャンセル'
      })

      if (confirmation) {
        try {
          // If we have booking item id, delete booking day slot by type_weekday
          if (bookingItemStore.currentBookingItem?.booking_item.id_booking_item) {
            const response = await bookingItemStore.deleteBookingDaySlotsByFilter({
              type_weekday,
              id_booking_item: bookingItemStore.currentBookingItem?.booking_item.id_booking_item,
              id_clinic: props.id_clinic
            })

            if (response) {
              mtUtils.autoCloseAlert('削除しました。')
              if (props.init) {
                props.init()
              } else {
                emits('init')
              }
              closeModal()
            } else {
              mtUtils.autoCloseAlert('予約時間枠の削除に失敗しました。')
            }
          } else {
            // If no booking item exists yet, just close the modal
            mtUtils.autoCloseAlert('削除するデータがありません。')
            closeModal()
          }
        } catch (error) {
          mtUtils.autoCloseAlert('予約時間枠の削除に失敗しました。')
          console.error(error)
        }
      }
    }
  }
}

// show generator only for create case
const showGenerator = computed(() => {
  return !isUpdate.value
})
</script>

<template>
  <q-form @submit.prevent="submit">
    <MtModalHeader @closeModal="closeModal">
      <q-toolbar-title class="text-grey-900 title2 bold"> 予約商品設定 </q-toolbar-title>
      <template v-if="isUpdate">
        <q-btn @click="openMenu" icon="more_horiz" round size="24" flat>
          <q-tooltip> その他のオプション </q-tooltip>
        </q-btn>
      </template>
    </MtModalHeader>

    <q-card-section class="q-mt-md q-px-xl content" :class="{ 'q-mt-md': isIpad }">
      <div class="row q-col-gutter-md">
        <div class="col-12">
          <span class="body1 regular">設定曜日</span>
        </div>
        <div class="flex items-center col-12 q-gutter-x-md">
          <!-- checkbox days - show all days in create mode, only selected day in update mode -->
          <template v-if="isUpdate">
            <q-checkbox
              v-for="day in days.filter(
                (d) =>
                  props.selectedDay &&
                  d.type_weekday ===
                    (props.selectedDay.id === 'common'
                      ? 1
                      : props.selectedDay.id === 'monday'
                      ? 11
                      : props.selectedDay.id === 'tuesday'
                      ? 12
                      : props.selectedDay.id === 'wednesday'
                      ? 13
                      : props.selectedDay.id === 'thursday'
                      ? 14
                      : props.selectedDay.id === 'friday'
                      ? 15
                      : props.selectedDay.id === 'saturday'
                      ? 16
                      : props.selectedDay.id === 'sunday'
                      ? 17
                      : props.selectedDay.id === 'holiday'
                      ? 18
                      : null)
              )"
              :key="day.id"
              v-model="day.checked"
              :label="day.name"
              :disable="isUpdate"
            />
          </template>
          <template v-else>
            <q-checkbox v-for="day in days" :key="day.id" v-model="day.checked" :label="day.name" />
          </template>
        </div>
        <!-- Add warning if no days selected -->
        <div class="col-12 text-danger" v-if="!days.some((day) => day.checked)">
          <q-icon name="warning" class="q-mr-xs" />
          <span>少なくとも1日を選択してください。</span>
        </div>
      </div>

      <div v-if="showGenerator">
        <div class="q-mt-md row q-col-gutter-md">
          <div class="col-12">
            <span class="body1 regular">予約時間設定</span>
            <q-btn dense flat round @click="openHelpMenu" class="q-mx-sm grey-800">
              <q-icon size="24px" name="help_outline" />
            </q-btn>
          </div>
        </div>
        <div class="row q-col-gutter-md">
          <div class="col-4">
            <span class="text-grey-500 body2 regular">マスタの診療時間枠を設定する。</span>
          </div>
          <div class="col-4">
            <q-select
              v-model="businessForm.id_business_hour_slot"
              :options="businessHourSlots"
              required
              label="診療時間枠"
              autofocus
              emit-value
              map-options
              dense
              clearable
            />
          </div>
        </div>

        <!-- Remove the time range selection from the UI -->
        <div class="q-mt-md row q-col-gutter-md">
          <div class="col-12">
            <span class="body1 regular">診療時間</span>
          </div>
        </div>

        <!-- Business hour ranges display -->
        <div class="bg-grey-2 q-mb-md q-pa-md">
          <div v-for="(range, index) in businessHourRanges" :key="range.id" class="q-mb-sm row q-col-gutter-md">
            <div class="col-4">
              <!-- Replace with MInputTime component -->
              <MInputTime v-model="range.start" :label="`開始時刻 ${range.id}`" :outlined="false" />
            </div>
            <span class="flex items-center q-mx-sm">~</span>
            <div class="col-4">
              <!-- Replace with MInputTime component -->
              <MInputTime v-model="range.end" :label="`終了時刻 ${range.id}`" :outlined="false" />
            </div>
            <div class="col-2">
              <q-btn
                dense
                round
                flat
                color="negative"
                icon="delete"
                @click="removeBusinessHourRange(index)"
                :disable="businessHourRanges.length <= 1"
              />
            </div>
          </div>

          <!-- Add button for business hour range -->
          <div class="justify-end q-mt-md row">
            <q-btn color="primary" dense outline icon="add" label="時間枠を追加" @click="addBusinessHourRange" />
          </div>
        </div>

        <div class="q-mt-md row q-col-gutter-md">
          <div class="flex items-center col-12">
            <span class="body1 regular">区切り間隔（分単位）</span>
            <q-btn dense flat round @click="openHelpMenu" class="q-mx-sm grey-800">
              <q-icon size="24px" name="help_outline" />
            </q-btn>
          </div>
        </div>

        <div class="row q-col-gutter-md">
          <div class="flex items-center col-12 q-gutter-x-sm">
            <q-radio
              v-for="interval in intervals"
              :key="interval.id"
              v-model="form.intervalType"
              :val="interval.value"
              :label="interval.name"
            />
            <q-input
              v-if="form.intervalType === 'custom'"
              label="間隔(分)"
              v-model="form.intervalCustom"
              type="number"
              style="width: 30%"
            />
          </div>
        </div>
        <div class="q-mt-md row q-col-gutter-md">
          <div class="col-4">
            <q-input
              label="予約可能枠数"
              type="number"
              v-model="form.slot_max"
              clearable
              dense
              @update:model-value="updateAllSlotMax"
            />
          </div>
        </div>

        <!-- Add section for the generate button -->
        <div class="q-mt-md row q-col-gutter-md">
          <div class="col-12">
            <q-btn
              unelevated
              color="primary"
              class="q-ml-md"
              @click="generateBookingSlots"
              :disable="!businessForm.id_business_hour_slot"
            >
              <span>時間枠を生成</span>
            </q-btn>
            <!-- Add clear button -->
            <q-btn
              outline
              color="negative"
              class="q-ml-md"
              @click="clearBookingSlots"
              :disable="dataBookingSlot.length <= 1 && !dataBookingSlot[0].time_bookable_start"
            >
              <span>時間枠をクリア</span>
            </q-btn>
          </div>
        </div>
      </div>

      <!-- booking slot -->
      <div class="bg-grey-100 q-mt-md q-py-lg">
        <div class="q-px-md flex justify-between">
          <span class="body1 regular">予約可能時間枠</span>
          <span class="text-grey-500 caption1" v-if="dataBookingSlot.length === 0">
            時間枠を生成ボタンを押して、時間枠を作成してください。
          </span>
        </div>

        <!-- No slots message -->
        <div v-if="dataBookingSlot.length === 0" class="q-pa-lg text-center text-grey-500">
          <q-icon name="schedule" size="48px" class="q-mb-md" />
          <div>時間枠がありません。時間枠を生成するか、手動で追加してください。</div>
        </div>

        <template v-else>
          <div v-for="(slot, index) in dataBookingSlot" :key="index" class="justify-center q-mt-md row q-col-gutter-md">
            <div class="col-3">
              <MInputTime :label="'開始時刻 ' + (index + 1)" v-model="slot.time_bookable_start" :outlined="false" />
            </div>
            <span class="q-mx-sm">~</span>
            <div class="col-3">
              <MInputTime :label="'終了時刻 ' + (index + 1)" v-model="slot.time_bookable_end" :outlined="false" />
            </div>
            <div class="col-3">
              <q-input label="予約可能枠数" type="number" v-model="slot.slot_max" clearable dense />
            </div>
            <!-- delete button -->
            <q-btn
              dense
              flat
              @click="deleteBookingSlot(index)"
              :disable="dataBookingSlot.length <= 1"
              class="text-negative"
            >
              <q-icon size="24px" name="close" />
            </q-btn>
          </div>

          <!-- add booking slot -->
          <div class="justify-center q-mt-md row q-col-gutter-md">
            <div class="col-4">
              <q-btn dense outline rounded icon="add" padding="sm xl" class="bg-grey-3" @click="addBookingSlot" />
            </div>
            <div class="col-3"></div>
            <div class="col-3"></div>
          </div>
        </template>
      </div>
    </q-card-section>

    <!-- footer -->
    <q-card-section class="bg-white q-bt">
      <div class="text-center modal-btn">
        <q-btn outline class="bg-grey-100 text-grey-800" @click="closeModal()">
          <span>キャンセル</span>
        </q-btn>
        <q-btn unelevated color="primary" class="q-ml-md" type="submit">
          <span>保存</span>
        </q-btn>
      </div>
    </q-card-section>
  </q-form>
</template>

<style lang="scss" scoped>
.underline-input {
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 0;
}
</style>
