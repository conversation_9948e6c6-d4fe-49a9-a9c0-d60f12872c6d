import { ref } from 'vue'
import {
  RoomType,
  ClinicType
} from '@/types/types'

import useRoomStore from '@/stores/rooms'
import useClinicStore from '@/stores/clinics'

//constants
const QUEUE_STATUS = {
  WAITING: 1 as number,
  IN_PROGRESS: 2 as number,
  CALLED: 3 as number,
  ABSENT: 4 as number,
} as const
const REFRESH_INTERVAL = 7 * 1000
const CLEAR_TICKET_CALLING_SECONDS = 30 * 1000

// states
const timeoutId = ref<ReturnType<typeof setTimeout> | null>(null)
const intervalId = ref<ReturnType<typeof setInterval> | null>(null)
const isQtCalling = ref(false)
const isQtToBeCalled = ref(false)
const callingQt = ref(null)

const clearTimeoutAndIntervals = () => {
  if (intervalId.value) clearInterval(intervalId.value)
  if (timeoutId.value) clearTimeout(timeoutId.value)
}

const qtToBeCalledPromise = () => {
  const selectedClinicId = localStorage.getItem('id_clinic')
  const selectedClinic = useClinicStore().getClinics.find((clinic: ClinicType) => clinic.id_clinic === parseInt(selectedClinicId || ''))
  const storedType = localStorage.getItem('typeWaitingScreen')
  const typeRoomWaitScreen = storedType ? JSON.parse(storedType) : selectedClinic?.type_waiting_room_screen
  const QUEUE_TICKET_TO_CALL_SECONDS = (typeRoomWaitScreen == 2 ? 5 : 6) * 1000

  return new Promise((resolve) => {
    setTimeout(() => {
      isQtToBeCalled.value = false
      resolve(true)
    }, QUEUE_TICKET_TO_CALL_SECONDS)
  })
}

const getRoomName = (idRoom: number) => {
  return useRoomStore().getAllRooms.find((room: RoomType) => room.id_room == idRoom)?.name_room
}

const fetchRooms = async () => {
  await useRoomStore().fetchPreparationRooms()
}

export function useQueueTicketUtils() {
  return {
    intervalId,
    timeoutId,
    QUEUE_STATUS,
    REFRESH_INTERVAL,
    CLEAR_TICKET_CALLING_SECONDS,
    isQtCalling,
    isQtToBeCalled,
    callingQt,
    clearTimeoutAndIntervals,
    qtToBeCalledPromise,
    getRoomName,
    fetchRooms
  }
}