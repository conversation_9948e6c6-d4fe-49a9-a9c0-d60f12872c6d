<script lang="ts" setup>
import { onMounted, ref, reactive, computed, watch } from 'vue'
import { aahUtilsGetEmployeeName, concatenate, formatDateTime, formatHoursMinutes, getDateTimeNow, timeDifferences } from '@/utils/aahUtils'
import MtInputForm from '@/components/form/MtInputForm.vue'
import InputEmployeeOptGroup from '@/components/form/InputEmployeeOptGroup.vue'
import MtFilterSelect from '@/components/MtFilterSelect.vue'
import useEmployeeStore from '@/stores/employees'
import useCustomerStore from '@/stores/customers'
import { storeToRefs } from 'pinia'
import { CliCommon, GenericValueLabelType, MessageThreadType } from '@/types/types'
import { typeLinkCategory, typeThreadClassification, typeFilterThreadMessages } from '@/utils/enum'
import useCliCommonStore from '@/stores/cli-common'

type PropsDataType = {
  type_department: number
  typeMessage: number
  messageTextarea: string
  id_file: null
  id_employee: string
  name_employee: string
  id_employee_insert: string
  filedata: []
}


type FilterDataType = {
  type_department: number[] | null
  flg_goal_achieved: boolean | null
  flg_closed: boolean | null
  name_thread: string | null
  number_link1: string | null
  id_customer: string | null
  id_pet: string | null
  id_employee_ask: string | null
  id_employee_answer: string | null
}

const props = withDefaults(
  defineProps<{
    data: PropsDataType
    drawer: boolean
    allTypeThreads: MessageThreadType[]
    selectedThread: MessageThreadType
    showBadgeFilter: FilterDataType
  }>(),
  {
    drawer: true,
    allTypeThreads: () => {
      return [] as MessageThreadType[]
    },
    data: () => {
      return {} as PropsDataType
    },
    selectedThread: () => {
      return {} as MessageThreadType
    },
    showBadgeFilter: () => {
      return {} as FilterDataType
    }
  }
)

// This computed property will reactively generate the array of active filter keys
const showBadgeFilter = computed(() => {
  // Ensure filterData.value exists and is an object
  if (!props.showBadgeFilter || typeof props.showBadgeFilter !== 'object') {
    return [];
  }

  // Filter keys where the corresponding value is truthy
  const keys = Object.keys(props.showBadgeFilter).filter((key) => {
    const value = props.showBadgeFilter[key];
    if (value === null || typeof value === 'undefined' || value === false) {
      return false; // Exclude null, undefined, and explicit false
    }
    if (typeof value === 'string' && value.trim() === '') {
      return false; // Exclude empty strings
    }
    // For numbers, if 0 means 'not selected', you might want to exclude it
    // if (typeof value === 'number' && value === 0) {
    //   return false;
    // }
    if (Array.isArray(value) && value.length === 0) {
      return false; // Exclude empty arrays
    }
    // If it reaches here, the value is considered an "active" filter
    return true;
  });

  return keys;
});

const cliCommonStore = useCliCommonStore()
const customerStore = useCustomerStore()
const employeeStore = useEmployeeStore()
const { getCustomerListOptions } = storeToRefs(customerStore)
const drawerHeader = ref()
const filterMessageToggle = ref(0)
const employeeId = Number(JSON.parse(localStorage?.getItem('id_employee')))
const employeeDepartement = Number(JSON.parse(localStorage?.getItem('userTypeDepartment')))
const filteredSelectedThread = computed(() => {
  let threadsToFilter = [];

  // Step 1: Normalize the incoming threads data into a flat array
  threadsToFilter = props.allTypeThreads;

  // Step 2: Apply conditional filtering based on filterMessageToggle
  if (filterMessageToggle.value === 1 || filterMessageToggle.value === 0) {
    // Show only data related to the logged-in user
    if (employeeId === null || typeof employeeId === 'undefined') {
        console.warn("Logged in user ID is not available for filtering based on toggle 1.");
        // If employeeId is missing, we can't filter by it.
        // The user's request implies a fallback if the result is empty.
        // For now, we'll proceed with filtering based on department if possible,
        // or return an empty array if no department match is found.
    }

    const filteredThreads = threadsToFilter.filter(thread => {
        if (typeof thread !== 'object' || thread === null) {
            return false;
        }
        // Check if the thread is related to the employeeId (if available)
        // OR if the thread's department matches the employee's department
        const isRelatedToEmployeeId = (employeeId !== null && typeof employeeId !== 'undefined') &&
            (thread.id_employee_achieved === employeeId ||
             thread.id_employee_answer === employeeId ||
             thread.id_employee_ask === employeeId);

        const isRelatedToEmployeeDepartment = thread.type_department === employeeDepartement;
        if (isRelatedToEmployeeId || isRelatedToEmployeeDepartment) {
          filterMessageToggle.value = 1 
        }
        return isRelatedToEmployeeId || isRelatedToEmployeeDepartment;
    });
    return filteredThreads;
  } else if (filterMessageToggle.value === 2) {
    filterMessageToggle.value = 2 
    // Show all data, no filtering needed based on user ID
    return threadsToFilter;
  } else {
    // Default case or handle other toggle values if any
    console.warn("Unknown filterMessageToggle value:", filterMessageToggle.value);
    return threadsToFilter; // Or [] depending on desired default behavior
  }
});

watch(
  filteredSelectedThread,
  (newValue, oldValue) => {
    setTimeout(() => {
      drawerHeaderHeight.value = drawerHeader?.value?.offsetHeight + 18
    }, 10)
    drawerHeaderHeight.value = drawerHeader?.value?.offsetHeight + 18
    // If the filtered array is empty, change filterMessageToggle.value to 2
    if (filteredSelectedThread.value.length === 0) {
        console.log("No threads found for filterMessageToggle.value === 1. Setting filterMessageToggle.value to 2.");
        filterMessageToggle.value = 2;
    } else {
        filterMessageToggle.value = 1;
    }
  },
  {
    once: true // This is the magic option from vue to only call watch once!
  }
);

const filterData = ref<FilterDataType>({
  type_department: null,
  flg_goal_achieved: false,
  flg_closed: false,
  name_thread: null,
  number_link1: null,
  id_customer: null,
  id_pet: null,
  id_employee_ask: null,
  id_employee_answer: null
})

const emits = defineEmits<{
  (e: 'handleDrawer'): void
  (e: 'handleFlgPinned', value: MessageThreadType): void
  (e: 'handleRefreshClick'): void
  (e: 'openUpdateModal'): void
  (e: 'openThreadFilterModal'): void
  (e: 'selectedFilter', value: FilterDataType): void
  (e: 'setSelectedThread', value: MessageThreadType): void
}>()

const defaultEmployee = JSON.parse(localStorage.getItem("id_employee"))
const typeDepartments = ref<Array<GenericValueLabelType>>([])
const typeDepartmentsDefault = reactive<Array<GenericValueLabelType>>([])
const petList = ref<Array<GenericValueLabelType>>([])
const petListDefault = reactive<Array<GenericValueLabelType>>([])
const showPets = ref(false)
const drawerHeaderHeight = ref(0)

const clearFilter = () => {
  filterData.value = {
    type_department: null,
    flg_goal_achieved: false,
    flg_closed: false,
    name_thread: null,
    number_link1: null,
    id_customer: null,
    id_pet: null,
    id_employee_ask: null,
    id_employee_answer: null
  }

  const keys = Object.keys(filterData.value).filter((key) => {
    return filterData.value[key as keyof FilterDataType]
  })
  // props.showBadgeFilter = [...keys]

  emits('selectedFilter', filterData.value)
}

const setFilter = () => {
  const keys = Object.keys(filterData.value).filter((key) => {
    return filterData.value[key as keyof FilterDataType]
  })
  // props.showBadgeFilter = [...keys]

  emits('selectedFilter', filterData.value)
}

const handleDrawer = () => {
  emits('handleDrawer')
}

const handleEmpName = (value: string) => {
  return aahUtilsGetEmployeeName(employeeStore.getAllEmployees, value)
}

const handleFlgPinned = (value: MessageThreadType) => {
  emits('handleFlgPinned', value)
}

const handleRefreshClick = () => {
  emits('handleRefreshClick')
}

const handleThreadType = (value: number) => {
  return typeThreadClassification.find(
    (items: any) => items.value === value
  )?.label
}

const openUpdateModal = () => {
  emits('openUpdateModal')
}

const setSelectedThread = (value: MessageThreadType) => {
  emits('setSelectedThread', value)
}

const handleLinkType = (Link: any) => {
  return typeLinkCategory.find(
    (items: any) => items.value === Link?.type_link1
  )?.label
}

const format = (value: string) => {
  const diff = timeDifferences(
    value,
    getDateTimeNow(),
    'hours'
  )

  if (diff < 0) {
    return `${formatDateTime(value)} ${formatHoursMinutes(
      value
    )}`
  }

  return formatHoursMinutes(value)
}

const handlePetsList = async (value: any) => {
  await customerStore.selectCustomer(value)
  if (value) {
    const selectedCustomer = customerStore?.getCustomer
    if (selectedCustomer) {
      if (selectedCustomer.pets.length) {
        petListDefault.length = 0
        selectedCustomer.pets.map((petObj: any) => {
          petListDefault.push({
            label: concatenate(
              petObj.code_pet,
              selectedCustomer.name_family,
              petObj.name_pet
            ),
            value: petObj.id_pet
          })
        })
        petList.value = [...petListDefault]
        if (petList.value.length) {
          filterData.value.id_pet = petList.value[0].value
          showPets.value = true
        }
      }
    }
  } else {
    filterData.value.id_pet = ''
    petList.value.length = 0
    petListDefault.length = 0
  }
}

const selectDefaultEmployee = (key: string) => {
  filterData.value[key as keyof FilterDataType] = defaultEmployee
}

const typeDeptName = (value: number) =>
  typeDepartments.value.find((v) => v.value == value)
    ?.name_cli_common

const openFilterModal = async () => {
  emits('openThreadFilterModal')
}

onMounted(async () => {
  typeDepartments.value = [...cliCommonStore.getCliCommonTypeDepartmentList.map((item: CliCommon) => ({
    ...item,
    value: item.code_func1,
    label: item.name_cli_common
  }))]
  typeDepartmentsDefault.push(...typeDepartments.value)
  drawerHeaderHeight.value = drawerHeader?.value?.offsetHeight + 18
})
window.onresize = () => {
  drawerHeaderHeight.value = drawerHeader?.value?.offsetHeight + 18
}
</script>

<template>
  <q-drawer
    v-model="props.drawer"
    show-if-above
    :width="350"
    :breakpoint="500"
    class="drawer-border overflow-hidden clinic-drawer"
  >
    <div ref="drawerHeader" :key="filterMessageToggle" class="absolute bg-white drawerHeaderBox">
      <q-toolbar class="bg-grey-white text-grey-800 pt-15px">
        <q-toolbar-title class="title2 bold">
          <div class="row items-center row-item-drawer">
            <q-btn
              @click="openFilterModal"
              round
              unelevated
              padding="3px 3px"
              text-color="black"
              class="q-ml-auto">
              <q-icon size="24px" name="filter_list" />
              <q-badge class="mt-badge" v-if="showBadgeFilter.length" color="red" rounded floating />
              
            </q-btn>
            <q-btn
              @click="openUpdateModal"
              round
              unelevated
              padding="3px 3px"
              text-color="black"
              class="q-ml-md"
            >
              <q-icon size="24px" name="add" />
            </q-btn>
            <q-btn
              @click="handleRefreshClick"
              round
              unelevated
              text-color="black"
              padding="3px 3px"
              class="q-ml-md"
            >
              <q-icon size="24px" name="refresh" />
            </q-btn>
            <q-btn
              @click="handleDrawer"
              round
              unelevated
              padding="3px 3px"
              text-color="black"
              class="q-ml-md"
            >
              <q-icon size="28px" name="chevron_left" />
            </q-btn>
          </div>
        </q-toolbar-title>
      </q-toolbar>
      
    </div>
    <q-separator color="grey-800" />
    <q-scroll-area
      class="scrollBox"
      :style="{ marginTop: `${drawerHeaderHeight}px` }"
    >
      <q-btn-toggle v-model="filterMessageToggle" unelevated spread dense no-caps
        color="white" text-color="primary" :options="typeFilterThreadMessages" />
      <q-list v-if="props.selectedThread" class="q-mb-xl">
        <q-item
          clickable
          v-ripple
          v-for="items in filteredSelectedThread"
          :key="items?.id_message_thread"
          :class="
            props.selectedThread?.id_message_thread === items?.id_message_thread
              ? 'selecte_Thread'
              : 'not_selecte_Threads'
          "
          @click="setSelectedThread(items)"
        >
          <q-item-section>
            <div class="row items-center threadTypeLink">
              <div class="col-sm-8">
                <div class="status-container">
                  <div>
                    <q-chip dense class="text-white status-chip urgent-chip " v-show="items.flg_urgent">
                      <div class="text-xs text-white ">至急</div>
                    </q-chip>
                  </div>
                  <div class="status-information">
                    <!-- flg_goal_achieved -->
                    <q-chip
                      dense
                      class="status-chip"
                      text-color="white"
                      :class="
                        items?.flg_goal_achieved
                          ? 'achieved-chip'
                          : 'unachieved-chip'
                      "
                    >
                      <span class="">
                        {{ items?.flg_goal_achieved ? '達成' : '未達成' }}
                      </span>
                    </q-chip>
                  </div>
                  <div>
                    <q-chip dense class="text-white thread-type-chip status-chip"  v-if="items?.type_link1">
                      <div class="text-xs text-white ">{{ handleLinkType(items) }}</div>
                    </q-chip>
                  </div>
                  <div>
                    <q-chip dense class="text-white closed-chip status-chip" v-if="items?.flg_closed">
                      <div class="text-xs text-white ">終了</div>
                    </q-chip>
                  </div>
                </div>
              </div>
              <div class="col">
                <div class="flex items-center no-wrap justify-end">
                  <div
                    v-if="items?.datetime_update"
                    class="caption1 text-no-wrap regular text-grey-700 q-pr-xs"
                  >
                    {{ format(items?.datetime_update) }}
                  </div>
                  <div>
                    <q-btn
                      round
                      unelevated
                      :text-color="
                        items?.flg_emr_pinned ? 'black' : 'grey-400'
                      "
                      padding="3px"
                      @click="handleFlgPinned(items)"
                    >
                      <q-icon
                        size="16px"
                        name="push_pin"
                      />
                    </q-btn>
                  </div>
                </div>
              </div>
            </div>
            <div class="flex no-wrap items-center q-my-xs">
              <div v-if="handleThreadType(items?.type_thread)" class="caption2 text-grey-700 flex no-wrap" :style="handleThreadType(items?.type_thread).length > 4 ? 'min-width: 70px;' : 'min-width: 34px;'">
                [{{ handleThreadType(items?.type_thread) }}]
              </div>
              <div class="title3 bold text-grey-900 q-ml-xs truncate-lines lines-1">
                {{ items.name_thread }}
              </div>
            </div>
            <div class="row no-wrap items-center justify-between">
              <div class="col-auto">
                <div
                  v-if="items.id_employee_ask || items.id_employee_answer"
                  class="flex items-center"
                >
                  <span class="body2 regular text-grey-700">{{
                    handleEmpName(items?.id_employee_ask)
                  }}</span>
                  <span v-if="items.id_employee_ask && items.id_employee_answer" class="text-grey-700 ellipsis q-mx-xs">→</span>
                  <span
                    class="body2 regular text-grey-700 ellipsis"
                    :class="items?.flg_urgent ? 'flgUrgentBox' : ''"
                  >
                    {{
                      items?.id_employee_ask
                        ? handleEmpName(items?.id_employee_answer)
                        : '犬舎'
                    }}</span>
                    <!-- if id_employee_answer empty just show departement name -->
                    <span v-if="items.id_employee_ask && !items.id_employee_answer && items.type_department" class="text-grey-700 ellipsis q-mx-xs">→</span>
                    <span v-if="!items.id_employee_answer" class="text-grey-700 ellipsis">
                      {{ typeDeptName(items.type_department) }}
                    </span>
                </div>
              </div>
            </div>
          </q-item-section>
        </q-item>
      </q-list>
      <div v-if="!allTypeThreads.length" class="text-center noThreads">
        スレッドがありません
      </div>
    </q-scroll-area>
  </q-drawer>
</template>

<style lang="scss">
.q-drawer {
  z-index: 5;
}
.selecte_Thread {
  padding: 8px 9px 10px 10px;
  border-bottom: 0.1px solid #dddd;
  background-color: #dddd !important;
}
.not_selecte_Threads {
  padding: 8px 9px 10px 10px;
  border-bottom: 0.1px solid #dddd;
  background-color: white !important;
}
.goal-achieved-bg {
  background-color: $positive;
}
.goal-unachieved-bg {
  background-color: #b99b14;
}
.drawerHeaderBox {
  width: 350px;
  border-right: 1px solid #e7e7e7;
}
.filterWidth {
  max-width: 150px;
}
.scrollBox {
  height: 90%;
  border-top: 1px solid #dddd;
  @media screen and (max-width: 1024px) {
    // for Ipad
    height: 90%;
  }
}
.threadTypeLink {
  height: 21px;
  .status-container {
    display: flex;
    align-items: center;
    .status-information {
      display: flex;
      gap: 4px;
      align-items: center;
      .status-goal-information {
        color: $white;
        padding: 2px 5px;
        border-radius: 3px;
      }
    }
  }
}

.flgUrgentBox {
  max-width: 155px;
}
.typeThreadBox {
  padding: 1.5px 5px;
}
.noThreads {
  margin-top: 250px;
}
.filter-menu {
  width: 750px;
  @media screen and (max-width: 500px) {
    width: 350px;
  }
}
.status-chip {
  font-size: 12px;
  padding: 0px 12px;
  &.urgent-chip {
    background-color: #BE0123;
  }

  &.achieved-chip {
    background-color: #34c759;
  }

  &.unachieved-chip {
    background-color: #EC9819;
  }

  &.closed-chip {
    background-color: $primary;
  }

  &.thread-type-chip {
    background-color: #45589C;
  }

  :deep(.q-chip__content) {
    padding: 0 8px;
  }
}

.pt-15px {
  padding-top: 15px
}

.row-item-drawer {
  height: 37px;
  // margin-top: -2spx;
}
.mt-badge {
  margin-top: 2px;
}
</style>
