<script setup lang="ts">
import { computed, onMounted } from 'vue'
import useClinicStore from '@/stores/clinics'
import { useQueueTicketUtils } from './queueTicketUtils'
import { storeToRefs } from 'pinia'

const {
  isQtCalling,
  isQtToBeCalled,
  callingQt,
  getRoomName,
} = useQueueTicketUtils()

const WAITING_SCREEN_GIF_TYPE1 = 'https://assets.vetty.clinic/waiting-room-screen1.gif'
const WAITING_SCREEN_GIF_TYPE2 = 'https://assets.vetty.clinic/waiting-room-screen2.gif'

const clinicStore = useClinicStore()
const { getClinics } = storeToRefs(clinicStore)

const getWaitingScreenUrl = () => {
  const selectedClinicId = localStorage.getItem('id_clinic')
  const selectedClinic = getClinics.value.find((clinic) => clinic.id_clinic === parseInt(selectedClinicId))
  const storedType = localStorage.getItem('typeWaitingScreen')
  const typeRoomWaitScreen = storedType ? JSON.parse(storedType) : selectedClinic?.type_waiting_room_screen
  if(typeRoomWaitScreen === 2) return WAITING_SCREEN_GIF_TYPE2
  return WAITING_SCREEN_GIF_TYPE1
}

onMounted(() => {
  const img1 = new Image()
  img1.src = WAITING_SCREEN_GIF_TYPE1
  const img2 = new Image()
  img2.src = WAITING_SCREEN_GIF_TYPE2
})

</script>
<template>
   <transition name="fade">
    <div class="flex justify-center items-center ticket-to-call-gif" v-if="isQtToBeCalled">
      <img :src="getWaitingScreenUrl()" width="100%" height="100%" />
    </div>
  </transition>
  <transition name="fade">
    <div class="qtcalling-screen-container q-pa-md" v-if="isQtCalling">
      <div class="text-center calling-text text-weight-bold">お呼び出し中</div>
      <div class="text-center ticket-number text-weight-bold">{{ callingQt.number_queue_ticket}}</div>
      <div class="text-center room-number text-weight-bold" v-if="callingQt.queue_detail?.id_room">
        <span class="room-text">ROOM</span> 
        {{ getRoomName(callingQt.queue_detail?.id_room) }}
      </div>
    </div>
  </transition>
</template>
<style src="./WaitingScreens.scss" lang="scss" scoped></style>