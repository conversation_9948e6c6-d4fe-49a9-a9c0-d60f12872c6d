<script lang="ts" setup>
import { nextTick, onMounted, ref, computed } from 'vue'
// @ts-ignore
import PdfExport from '@/pages/pdfExport/PdfExport.vue'
import useClinicStore from '@/stores/clinics'
import { dateFormat, formatDate, getDateToday } from '@/utils/aahUtils'
import { date } from 'quasar'

// SVG icons for work status
const ICONS = {
  CIRCLE: `<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M12 2C6.47 2 2 6.47 2 12s4.47 10 10 10 10-4.47 10-10S17.53 2 12 2z"/></svg>`,
  RADIO_BUTTON_UNCHECKED: `<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"/></svg>`
}

// Define interfaces for the API response structure
interface CalendarHeader {
  date: string;
  date_day: string;
  day: string;
  is_business_day: boolean;
  is_holiday: boolean;
  holiday_name: string;
}

interface Employee {
  id: number;
  name_display: string;
  type_occupation: string;
  display_order: number;
}

interface WorkScheduleItem {
  date: string;
  work_status: 'none' | 'off' | 'part_time' | 'full_time';
  work_hours: number;
  rest_minutes: number;
  work_start: string | null;
  work_end: string | null;
}

interface Counts {
  full_time: number;
  part_time: number;
  off: number;
}

interface EmployeeWorkSchedule {
  employee_id: number;
  type_occupation: string;
  employee_work_schedule: WorkScheduleItem[];
  display_order: number;
  counts: Counts;
}

interface SummaryItem {
  date: string;
  total: number;
}

interface Summary {
  doctor_works: SummaryItem[];
  nurse_works: SummaryItem[];
  reception_works: SummaryItem[];
  total_employee: SummaryItem[];
}

interface WorkSchedulePdfData {
  clinic: {
    id: number;
    name: string;
    date_start_cycle: number;
  };
  title: string;
  calendar_headers: CalendarHeader[];
  employees: Employee[];
  employee_work_schedules: EmployeeWorkSchedule[];
  summary: Summary;
}

const clinicStore = useClinicStore()

const emits = defineEmits(['close'])
const props = defineProps({
  data: {
    type: Object as () => WorkSchedulePdfData,
    default: () => ({})
  },
  workSchedulePdfAttributes: { 
    type: Object,
    default: () => ({ render: false })
  },
  callback: {
    type: Function,
    default: null
  },
  flgDownloadPdf: {
    type: Boolean,
    default: false
  },
  monthTitle: {
    type: String,
    default: ''
  },
  previewMode: {
    type: Boolean,
    default: false
  }
})

const exportPdf = ref()
const clinic = ref<any>({})
const scheduleData = ref<WorkSchedulePdfData | null>(null)
const rows = ref<any[]>([])

const init = async () => {
  if (props.data) {
    scheduleData.value = props.data as WorkSchedulePdfData
  }
  
  if (scheduleData.value?.clinic) {
    clinic.value = {
      name_clinic_display: scheduleData.value.clinic.name
    }
  } else {
    const clinicResponse = await clinicStore.fetchClinicById(Number(localStorage.getItem('id_clinic')))
    clinic.value = clinicResponse
  }

  const rowsPerPage = calculateRowsPerPage()
  
  if (scheduleData.value) {
    rows.value = exportPdf.value.populateRowsPerPage(scheduleData.value.employees, rowsPerPage)
  }
  
  await nextTick()
  generateReport()
  close()
}

const close = () => {
  emits('close')
}

const generateReport = () => {
  let imagePDFOptions = { quality: 0.95 }
  let jsPDFOptions = { orientation: 'landscape' }
  let pagesNumber = 0
  
  if (props.callback) {
    exportPdf.value.getPdfBlob(getFileName(), pagesNumber, jsPDFOptions, imagePDFOptions).then((blob: Blob) => {
      props.callback(blob, `${getFileName()}.pdf`)
    })
  } else if (props.workSchedulePdfAttributes?.mode === 'download' || props.flgDownloadPdf) {
    exportPdf.value.generateReport(getFileName(), pagesNumber, jsPDFOptions, imagePDFOptions)
  } else if (props.workSchedulePdfAttributes?.mode === 'print') {
    exportPdf.value.generateReport(
      getFileName(),
      pagesNumber,
      jsPDFOptions,
      imagePDFOptions,
      props.workSchedulePdfAttributes.mode
    )
  }
  
  if (props.workSchedulePdfAttributes) {
    props.workSchedulePdfAttributes.render = false
  }
}

const getFileName = () => {
  const clinicName = scheduleData.value?.clinic?.name || 'Clinic'
  return `${date.formatDate(Date.now(), 'YYYYMMDD')}_WorkSchedule_${clinicName}`
}

// Get the display symbol based on work status
const getWorkStatusSymbol = (status: string, hours: number, restMinutes: number) => {
  if (status === 'off') return '休'
  if (status === 'part_time') return ICONS.RADIO_BUTTON_UNCHECKED
  if (status === 'full_time' || (hours >= 8 && (hours * 60 - restMinutes) >= 480)) return ICONS.CIRCLE
  return ''
}

// Get work schedule for an employee on a specific date
const getEmployeeWorkStatus = (employeeId: number, date: string) => {
  if (!scheduleData.value) return { symbol: '', isBusinessDay: true, work_start: null, work_end: null }
  
  const scheduleInfo = scheduleData.value.employee_work_schedules.find(
    schedule => schedule.employee_id === employeeId
  )
  
  if (!scheduleInfo) return { symbol: '', isBusinessDay: true, work_start: null, work_end: null }
  
  const daySchedule = scheduleInfo.employee_work_schedule.find(
    day => day.date === date
  )
  
  if (!daySchedule) return { symbol: '', isBusinessDay: true, work_start: null, work_end: null }
  
  const header = scheduleData.value.calendar_headers.find(h => h.date === date)
  const isBusinessDay = header?.is_business_day ?? true
  
  return {
    symbol: getWorkStatusSymbol(daySchedule.work_status, daySchedule.work_hours, daySchedule.rest_minutes),
    isBusinessDay,
    work_start: daySchedule.work_start,
    work_end: daySchedule.work_end
  }
}

// Get summary data for a specific date and type
const getSummaryData = (date: string, type: keyof Summary) => {
  if (!scheduleData.value?.summary) return '0'
  
  const summaryArray = (scheduleData.value.summary)[type] || []
  const summaryItem = summaryArray.find((item) => item.date === date)
  return summaryItem?.total === 0 ? '' : summaryItem?.total
}

// Update the getOccupationTypes function to only return required types
const getOccupationTypes = () => {
  return [
    { key: 'doctor_works', label: '獣医師' },
    { key: 'nurse_works', label: 'VT' },
    { key: 'reception_works', label: '受付・事務' }
  ]
}

const filteredOccupationTypes = computed(() => {
  return getOccupationTypes()
})

const getTitle = () => {
  const yearMonth = (date: string) => {
    return date.split('-')[0] + '.' + date.split('-')[1] + '月'
  }

  if (scheduleData.value?.calendar_headers && scheduleData.value.calendar_headers.length > 0) {
    return yearMonth(scheduleData.value.calendar_headers[0].date) +
      ' (' + dateFormat(scheduleData.value.calendar_headers[0].date, 'MM/DD') +
      ' ~ ' + dateFormat(scheduleData.value.calendar_headers[scheduleData.value.calendar_headers.length - 1].date, 'MM/DD') +
      ') シフト表'
  }

  if (scheduleData.value?.title) {
    return scheduleData.value.title
  }
  
  if (props.monthTitle) {
    return props.monthTitle
  }
  
  return '勤務表'
}

const isSaturday = (dayStr: string) => {
  if (!dayStr) return false
  return dayStr === '土'
}

const isSunday = (dayStr: string) => {
  if (!dayStr) return false
  return dayStr === '日'
}

const isHoliday = (date: string) => {
  if (!scheduleData.value) return false
  const header = scheduleData.value.calendar_headers.find(h => h.date === date)
  return header?.is_holiday || false
}

const getHolidayName = (date: string) => {
  if (!scheduleData.value) return ''
  const header = scheduleData.value.calendar_headers.find(h => h.date === date)
  return header?.holiday_name || ''
}

const formatTime = (timeStr: string) => {
  if (!timeStr) return ''
  return timeStr.substring(0, 5)
}

// Get work status counts for an employee
const getEmployeeWorkStatusCounts = (employeeId: number) => {
  if (!scheduleData.value) return { full_time: 0, part_time: 0, off: 0 }
  
  const scheduleInfo = scheduleData.value.employee_work_schedules.find(
    schedule => schedule.employee_id === employeeId
  )
  
  if (!scheduleInfo) return { full_time: 0, part_time: 0, off: 0 }
  
  return scheduleInfo.counts
}

const isFirstDayOfMonth = (dateDay: string) => {
  // Check if the date is in format "MM/DD" and ends with "/1"
  const parts = dateDay.split('/')
  return parts.length === 2 && parts[1] === '1'
}

// Calculate rows per page for pagination
const calculateRowsPerPage = () => {
  // Constants for pagination
  const MAX_ROWS_PER_PAGE = 20
  const summaryRowHeight = 1 // Height of the summary separator row
  const calendarHeaderCount = scheduleData.value?.calendar_headers.length || 0
  
  // Get employees from API response
  const employees = scheduleData.value?.employees || []
  const totalEmployees = employees.length
  
  // Get summary rows count - one row for each occupation type plus total row
  const occupationTypeCount = filteredOccupationTypes.value.length
  const totalSummaryRows = occupationTypeCount + 1 // +1 for total employee row
  
  // If everything fits on one page, put it all on one page
  if (totalEmployees + totalSummaryRows + summaryRowHeight <= MAX_ROWS_PER_PAGE) {
    return [totalEmployees + totalSummaryRows + summaryRowHeight]
  }
  
  // Strategy: Fill pages with employees, keep summary on the last page
  const maxEmployeesPerPage = MAX_ROWS_PER_PAGE
  const maxEmployeesOnLastPage = MAX_ROWS_PER_PAGE - totalSummaryRows - summaryRowHeight
  
  // Calculate number of full pages needed (without summary)
  const fullPages = Math.floor(totalEmployees / maxEmployeesPerPage)
  const remainingEmployees = totalEmployees % maxEmployeesPerPage
  
  // Calculate pages distribution
  const pages = []
  
  // Add full pages
  for (let i = 0; i < fullPages; i++) {
    pages.push(maxEmployeesPerPage)
  }
  
  // Handle remaining employees
  if (remainingEmployees > 0) {
    if (remainingEmployees <= maxEmployeesOnLastPage) {
      // Put remaining employees and summary on the last page
      pages.push(remainingEmployees + totalSummaryRows + summaryRowHeight)
    } else {
      // Too many remaining employees for the last page with summary
      // Create a new full page and put remaining employees on the next page with summary
      pages.push(remainingEmployees)
      pages.push(totalSummaryRows + summaryRowHeight)
    }
  } else if (fullPages > 0) {
    // No remaining employees, add summary page
    pages.push(totalSummaryRows + summaryRowHeight)
  }
  
  return pages
}

// Filter rows for pagination
const filterRows = (page: any[]) => {
  if (!page) return []
  return page.filter(row => row !== '')
}

onMounted(() => {
  init()
})
</script>

<template>
  <div>
    <PdfExport :generateOnePDF="false" ref="exportPdf" sheetName="_勤務表" orientationMode="landscape" />
    <q-card id="element-to-print" class="bg-white q-pa-none" square style="max-width: 1122px; margin: auto">
      <div class="q-px-md" :class="idx == 0 ? 'q-pt-md' : ''" :style="idx == 0 ? 'height: 792.96px;' : `height: 792.96px; overflow: hidden; padding-top: 15px;`" v-for="(page, idx) in rows" :key="idx">
        <div class="flex justify-between items-end">
          <div class="flex items-center justify-between title" style="flex-basis: 50%">
            <div class="text-weight-bold" style="font-size: 18px;">{{ getTitle() }}</div>
          </div>
          <div class="flex justify-end" style="flex-basis: 50%">
            <span class="q-mr-sm">{{ scheduleData?.clinic?.name || clinic.name_clinic_display || '' }}</span>
            <span class="text-grey-700 text-caption">出力時刻 {{ date.formatDate(Date.now(), 'YYYY/MM/DD') }}</span>
            <span class="page-info">page {{ idx + 1 }} of {{ rows.length }}</span>
          </div>
        </div>

        <div class="q-mt-md page-container" :class="{ 'last-page': idx === rows.length - 1 }">
          <table class="schedule-table full-width">
            <thead>
              <tr>
                <th class="employee-header">従業員</th>
                <th 
                  v-for="header in scheduleData?.calendar_headers" 
                  :key="header.date"
                  class="day-header text-center"
                  :class="{
                    'business-day-off': !header.is_business_day, 
                    'is-saturday': isSaturday(header.day), 
                    'is-sunday': isSunday(header.day),
                    'is-holiday': header.is_holiday
                  }"
                  :title="header.holiday_name"
                >
                  {{ header.date_day }}<br>
                  <span v-if="!isFirstDayOfMonth(header.date_day)">{{ header.day }}</span>
                  <span v-if="header.is_holiday" class="holiday-indicator">*</span>
                </th>
                <th class="count-header text-center" v-html="ICONS.CIRCLE"></th>
                <th class="count-header text-center" v-html="ICONS.RADIO_BUTTON_UNCHECKED"></th>
                <th class="count-header text-center">休</th>
              </tr>
            </thead>
            <tbody>
              <!-- Employees for this page -->
              <tr v-for="employee in filterRows(page)" :key="employee.id">
                <td class="employee-cell">
                  <div class="employee-info">
                    <span class="employee-type">{{ employee.type_occupation }}</span>
                    <span class="employee-name">{{ employee.name_display }}</span>
                  </div>
                </td>
                <td 
                  v-for="header in scheduleData?.calendar_headers" 
                  :key="`${employee.id}-${header.date}`"
                  class="text-center"
                  :class="{ 
                    'business-day-off': !header.is_business_day,
                    'day-off': getEmployeeWorkStatus(employee.id, header.date).symbol === '休',
                    'is-saturday': isSaturday(header.day),
                    'is-sunday': isSunday(header.day),
                    'is-holiday': header.is_holiday
                  }"
                >
                  <div class="work-status-container">
                    <span 
                      :class="{
                        'work-symbol': ['radio_button_unchecked', 'circle'].includes(getEmployeeWorkStatus(employee.id, header.date).symbol),
                        'rest-mark': getEmployeeWorkStatus(employee.id, header.date).symbol === '休'
                      }"
                      v-if="getEmployeeWorkStatus(employee.id, header.date).symbol === '休'"
                    >
                      {{ getEmployeeWorkStatus(employee.id, header.date).symbol }}
                    </span>
                    <span 
                      v-else
                      class="work-symbol"
                      v-html="getEmployeeWorkStatus(employee.id, header.date).symbol"
                    ></span>
                  </div>
                </td>
                <td class="count-cell text-center" :class="{ 'text-grey-500': getEmployeeWorkStatusCounts(employee.id).full_time === 0 }">{{ getEmployeeWorkStatusCounts(employee.id).full_time }}</td>
                <td class="count-cell text-center" :class="{ 'text-grey-500': getEmployeeWorkStatusCounts(employee.id).part_time === 0 }">{{ getEmployeeWorkStatusCounts(employee.id).part_time }}</td>
                <td class="count-cell text-center" :class="{ 'text-grey-500': getEmployeeWorkStatusCounts(employee.id).off === 0 }">{{ getEmployeeWorkStatusCounts(employee.id).off }}</td>
              </tr>
            </tbody>
          </table>
          
          <!-- Summary section on last page only - positioned at bottom -->
          <template v-if="idx === rows.length - 1">
            <div class="summary-section">
              <table class="schedule-table full-width">
                <thead>
                  <tr>
                    <th class="count-cell"></th>
                    <th 
                      v-for="header in scheduleData?.calendar_headers" 
                      :key="header.date"
                      class="day-header text-center"
                      :class="{
                        'business-day-off': !header.is_business_day, 
                        'is-saturday': isSaturday(header.day), 
                        'is-sunday': isSunday(header.day),
                        'is-holiday': header.is_holiday
                      }"
                      :title="header.holiday_name"
                    >
                      {{ header.date_day }}<br>
                      <span v-if="!isFirstDayOfMonth(header.date_day)">{{ header.day }}</span>
                      <span v-if="header.is_holiday" class="holiday-indicator">*</span>
                    </th>
                    <th class="count-cell" colspan="3"></th>
                  </tr>
                </thead>
                <tbody>
                  <!-- <tr class="summary-separator">
                    <td colspan="100%" class="summary-separator-cell"></td>
                  </tr> -->

                  <!-- Dynamic occupation summary rows -->
                  <tr 
                    v-for="occupationType in filteredOccupationTypes" 
                    :key="occupationType.key"
                    class="category-row total-row"
                  >
                    <td class="summary-label">{{ occupationType.label }}</td>
                    <td 
                      v-for="header in scheduleData?.calendar_headers" 
                      :key="`${occupationType.key}-${header.date}`"
                      class="text-center summary-day"
                      :class="{ 
                        'business-day-off': !header.is_business_day,
                        'is-saturday': isSaturday(header.day),
                        'is-sunday': isSunday(header.day),
                        'is-holiday': header.is_holiday
                      }"
                    >
                      {{ getSummaryData(header.date, occupationType.key as keyof Summary) }}
                    </td>
                    <td class="count-cell" colspan="3"></td>
                  </tr>

                  <!-- Total employee row -->
                  <tr class="category-row total-row">
                    <td class="summary-label">合計スタッフ数</td>
                    <td 
                      v-for="header in scheduleData?.calendar_headers" 
                      :key="`total-${header.date}`"
                      class="text-center"
                      :class="{ 
                        'business-day-off': !header.is_business_day,
                        'is-saturday': isSaturday(header.day),
                        'is-sunday': isSunday(header.day),
                        'is-holiday': header.is_holiday
                      }"
                    >
                      {{ getSummaryData(header.date, 'total_employee') }}
                    </td>
                    <td class="count-cell" colspan="3"></td>
                  </tr>
                </tbody>
              </table>
            </div>
          </template>
        </div>
        
        <template v-if="idx + 1 != rows.length">
          <div class="html2pdf__page-break"></div>
        </template>
      </div>
    </q-card>
  </div>
</template>

<style lang="scss" scoped>
.title {
  padding: 0 10px 2px;
}

.border-bottom-black { 
  border-bottom-color: #000 !important; 
}

.card-pdf-main-border-1px {
  border: 1px solid #ddd;
  position: relative;
  min-height: 800px;
}

.schedule-table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  margin-bottom: 15px;
  background-color: white;
  // border: 1px solid #333;
}

.schedule-table th,
.schedule-table td {
  border: none;
  border-right: 1px solid #333;
  border-bottom: 1px solid #333;
  padding: 3px 2px;
  font-size: 12px;
  height: 28px;
  vertical-align: middle;
}

.schedule-table th:first-child,
.schedule-table td:first-child {
  border-left: 1px solid #333;
}

.schedule-table tr:first-child th {
  border-top: 1px solid #333;
}

.employee-header {
  background-color: #d0d0d0;
  font-weight: bold;
  width: 150px;
}

.day-header {
  background-color: #e8e8e8;
  font-size: 10px;
  width: 27px;
  position: relative;
  padding: 2px;
  line-height: 1.2;
}

.summary-day {
  width: 25px;
}

.holiday-indicator {
  position: absolute;
  top: 1px;
  right: 2px;
  color: #D63C3C;
  font-size: 10px;
  font-weight: bold;
}

.business-day-off {
  background-color: #e6d4ff;
}

.is-holiday {
  background-color: #ffd4d4;
}

.employee-cell {
  background-color: #f5f5f5;
  font-weight: bold;
  padding: 0;
}

.employee-info {
  display: flex;
  padding: 0 8px;
  align-items: center;
  width: 100%;
}

.employee-type {
  font-size: 11px;
  font-weight: bold;
  color: #666;
  margin-right: 5px;
}

.employee-name {
  font-weight: bold;
  font-size: 11px;
}

.work-symbol {
  font-weight: bold;
  color: #000;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  
  :deep(svg) {
    width: 16px;
    height: 16px;
  }
}

.rest-mark {
  font-weight: bold;
  color: #d93025;
}

.day-off {
  background-color: #f8f8f8;
}

.summary-separator {
  border: none;
  border-bottom: 1px solid #333 !important;
}

.summary-separator td,
.summary-separator th {
  border: none !important;
  border-bottom: 1px solid #333 !important;
}

.summary-separator-cell {
  background-color: #fff;
  font-weight: bold;
  text-align: center;
  padding: 4px;
  font-size: 13px;
  border: none !important;
  border-bottom: 1px solid #333 !important;
}

.summary-label {
  width: 150px;
  background-color: #d0d0d0;
  font-weight: bold;
  text-align: left !important;
  padding-left: 8px !important;
}

.category-row td {
  background-color: #f8f8f8;
}

.total-row td {
  background-color: #e8e8e8;
  font-weight: bold;
}

.is-saturday {
  color: #0057FF;
}

.is-sunday, .is-holiday {
  color: #D63C3C;
}

.count-header {
  background-color: #e8e8e8;
  font-weight: bold;
  min-width: 30px;
  font-size: 12px;
}

.count-cell {
  background-color: #f5f5f5;
  font-size: 12px;
  min-width: 30px;
}

.absolute-bottom {
  position: absolute;
  bottom: 10px;
  left: 10px;
  right: 10px;
  font-size: 12px;
}

.page-info {
  font-size: 12px;
  color: #666;
  margin-left: 8px;
}

.print-date {
  font-size: 12px;
  color: #666;
}

.work-times {
  font-size: 7px;
  margin-top: 2px;
  display: block;
  line-height: 1;
}

.work-status-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

.page-container {
  display: flex;
  flex-direction: column;
  height: 100%; /* Adjust based on your page height */
}

.last-page {
  justify-content: space-between;
}

.summary-section {
  margin-top: auto;
  padding-bottom: 40px;
}

@media print {
  body {
    margin: 0;
    padding: 0;
  }
}
</style>
