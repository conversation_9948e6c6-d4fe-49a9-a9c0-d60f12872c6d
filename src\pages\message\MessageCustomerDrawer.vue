<script setup lang="ts">
import { ref, computed, onMounted, watch, reactive } from 'vue'
import { formatDateTime, formatHoursMinutes, getCustomerNameById, getDateTimeNow, timeDifferences, getCustomerLabelColor, aahUtilsGetEmployeeName, concatenate, dateFormat } from '@/utils/aahUtils'
import useCustomerStore from '@/stores/customers'
import useEmployeeStore from '@/stores/employees'
import useCliCommonStore from '@/stores/cli-common'
import { MessageThreadType, CliCommon, GenericValueLabelType } from '@/types/types'
import { typeCustomerThread } from '@/utils/enum'
import { storeToRefs } from 'pinia'

type FilterDataType = {
  name_thread: string | null
  id_customer: string | null
  // id_pet: string | null
  type_thread: number[] | null
  id_employee_ask: string | null
  id_employee_answer: string | null
}

const props = withDefaults(
  defineProps<{
    allTypeThreads: MessageThreadType[]
    drawer: boolean
    drawerHeaderHeight: number
    selectedThread: MessageThreadType
    showBadgeFilter: FilterDataType
  }>(),
  {
    drawer: true,
    drawerHeaderHeight: 0,
    allTypeThreads: () => {
      return [] as MessageThreadType[]
    },
    selectedThread: () => {
      return {} as MessageThreadType
    },
    showBadgeFilter: () => {
      return {} as FilterDataType
    }
  }
)

// This computed property will reactively generate the array of active filter keys
const showBadgeFilter = computed(() => {
  // Ensure filterData.value exists and is an object
  if (!props.showBadgeFilter || typeof props.showBadgeFilter !== 'object') {
    return [];
  }

  // Filter keys where the corresponding value is truthy
  const keys = Object.keys(props.showBadgeFilter).filter((key) => {
    const value = props.showBadgeFilter[key as keyof FilterDataType];
    if (value === null || typeof value === 'undefined') {
      return false; // Exclude null and undefined
    }
    if (typeof value === 'string' && value.trim() === '') {
      return false; // Exclude empty strings
    }
    if (Array.isArray(value) && value.length === 0) {
      return false; // Exclude empty arrays
    }
    // If it reaches here, the value is considered an "active" filter
    return true;
  });

  return keys;
});

const emits = defineEmits<{
  (e: 'handleDrawer'): void
  (e: 'handleFlgPinned', value: MessageThreadType): void
  (e: 'handleRefreshClick'): void
  (e: 'openThreadFilterModal'): void
  (e: 'selectedFilter', value: FilterDataType): void
  (e: 'setSelectedThread', value: MessageThreadType): void
  (e: 'handleUpdatePanel', value: string): void
}>()

const cliCommonStore = useCliCommonStore()
const customerStore = useCustomerStore()
const employeeStore = useEmployeeStore()
const { getAllCustomers, getCustomer } = storeToRefs(customerStore)
const drawerHeader = ref()
const filterMessageToggle = ref(0)
const employeeId = Number(JSON.parse(localStorage?.getItem('id_employee') || '0'))
const employeeDepartement = Number(JSON.parse(localStorage?.getItem('userTypeDepartment') || '0'))
const defaultEmployee = JSON.parse(localStorage.getItem("id_employee") || 'null')

const typeDepartments = ref<Array<GenericValueLabelType>>([])
const typeDepartmentsDefault = reactive<Array<GenericValueLabelType>>([])
const petList = ref<Array<GenericValueLabelType>>([])
const petListDefault = reactive<Array<GenericValueLabelType>>([])
const showPets = ref(false)
const drawerHeaderHeight = ref(0)
const panel = ref('open')
const tabPanelName = ref('open')

const filterData = ref<FilterDataType>({
  name_thread: null,
  id_customer: null,
  // id_pet: null,
  type_thread: null,
  id_employee_ask: null,
  id_employee_answer: null
})


const filteredSelectedThread = computed(() => {
  let threadsToFilter = [];

  // Step 1: Normalize the incoming threads data into a flat array
  threadsToFilter = props.allTypeThreads;

  // Step 2: Apply conditional filtering based on filterMessageToggle
  if (filterMessageToggle.value === 1 || filterMessageToggle.value === 0) {
    // Show only data related to the logged-in user
    if (employeeId === null || typeof employeeId === 'undefined') {
        console.warn("Logged in user ID is not available for filtering based on toggle 1.");
    }

    const filteredThreads = threadsToFilter.filter(thread => {
        if (typeof thread !== 'object' || thread === null) {
            return false;
        }
        // Check if the thread is related to the employeeId (if available)
        // OR if the thread's department matches the employee's department
        const isRelatedToEmployeeId = (employeeId !== null && typeof employeeId !== 'undefined') &&
            (thread.id_employee_achieved === employeeId.toString() ||
             thread.id_employee_answer === employeeId.toString() ||
             thread.id_employee_ask === employeeId.toString());

        const isRelatedToEmployeeDepartment = thread.type_department === employeeDepartement;
        if (isRelatedToEmployeeId || isRelatedToEmployeeDepartment) {
          filterMessageToggle.value = 1
        }
        return isRelatedToEmployeeId || isRelatedToEmployeeDepartment;
    });
    return filteredThreads;
  } else if (filterMessageToggle.value === 2) {
    filterMessageToggle.value = 2
    // Show all data, no filtering needed based on user ID
    return threadsToFilter;
  } else {
    // Default case or handle other toggle values if any
    console.warn("Unknown filterMessageToggle value:", filterMessageToggle.value);
    return threadsToFilter; // Or [] depending on desired default behavior
  }
});

watch(
  filteredSelectedThread,
  (newValue, oldValue) => {
    setTimeout(() => {
      drawerHeaderHeight.value = drawerHeader?.value?.offsetHeight + 18
    }, 10)
    drawerHeaderHeight.value = drawerHeader?.value?.offsetHeight + 18
    // If the filtered array is empty, change filterMessageToggle.value to 2
    if (filteredSelectedThread.value.length === 0) {
        console.log("No threads found for filterMessageToggle.value === 1. Setting filterMessageToggle.value to 2.");
        filterMessageToggle.value = 2;
    } else {
        filterMessageToggle.value = 1;
    }
  },
  {
    once: true // This is the magic option from vue to only call watch once!
  }
);

const clearFilter = () => {
  filterData.value = {
    name_thread: null,
    id_customer: null,
    // id_pet: null,
    type_thread: null,
    id_employee_ask: null,
    id_employee_answer: null
  }

  const keys = Object.keys(filterData.value).filter((key) => {
    return filterData.value[key as keyof FilterDataType]
  })

  emits('selectedFilter', filterData.value)
}
const getPetName = (petId: string) => {
  let r = getCustomer.value?.pets?.find((pet) => pet?.value == petId)?.name_pet
  return r ? r : ''
}
const setFilter = () => {
  const keys = Object.keys(filterData.value).filter((key) => {
    return filterData.value[key as keyof FilterDataType]
  })

  emits('selectedFilter', filterData.value)
}

const handleDrawer = () => {
  emits('handleDrawer')
}

const handleRefreshClick = () => {
  emits('handleRefreshClick')
}

const setSelectedThread = (value: MessageThreadType) => {
  emits('setSelectedThread', value)
}

const onUpdatePanel = (value: string) => {
  tabPanelName.value = value
  emits('handleUpdatePanel', value)
}

const handleFlgPinned = (value: MessageThreadType) => {
  emits('handleFlgPinned', value)
}

const handleEmpName = (value: string) => {
  return aahUtilsGetEmployeeName(employeeStore.getAllEmployees, value)
}

const selectDefaultEmployee = (key: string) => {
  filterData.value[key as keyof FilterDataType] = defaultEmployee
}

const typeDeptName = (value: number) =>
  typeDepartments.value.find((v) => v.value == value)
    ?.label

const openFilterModal = async () => {
  emits('openThreadFilterModal')
}

const handleCustomerName = (customerId: string) => {
  const customer = getAllCustomers.value.find(
    (cus) => cus?.value == customerId
  )
  return getCustomerNameById(customer, 3)
}

const handleCustomerTypeLabelColor = (customerId: string) => {
  if(!customerId) return false
  const customer = getAllCustomers.value.find(
    (cus) => cus?.value == customerId
  )
  return customer && customer?.type_customer_color ? getCustomerLabelColor(customer?.type_customer_color) : null
}

const format = (value: string) => {
  const diff = timeDifferences(
    value,
    getDateTimeNow(),
    'hours'
  )

  if (diff < 0) {
    return `${formatDateTime(value)} ${formatHoursMinutes(
      value
    )}`
  }

  return formatHoursMinutes(value)
}

const handleStr = (str: String) => {
  let newStr = ''
  for (let i = 0; i < str?.length; i++) {
    if (i <= 20) {
      newStr += str[i]
    }
  }
  if (str?.length >= 20) {
    newStr += '...'
  }
  return newStr
}

const getTypeThreadName = (threadTypeEnum: number) => {
  return typeCustomerThread.find((data) => data.value === threadTypeEnum)?.label
}

onMounted(async () => {
  typeDepartments.value = [...cliCommonStore.getCliCommonTypeDepartmentList.map((item: CliCommon) => ({
    value: parseInt(item.code_func1),
    label: item.name_cli_common
  }))]
  typeDepartmentsDefault.push(...typeDepartments.value)
  drawerHeaderHeight.value = drawerHeader?.value?.offsetHeight + 18
})

window.onresize = () => {
  drawerHeaderHeight.value = drawerHeader?.value?.offsetHeight + 18
}
</script>

<template>
  <q-drawer
    v-model="props.drawer"
    show-if-above
    :width="350"
    :breakpoint="500"
    class="drawer-border overflow-hidden clinic-drawer"
  >
    <div ref="drawerHeader" :key="filterMessageToggle" class="absolute bg-white drawerHeaderBox">
      <q-toolbar class="bg-grey-white text-grey-800 pt-15px">
        <q-toolbar-title class="title2 bold">
          <div class="row items-center row-item-drawer">
            <q-btn
              @click="openFilterModal"
              round
              unelevated
              padding="3px 3px"
              text-color="black"
              class="q-ml-auto">
              <q-icon size="24px" name="filter_list" />
              <q-badge class="mt-badge" v-if="showBadgeFilter.length" color="red" rounded floating />

            </q-btn>
            <q-btn
              @click="handleRefreshClick"
              round
              unelevated
              text-color="black"
              padding="3px 3px"
              class="q-ml-md"
            >
              <q-icon size="24px" name="refresh" />
            </q-btn>
            <q-btn
              @click="handleDrawer"
              round
              unelevated
              padding="3px 3px"
              text-color="black"
              class="q-ml-md"
            >
              <q-icon size="28px" name="chevron_left" />
            </q-btn>
          </div>
        </q-toolbar-title>
      </q-toolbar>
    </div>
    <q-scroll-area
      class="scrollBox"
      :style="{ marginTop: `${drawerHeaderHeight}px` }"
    >
      <q-btn-toggle v-model="panel" unelevated spread dense no-caps
        color="white" text-color="primary" :options="[
          { label: 'オープン', value: 'open' },
          { label: '対応終了', value: 'close' }
        ]" @update:model-value="onUpdatePanel" />
      <q-tab-panels v-model="panel" class="tab-panels">
        <q-tab-panel :name="tabPanelName" class="q-pa-none">
          <q-list v-for="(thread, index) in props.allTypeThreads" :key="index">
            <q-item
              clickable
              v-ripple
              class="fit"
              :class="
                selectedThread?.id_message_thread === thread?.id_message_thread
                  ? 'selecte_Thread'
                  : 'not_selecte_Threads'
              "
              @click="setSelectedThread(thread)"
            >
              <q-item-section>
                <div class="flex justify-between items-center threadTypeLink">
                  <div class="col-11">
                    <q-chip dense class="text-white status-chip urgent-chip " v-show="thread?.flg_urgent">
                      <div class="text-xs text-white ">至急</div>
                    </q-chip>
                    <q-chip
                      v-if="thread?.type_thread"
                      dense
                      class="status-chip thread-type-chip"
                      text-color="white"
                    >
                      {{ getTypeThreadName(thread?.type_thread) }}
                    </q-chip>
                    <div class="flex items-center">
                      <div
                        class="title3 bold text-grey-900 nameThreadBox flex  justify-between"
                      >
                      <div class="message-ellipsis">
                        <span v-if="thread?.type_thread === 90">{{ (thread?.type_thread === 90 ? "有料メッセージ相談":'') + ' ' + 'のご予約' + '/'  +  getPetName(thread?.id_pet) }}</span>
                        <span v-else>
                          {{ handleStr(thread?.name_thread) }}
                        </span>
                      </div>
                        <q-icon
                          v-if="thread?.flg_read === false && thread?.messages?.length > 0"
                          size="13px"
                          name="circle"
                          class="q-ml-xs"
                          color="red"
                        />
                      </div>
                    </div>
                  </div>
                  <div class="flex items-center">
                    <div class="caption1 text-grey-700 q-mr-xs">
                      {{ format(thread?.datetime_update) }}
                    </div>
                    <q-btn
                      round
                      unelevated
                      :text-color="
                        thread?.flg_emr_pinned ? 'black' : 'grey-400'
                      "
                      padding="3px"
                      @click="handleFlgPinned(selectedThread)"
                    >
                      <q-icon
                        size="16px"
                        name="push_pin"
                      />
                    </q-btn>
                  </div>
                </div>
                <div
                  class="title3 bold text-grey-900 ellipsis nameThreadBox q-mt-xs q-mt-sm"
                >
                  <div class="row no-wrap items-center col-auto">
                    <div class="flex items-center ellipsis no-wrap">
                      <q-icon size="18px" name="person" class="q-mr-sm" />
                      <span class="body2 regular text-black ellipsis"
                        >{{ handleCustomerName(thread?.id_customer) }} 様</span
                      >
                      <q-icon
                        v-if="handleCustomerTypeLabelColor(thread?.id_customer)"
                        size="13px"
                        name="circle"
                        class="q-ml-xs"
                        :color="handleCustomerTypeLabelColor(thread?.id_customer) || undefined"
                      />
                    </div>
                  </div>
                </div>
                <div class="row no-wrap items-center justify-between">
                  <div v-if="thread?.last_message" class="col-12 text-right">
                    <span class="caption1 regular text-grey-800">
                      {{ format(thread?.last_message) }}
                    </span>
                  </div>
                </div>
              </q-item-section>
            </q-item>
            <q-separator class="separator-class" />
          </q-list>
          <div v-if="!props.allTypeThreads.length" class="text-center noThreads">
            スレッドがありません
          </div>
        </q-tab-panel>
      </q-tab-panels>
    </q-scroll-area>
  </q-drawer>
</template>

<style lang="scss">
.q-drawer {
  z-index: 5;
}
.selecte_Thread {
  padding: 8px 9px 10px 10px;
  border-bottom: 0.1px solid #dddd;
  background-color: #dddd !important;
}
.not_selecte_Threads {
  padding: 8px 9px 10px 10px;
  border-bottom: 0.1px solid #dddd;
  background-color: white !important;
}
.goal-achieved-bg {
  background-color: $positive;
}
.goal-unachieved-bg {
  background-color: #b99b14;
}
.drawerHeaderBox {
  width: 350px;
  border-right: 1px solid #e7e7e7;
}
.filterWidth {
  max-width: 150px;
}
.scrollBox {
  height: 90%;
  border-top: 1px solid #dddd;
  @media screen and (max-width: 1024px) {
    // for Ipad
    height: 90%;
  }
}
.threadTypeLink {
  height: 21px;
  .status-container {
    display: flex;
    align-items: center;
    .status-information {
      display: flex;
      gap: 4px;
      align-items: center;
      .status-goal-information {
        color: $white;
        padding: 2px 5px;
        border-radius: 3px;
      }
    }
  }
}

.flgUrgentBox {
  max-width: 155px;
}
.typeThreadBox {
  padding: 1.5px 5px;
}
.noThreads {
  margin-top: 250px;
}
.filter-menu {
  width: 750px;
  @media screen and (max-width: 500px) {
    width: 350px;
  }
}
.status-chip {
  font-size: 12px;
  padding: 0px 12px;
  &.urgent-chip {
    background-color: #BE0123;
  }

  &.achieved-chip {
    background-color: #34c759;
  }

  &.unachieved-chip {
    background-color: #EC9819;
  }

  &.closed-chip {
    background-color: $primary;
  }

  &.thread-type-chip {
    background-color: #45589C;
  }

  :deep(.q-chip__content) {
    padding: 0 8px;
  }
}

.pt-15px {
  padding-top: 15px
}

.row-item-drawer {
  height: 37px;
  // margin-top: -2spx;
}
.mt-badge {
  margin-top: 2px;
}

// Tab CSS Start
:deep(.q-tab__label) {
  font-size: 15px;
  font-weight: 700;
}
.tab-panels {
  max-width: 350px;
}
.nameThreadBox {
  max-width: 330px;
  width: 100%;
}
.separator-class {
  border-bottom: 1px solid $grey-300;
}
.message-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 230px;
  display: inline-block;
  vertical-align: middle;
}
.noThreads {
  margin-top: 250px;
}
</style>
