<script setup>
import { ref, onMounted } from 'vue'

const pdfCanvas = ref(null)
const props = defineProps({
  pdfPath: String,
  cardThumbnail: {
    type: Boolean,
    default: false
  }
})

const loadPdfJsScript = () => {
  return new Promise((resolve, reject) => {
    if (window.pdfjsLib) {
      resolve()
      return
    }
    const script = document.createElement('script');
    script.src = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.4.120/pdf.min.js'
    script.onload = resolve
    script.onerror = reject
    document.head.appendChild(script)
  });
};

const renderPdfThumbnail = async (pdfUrl, canvas) => {
  await loadPdfJsScript()
  pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.4.120/pdf.worker.min.js'

  const pdf = await pdfjsLib.getDocument(pdfUrl).promise
  const page = await pdf.getPage(1)

  const viewport = page.getViewport({ scale: 1 })
  const context = canvas.getContext('2d')

  if (props.cardThumbnail) {
    // For card thumbnails, use object-fit: cover behavior with 16:9 ratio
    const targetWidth = 208
    const targetHeight = 157.5 // 16:9 ratio (208 * 9/16 = 157.5)

    // Calculate scale to fill the container (like object-fit: cover)
    const scaleX = targetWidth / viewport.width
    const scaleY = targetHeight / viewport.height
    const scale = Math.max(scaleX, scaleY)

    const scaledViewport = page.getViewport({ scale })

    // Set canvas size to target size
    canvas.width = targetWidth
    canvas.height = targetHeight

    // Calculate offset to center the content
    const offsetX = (targetWidth - scaledViewport.width) / 2
    const offsetY = (targetHeight - scaledViewport.height) / 2

    // Clear canvas and set clipping
    context.clearRect(0, 0, targetWidth, targetHeight)
    context.save()
    context.rect(0, 0, targetWidth, targetHeight)
    context.clip()

    // Translate to center the content
    context.translate(offsetX, offsetY)

    await page.render({ canvasContext: context, viewport: scaledViewport }).promise
    context.restore()
  } else {
    // Default behavior for non-card thumbnails
    canvas.width = viewport.width
    canvas.height = viewport.height
    await page.render({ canvasContext: context, viewport }).promise
  }
}

onMounted(async () => {
  if (props.pdfPath) {
    await renderPdfThumbnail(props.pdfPath, pdfCanvas.value)
  }
})
</script>
<template>
  <div>
    <canvas
      ref="pdfCanvas"
      :style="cardThumbnail ? 'width: 100%; object-fit: cover; display: block;' : 'width: 100%; max-height: 250px; border: 1px solid #ccc;'"
    ></canvas>
  </div>
</template>

