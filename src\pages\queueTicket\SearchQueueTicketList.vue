<script setup lang="ts">
import { computed, defineAsyncComponent, h, nextTick, onMounted, onUnmounted, reactive, ref, watch } from 'vue'
import MtHeader from '@/components/layouts/MtHeader.vue'
import MtFilterSelect from '@/components/MtFilterSelect.vue'
import MtFormCheckBox from '@/components/form/MtFormCheckBox.vue'
import MtTable2 from '@/components/MtTable2.vue'
import SearchStatusBoardListModal from '../statusBoard/SearchStatusBoardListModal.vue'
import mtUtils from '@/utils/mtUtils'
import useCustomerStore from '@/stores/customers'
import useActionStore from '@/stores/action'
import { storeToRefs } from 'pinia'
import {
  concatenate,
  formatDateWithTime,
  formatHoursMinutes,
  getCustomerLabelColor,
  getCustomerName,
  getDateToday
} from '@/utils/aahUtils'
import useQueueTicketStore from '@/stores/queue_ticket'
import UpdateQueueTicketModal from './UpdateQueueTicketModal.vue'
import useEmployeeStore from '@/stores/employees'
import MtFormPullDown from '@/components/form/MtFormPullDown.vue'
import _, { keyBy } from 'lodash'
import { useRouter } from 'vue-router'
import useCommonStore from '@/stores/common'
import { statusQueueTicket, typeProcessTime, typeWeekday } from '@/utils/enum'
import { setPageTitle } from '@/utils/pageTitleHelper'
import useClinicStore from '../../stores/clinics'
import useCliCommonStore from '@/stores/cli-common'
import useRoomStore from '@/stores/rooms'
import EmailModal from './SendEmailModal.vue'
import selectOptions from '@/utils/selectOptions'
import dayjs from 'dayjs'
import { Platform, QIcon, useQuasar } from 'quasar'
import UpdQtRequest from '@/pages/queueTicket/UpdQtRequest.vue'
import MtPetInfoLabel from '@/components/customers/MtPetInfoLabel.vue'
import MtCustomerInfoLabel from '@/components/customers/MtCustomerInfoLabel.vue'
import aahMessages from '@/utils/aahMessages'
import { QueueTicketType, RoomType } from '@/types/types'
import AdditionalFilterQtModal, {
  AdditionalFilterQtModalProps,
  AdditionalFilterQtModalPropsSearch,
  SORT_TYPE_DEFAULT
} from '@/pages/queueTicket/AdditionalFilterQtModal.vue'
import { queueTicketHelperContents } from '@/utils/menuHelperContents'
import MtToolTipsSmall from '@/components/toolTips/MtToolTipsSmall.vue'
import { useHtmlBox } from '@/composables/useHtmlBox'

const ViewPetDetailModal = defineAsyncComponent(() => import('@/pages/master/customerPet/ViewPetDetailModal.vue'))

type Doctor = {
  id: number
  name: string
  count: number
  qt_list: string[]
  color: string
}

interface Customer {
  id_customer: number
  name_kana_family: string
  name_kana_first: string
  name_family: string
  name_first: string
  type_customer_color?: number
}

const $q = useQuasar()
const router = useRouter()
const actionStore = useActionStore()
const cliCommonStore = useCliCommonStore()
const queueTicketStore = useQueueTicketStore()
const customerStore = useCustomerStore()
const roomStore = useRoomStore()
// const { getAction } = storeToRefs(actionStore)
const action = computed(() => actionStore.action)
const { getCliCommonQTVisitPurposeList } = storeToRefs(cliCommonStore)
const { getFilteredQueueTicketLists, getDataFromCustomerPage } = storeToRefs(queueTicketStore)
const employeeStore = useEmployeeStore()
const commonStore = useCommonStore()
const { getCommonTypeAnimalOptionList } = storeToRefs(commonStore)
const { getAllRooms } = storeToRefs(roomStore)

const queueTicketCallDataDefault = {
  flg_qt_calling: true,
  flg_qt_room_display: false,
  id_room: null
}
const queueTicketCallData = reactive({...queueTicketCallDataDefault})

// Utility function to generate random colors
const generateRandomColor = (): string => {
  // Generate a random hue (0-360)
  const hue = Math.floor(Math.random() * 360)
  // Slightly randomize saturation and lightness for more variety
  const saturation = 60 + Math.floor(Math.random() * 25) // 60-85%
  const lightness = 50 + Math.floor(Math.random() * 20) // 50-70%
  return `hsl(${hue}, ${saturation}%, ${lightness}%)`
}

const emits = defineEmits(['close'])
const closeModal = () => {
  emits('close')
}

const todaysDate = getDateToday()
const typeAnimalOptions = ref()
const tab = ref('受付中')
const tabItems = ref(['受付中', '呼出済', '不在', '削除'])
const searchDoctor = ref('')
const status1_2 = ref(0)
const status3 = ref(0)
const status4 = ref(0)
const status90_99 = ref(0)
const rows1_2 = ref([])
const rows3 = ref([])
const rows4 = ref([])
const rows90_99 = ref([])
const doctors = ref([] as Doctor[])
const totalStatus = ref(0)
const link = ref(-1)
const defaultEmployee = JSON.parse(localStorage.getItem('id_employee'))
const typeAnimal = ref('')
const flgCallConfirmationModal = ref(false)
const qtDisplayRoomList = ref([])
const qtDisplayRoomListDefault = reactive([])
const cellActionRefs = ref<Record<string, HTMLElement | null>>({})
const tickerRender = ref<boolean>(true)
const filterBySelectedPurpose = ref<number>(-1)
const isTableRowSimplified = ref<boolean>(JSON.parse(localStorage.getItem('checkin_21_isTableRowSimplified') || 'false'))
const sideBarDoctorExpaned = ref<boolean>(JSON.parse(localStorage.getItem('checkin_21_sideBarDoctorExpaned') || 'true'))
const sideBarPurposeExpaned = ref<boolean>(JSON.parse(localStorage.getItem('checkin_21_sideBarPurposeExpaned') || 'true'))
let selectedQueueTicketId: number | string | null = null // for store id of queue ticket to call
const toolBarRef = ref<HTMLElement | null>(null)
const { cHeight: boxCHeight, height: boxHeight } = useHtmlBox(toolBarRef)

const columns = computed(() => [
  ...(isTableRowSimplified.value ? [] : [{
    name: 'label_time',
    label: '対応時刻',
    field: 'label_time',
    align: 'center'
  }]),
  {
    name: 'number_queue_ticket',
    label: '受付番号',
    field: 'number_queue_ticket',
    align: 'center'
  },
  {
    name: 'customer_info',
    label: 'オーナー',
    field: 'customer_info',
    align: 'center'
  },
  {
    name: 'type_status_queue_ticket',
    label: 'ステータス',
    field: 'type_status_queue_ticket',
    align: 'center'
  },
  {
    name: 'flags',
    label: '呼出',
    field: 'flags',
    align: 'center',
    style: 'width: 117px'
  },
])

const additionalFilterCount = computed(() => (sortState.value.activeSort == 'process' || !sortState.value.activeSort ? 0 : 1) + (typeAnimal.value ? 1 : 0) + (searchDoctor.value ? 1 : 0))

const clinicTypeQtRequestByCustomer = computed(() => (useClinicStore().getClinic?.type_qt_request || 0) < 2)

const clinicIdFromLs = computed(() => localStorage.getItem('id_clinic'))

const openAddModal = async () => {
  if (getDataFromCustomerPage.value?.id_customer) {
    const { id_customer, id_pet } = getDataFromCustomerPage.value
    await mtUtils.mediumPopup(UpdateQueueTicketModal, {
      data: { id_customer, id_pet: id_pet }
    })
    await refresh()
    return queueTicketStore.clearDataFromCustomerPage()
  }
  await mtUtils.mediumPopup(UpdateQueueTicketModal)
  await refresh()
}
const onRowClick = async (data, index) => {
  if (index != '7' && index != '8') {
    await mtUtils.mediumPopup(UpdateQueueTicketModal, {
      data,
      fromPage: '受付・整理券'
    })
    await refresh()
  }
}
const onFilterQTClick = async (ticketNumber: string) => {
  const queueNumber = ticketNumber.split(' ')[0]
  const qtData = getFilteredQueueTicketLists.value.find((ticket) => {
    return ticket.number_queue_ticket === queueNumber
  })
  await mtUtils.mediumPopup(UpdateQueueTicketModal, {
    data: qtData,
    fromPage: '受付・整理券'
  })
}

const onFilterDoctorClick = (data: { id: number }) => {
  searchDoctor.value = data.id.toString()
  selectedDoctor(data.id)
}
const emailSendModalOpen = async (row) => {
  await mtUtils.smallPopup(EmailModal, { queueTicketData: row })
}
const getCustomer = (row) => {
  return row?.customer
}

const getCustomerData = (row: number): Customer | undefined => {
  return row.customer
}

const visitPurpose = (type_visit_purpose_ticket: number[]) => {
  if (!type_visit_purpose_ticket || type_visit_purpose_ticket.length === 0) {
    return ''
  }
  
  return getCliCommonQTVisitPurposeList.value
    .filter((purpose) =>
      type_visit_purpose_ticket?.includes(purpose.id_cli_common)
    )
    .map((purpose) => {
      const color = getCliCommonPurposeColor(purpose.id_cli_common)
      return `<span class="purpose-name" style="color:${color}">${purpose.name_cli_common}</span>`
    })
    .join('<span class="mx-1">,</span> ')
}

const displayDoctors = (doctorsId: any[]) => {
  if (!doctorsId || doctorsId.length === 0) {
    return ''
  }
  let doctorCount = 0;
  const content = doctorsId
  .map((id) => {
    let e = employeeStore.getEmployees.find((v) => v.id_employee === id)
    if (e) {
        const color = doctorsAndEmployeesByIdWithColor.value[e.id_employee]?.color
        return `<span class="doctor-name" style="color:${color}">${e.name_family} ${e.name_first}</span>`
      }
      return ''
    })
    .join(!doctorCount ? '' : '<span class="mx-1">,</span> ')
  return content
}

const getProcessTime = (type_process_time) =>
  typeProcessTime.find((v) => v.value === type_process_time)?.label

const getStatusQueueTicket = (type_status_queue_ticket) =>
  statusQueueTicket.find((v) => v.value == type_status_queue_ticket)?.label

const selectedDoctor = async (value: number | null) => {
  await queueTicketStore.fetchQueueTicketList({
    id_employee_doctor: value,
    type_animal: typeAnimal.value,
    today: true
  })
  queueTicketStore.filterQueueTicketsByTypeAnimal(typeAnimal.value)
  setupQueueTicket(value)
}
const filterVisitPurpose = async (value) => {
  filterBySelectedPurpose.value = value ? value : -1
  setupQueueTicket()
}

const modalData = ref({ customerName: '', newStatus: '' })

const handleAutoRequestTitle = (ticketData: any) => {
  // let autoTitle = ''
  const selectedEmployeeDoctor = employeeStore.getAllEmployees.find(
    (v: any) => v.value === ticketData?.id_employee_doctor
  )?.label
  const name_customer = concatenate(
    ticketData?.customer?.name_family,
    ticketData?.customer?.name_first
  )
  const fixedTextCustomer = name_customer ? ' 様 /' : ''
  const fixedTextDoctor = selectedEmployeeDoctor ? ' 先生' : ''
  const fixedTextStaff = selectedEmployeeDoctor ? '/ 担当: ' : ''
  return (
    getDateToday() +
    ' ' +
    ticketData?.customer?.code_customer +
    ' ' +
    name_customer +
    ' ' +
    (fixedTextCustomer ?? '') +
    ' ' +
    (selectedEmployeeDoctor ?? '') +
    (fixedTextDoctor ?? '') +
    ' ' +
    (fixedTextStaff
      ? fixedTextDoctor !== ''
        ? fixedTextStaff
        : fixedTextStaff.replace('/ ', '')
      : '') +
    (selectedEmployeeDoctor ?? '')
  )
}

const filterQueueTicket = async (ticketData: any , value: string | number) => {
  const customerName = ticketData.customer.name_customer_display
  const newStatus = getStatusQueueTicket(value)
  const updatedTicketData = {
    ...ticketData,
    type_status_queue_ticket_placeholder: value
  }
  await openConfirmationModal(customerName, newStatus, updatedTicketData)
}

const openConfirmationModal = async (
  customerName: string,
  newStatus,
  ticketData
) => {
  modalData.value = { customerName, newStatus }
  // Open the modal and pass ticketData as a prop
  const title_request = handleAutoRequestTitle(ticketData)
  const confirmMsg = `${customerName ?? ''}様の受付ステータスを${newStatus}に変更しますか？`
  await mtUtils
    .confirm(confirmMsg, '確認', 'はい')
    .then(async (confirmation) => {
      if (confirmation) {


        let is_bring_order = false

        const confirmation2nd = await mtUtils.callApi(selectOptions.reqMethod.GET, 'check_order_exist', {
          id_customer: ticketData.id_customer,
          id_pet: (ticketData?.id_pet && ticketData?.id_pet[0]) ? ticketData.id_pet[0] : null
        })

        const updatedTypeStatusTicket = ticketData.type_status_queue_ticket_placeholder ?? ticketData.type_status_queue_ticket

        if (confirmation2nd && ![4, 90, 99].includes(updatedTypeStatusTicket)) {
          
          const confirm = await mtUtils.confirm(
            '対象のオーナー様に次回の会計があります。\n\n今回のリクエストに追加しますか？',
            '確認',
            'No',
            'OK'
          )
          if (confirm && confirm == 'edit') is_bring_order = true
        }

        delete ticketData.type_status_queue_ticket_placeholder
        queueTicketStore
          .updateQueueTicketList(ticketData.id_queue_ticket, {
            ...ticketData,
            title_request: title_request,
            type_visit_purpose_ticket: ticketData.type_visit_purpose_ticket,
            is_bring_order: is_bring_order,
            type_status_queue_ticket: updatedTypeStatusTicket
          })
          .then(async () => {
            // After successful update, call setupQueueTicket
            await refresh() // Call the function
            closeModal() // Close the modal after the operation
          })
      }
    })
}

const filterRowsByLink = (rows, type: any = '1_2') => {
  if (!rows) return []

  if (link.value === -1) {
    if (type == '4') {
      return rows.sort(
        (a, b) => Date.parse(b.datetime_absent) - Date.parse(a.datetime_absent)
      )
    }
    if (type == '3') {
      return rows.sort(
        (a, b) =>
          Date.parse(b.datetime_service_start) -
          Date.parse(a.datetime_service_start)
      )
    }
    if (type == '90_99') {
      return rows.sort(
        (a, b) => Date.parse(b.datetime_bin) - Date.parse(a.datetime_bin)
      )
    }
    return rows
  }

  if (link.value === -1) {
    return rows
  }

  return rows
}

// State Management
const sortState = ref({
  activeSort: localStorage.getItem('queueTicketLastSort') || 'process',
  timeDirection: 'time_asc', // Default ascending
  numberDirection: 'number_asc', // Default ascending
  processDirection: 'process_asc' // Default ascending
})

const isDragEnabled = computed(() => sortState.value.activeSort === 'process' && tab.value === '受付中')

const ticketCountByPurpose = computed(() => {
  const purposeCountList: Record<string, number> = {}

  // Get all valid id_cli_common from master
  const validPurposeIds = getCliCommonQTVisitPurposeList.value
    .filter((p) => dayjs(p.date_end).isSame(dayjs('9999/12/31'), 'year'))
    .map((p) => p.id_cli_common)
  // Initialize all counts to 0
  validPurposeIds.forEach(id => {
    purposeCountList[id.toString()] = 0
  })
  purposeCountList['all'] = 0
  let ticketHaystack: any[] = filteredRows1_2.value
  switch (tab.value) {
    case '受付中':
      ticketHaystack = filteredRows1_2.value
      break;
    case '呼出済':
      ticketHaystack = filteredRows3.value
      break;
    case '不在':
      ticketHaystack = filteredRows4.value
      break;
    case '削除':
      ticketHaystack = filteredRows90_99.value
      break;
    default:
      break;
  }
  // Count occurrences
  for (const ticket of ticketHaystack ?? []) {
    if (!ticket.queue_detail) continue
    for (const detail of Object.values(ticket.queue_detail)) {
      if (detail && Array.isArray(detail.type_purpose_list)) {
        for (const purposeId of detail.type_purpose_list) {
          if (validPurposeIds.includes(purposeId)) {
            const key = purposeId.toString()
            purposeCountList[key] = (purposeCountList[key] || 0) + 1
            purposeCountList['all'] = (purposeCountList['all'] || 0) + 1
          }
        }
      }
    }
  }
  return purposeCountList
})

// Sort handler
const handleSortTypeChange = async () => {
  switch (sortState.value.activeSort) {
    case 'process':
      sortState.value.timeDirection = 'time_asc'
      sortState.value.numberDirection = 'number_asc'
      sortState.value.processDirection = 'process_asc'
      break
    case 'time':
      sortState.value.processDirection = 'process_asc'
      sortState.value.numberDirection = 'number_asc'
      sortState.value.timeDirection = 'time_asc'
      break
    case 'number':
      sortState.value.processDirection = 'process_asc'
      sortState.value.timeDirection = 'time_asc'
      sortState.value.numberDirection = 'number_asc'
      break
  }

  // Save state immediately after change
  localStorage.setItem('queueTicketLastSort', sortState.value.activeSort ?? '')
  localStorage.setItem('queueTicketTimeSort', sortState.value.timeDirection)
  localStorage.setItem('queueTicketNumberSort', sortState.value.numberDirection)
  localStorage.setItem(
    'queueTicketProcessSort',
    sortState.value.processDirection
  )

  await handleSortChange()
}

const fillQueueTicketCallData = (queueTicket: QueueTicketType) => {
  Object.assign(queueTicketCallData, queueTicketCallDataDefault)
  if(queueTicket.queue_detail.flg_qt_calling !== undefined) {
    const { flg_qt_room_display: qtRoomDisplay, id_room: roomId } = queueTicket.queue_detail
    queueTicketCallData.flg_qt_room_display = qtRoomDisplay
    queueTicketCallData.id_room = roomId
  }
  selectedQueueTicketId = queueTicket.id_queue_ticket
  flgCallConfirmationModal.value = !flgCallConfirmationModal.value
}

const updateFlgQsCalling = async () => {
  if(!selectedQueueTicketId) 
    return mtUtils.autoCloseAlert('Please select queue ticket first')
  let payload = {
    id_queue_ticket: selectedQueueTicketId,
    ...queueTicketCallData
  }
  if(!queueTicketCallData.flg_qt_room_display || !queueTicketCallData.id_room) payload.id_room = null
  const response = await mtUtils.callApi(selectOptions.reqMethod.POST, 'queue_ticket_calling/', payload)
  if(response) {
    mtUtils.autoCloseAlert(aahMessages.success)
    flgCallConfirmationModal.value = false
    selectedQueueTicketId = null
    await queueTicketStore.fetchQueueTicketList({
      today: true,
      id_employee_doctor: searchDoctor.value
    })
    setupQueueTicket()
  }
}

const setCellACtionRef = (id: string, el: HTMLElement) => {
  if (el) cellActionRefs.value[id] = el
}

const handleCellActionToggle = (id: string) => {
  Object.keys(cellActionRefs.value).forEach((key: string) => {
    if (key.toString() === id) {
      if (cellActionRefs.value[id]?.classList.contains('action-expanded')) {
        cellActionRefs.value[id]?.classList.remove('action-expanded')
      } else {
        cellActionRefs.value[id]?.classList.add('action-expanded')
      }
    } else {
      cellActionRefs.value[key]?.classList.remove('action-expanded')
    }
  })
}

const resetRowsExpandedActions = () => {
  cellActionRefs.value = {}
  const expandedElements = document.getElementsByClassName('action-expanded')
  Array.from(expandedElements).forEach(element => {
    element.classList.remove('action-expanded')
  })
}

const calculateHourMinDiffFromNow = (dt: string, estMinType: number) => {
  if (!dt || !estMinType) return '<span>--:--</span>'
  const toLapse = typeProcessTime.find(pt => pt.value == estMinType)
  const toLapseMin = toLapse ? Number((toLapse?.enLabel)?.toLowerCase().replace('minutes', '') || 0) : 0
  if (!toLapseMin) return '<span>--:--</span>'

  const startTime = new Date(dt)
  if (isNaN(startTime.getTime())) return '<span>--:--</span>'

  const expectedEndTime = new Date(startTime.getTime() + toLapseMin * 60 * 1000)
  const now = new Date()

  const diffInMinutes = Math.floor((expectedEndTime.getTime() - now.getTime()) / (1000 * 60))
  const absMinutes = Math.abs(diffInMinutes)
  const hours = Math.floor(absMinutes / 60)
  const minutes = absMinutes % 60
  const formatted = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`

  if (diffInMinutes < 0) {
    return `<span style="color: red !important">-${formatted}</span>`
  }
  return `<span>${formatted}</span>`
}

const initLapseTicker = () => {
  nextTick(() => {
    tickerRender.value = false
  }).then(() => {
    tickerRender.value = true
  })
}

const filterByPurpose = (tickets: any[]) => {
  tickets.forEach(t => {
    _.set(t, 'type_status_queue_ticket_placeholder' , t.type_status_queue_ticket)
  })
  if(filterBySelectedPurpose.value.toString() == '-1') return tickets
  const filtered: any[] = []
  for (const ticket of tickets ?? []) {
    if (!ticket.queue_detail) continue
    for (const detail of Object.values(ticket.queue_detail)) {
      if (detail && Array.isArray(detail.type_purpose_list)) {
        for (const purposeId of detail.type_purpose_list) {
          if (purposeId.toString() == filterBySelectedPurpose.value.toString()) {
            filtered.push(ticket)
          }
        }
      }
    }
  }
  return filtered
}

const shortenStringNameFirstChar = (name: string | undefined, shorten: boolean) => {
  if (!shorten) return name;
  if (!name.length) return name;
  return name.charAt(0); // return first character
}

const getCliCommonPurposeColor = (idCommon: number | string) => {
  const purpose = getCliCommonQTVisitPurposeList.value.find( p => p.id_cli_common == idCommon)
  // @ts-ignore - not yet added to types
  return purpose && purpose.text2 ? purpose.text2 : '#000000'
}

const shortenRqCode = (rqCode: string): string => {
  if (!rqCode) return ''
  if (!rqCode.includes('-')) return rqCode
  return `RQ${rqCode.split('-')[1]}`
}

const generateRequestButton = (row: any, from: 'customer' | 'pet', petId?: string | number) => {
  const rqBtn = (rqNumber: string) => {
    return h('div', {
      class: 'qic-rq-btn text-blue caption1 regular text-grey-900 q-pt-xs',
      onClick: (event: Event) => {
        event.stopPropagation()
        openRequestDetail(row, petId)
      }
    }, [
      h('span', {}, shortenRqCode(rqNumber))
    ])
  }
  const rqBtnNoRq = () => {
    return h('div', {
      class: 'qic-rq-btn text-blue cursor-pointer',
      onClick: (event: Event) => {
        event.stopPropagation()
        openQTRQ(event, row)
      }
    }, [
      h(QIcon, { name: 'search' }, {}),
      h('span', {}, ' RQ')
    ])
  }
  if (row.request && row.request.id_request && from === 'customer' && clinicTypeQtRequestByCustomer.value) {
    return rqBtn(row.request.number_request)
  } else if (petId && row.queue_detail && row.queue_detail.request_dict && from === 'pet' && !clinicTypeQtRequestByCustomer.value) {
    const requestDict = row.queue_detail.request_dict
    const requestEntry: Record<string, any> = Object.values(requestDict).find((req: any) => req.id_pet === petId) as Record<string, any>
    if (requestEntry) {
      return rqBtn(requestEntry.number_request)
    }
  } else {
    if (from === 'customer' && clinicTypeQtRequestByCustomer.value) {
      return rqBtnNoRq()
    } else if (from === 'pet' && !clinicTypeQtRequestByCustomer.value) {
      return rqBtnNoRq()
    }
  }
}

// Utility function to check if two colors are noticeably different
const areColorsNoticeablyDifferent = (color1: string, color2: string): boolean => {
  // Extract HSL values from color strings
  const extractHSL = (color: string) => {
    const match = color.match(/hsl\((\d+),\s*(\d+)%,\s*(\d+)%\)/)
    if (match) {
      return {
        h: parseInt(match[1]),
        s: parseInt(match[2]),
        l: parseInt(match[3])
      }
    }
    return null
  }
  
  const hsl1 = extractHSL(color1)
  const hsl2 = extractHSL(color2)
  
  if (!hsl1 || !hsl2) return true // If we can't parse, assume they're different
  
  // Calculate hue difference (considering circular nature of hue)
  const hueDiff = Math.min(
    Math.abs(hsl1.h - hsl2.h),
    360 - Math.abs(hsl1.h - hsl2.h)
  )
  
  // Calculate saturation and lightness differences
  const satDiff = Math.abs(hsl1.s - hsl2.s)
  const lightDiff = Math.abs(hsl1.l - hsl2.l)
  
  // Colors are noticeably different if:
  // - Hue difference is at least 90 degrees (much more different)
  // - Combined saturation and lightness difference is at least 40
  return hueDiff >= 90 || (satDiff + lightDiff) >= 40
}

// This ensures consistent color assignments across sessions and handles color uniqueness
const getEmployeeColorManager = () => {
  const STORAGE_KEY = 'employee_colors'
  
  // Get all stored color assignments from localStorage
  const getStoredColors = (): Record<string, string> => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY)
      return stored ? JSON.parse(stored) : {}
    } catch {
      return {}
    }
  }
  
  // Save color assignments to localStorage
  const saveColors = (colors: Record<string, string>) => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(colors))
    } catch (error) {
      console.warn('Failed to save employee colors to localStorage:', error)
    }
  }
  
  // Generate a unique color that's noticeably different from existing colors
  const generateUniqueColor = (usedColors: Set<string>): string => {
    let attempts = 0
    const maxAttempts = 200 // Increased attempts for better uniqueness
    
    while (attempts < maxAttempts) {
      const newColor = generateRandomColor()
      
      // Check if this color is noticeably different from all existing colors
      let isUnique = true
      for (const existingColor of usedColors) {
        if (!areColorsNoticeablyDifferent(newColor, existingColor)) {
          isUnique = false
          break
        }
      }
      
      if (isUnique) {
        return newColor
      }
      attempts++
    }
    
    // If we can't find a unique color after max attempts, 
    // generate one with more variation
    const hue = Math.floor(Math.random() * 360)
    const saturation = 60 + Math.floor(Math.random() * 30) // 60-90%
    const lightness = 50 + Math.floor(Math.random() * 20) // 50-70%
    return `hsl(${hue}, ${saturation}%, ${lightness}%)`
  }
  
  // Assign a color to an employee with priority: data > localStorage > new unique color
  const assignColorToEmployee = (employeeId: string | number, existingColor?: string): string => {
    const storedColors = getStoredColors()
    const employeeIdStr = employeeId.toString()
    
    // Priority 1: If employee already has a color in data, use it and save to storage
    if (existingColor) {
      storedColors[employeeIdStr] = existingColor
      saveColors(storedColors)
      return existingColor
    }
    
    // Priority 2: If employee has a color in localStorage, use it
    if (storedColors[employeeIdStr]) {
      return storedColors[employeeIdStr]
    }
    
    // Priority 3: Generate a new unique color
    const usedColors = new Set(Object.values(storedColors))
    const newColor = generateUniqueColor(usedColors)
    
    // Save the new color assignment
    storedColors[employeeIdStr] = newColor
    saveColors(storedColors)
    
    return newColor
  }
  
  // Clear all stored color assignments (useful for resetting)
  const clearAllColors = () => {
    try {
      localStorage.removeItem(STORAGE_KEY)
    } catch (error) {
      console.warn('Failed to clear employee colors from localStorage:', error)
    }
  }
  
  // Manually update an employee's color
  const updateEmployeeColor = (employeeId: string | number, newColor: string) => {
    const storedColors = getStoredColors()
    const employeeIdStr = employeeId.toString()
    storedColors[employeeIdStr] = newColor
    saveColors(storedColors)
  }
  
  return {
    assignColorToEmployee,
    getStoredColors,
    clearAllColors,
    updateEmployeeColor
  }
}

const handleTableRowUiSimplification = (value:boolean) => {
  isTableRowSimplified.value = value
  localStorage.setItem('checkin_21_isTableRowSimplified', value.toString())
}

const handleSideBarDoctorExpaned = () => {
  sideBarDoctorExpaned.value = !sideBarDoctorExpaned.value
  localStorage.setItem('checkin_21_sideBarDoctorExpaned', sideBarDoctorExpaned.value.toString())
}

const handleSideBarPurposeExpaned = () => {
  sideBarPurposeExpaned.value = !sideBarPurposeExpaned.value
  localStorage.setItem('checkin_21_sideBarPurposeExpaned', sideBarPurposeExpaned.value.toString())
}

const syncDatetimeEstimate = async() => {
  await queueTicketStore.syncDatetimeEstimate()
  refresh()
}

const handleOpenAdditionalFilterModal = async () => {
  await mtUtils.smallPopup(AdditionalFilterQtModal, <AdditionalFilterQtModalProps>{
    search: {
      sortState: sortState.value.activeSort,
      typeAnimal: typeAnimal.value,
      searchDoctor: searchDoctor.value
    },
    searchCallback: async ( action: 'save' | 'none' | 'reset', data: AdditionalFilterQtModalPropsSearch) => {
      if (action === 'save') {
        sortState.value.activeSort = data.sortState
        typeAnimal.value = data.typeAnimal
        searchDoctor.value = data.searchDoctor
        await selectedDoctor(searchDoctor.value ? null : Number(searchDoctor.value))
        handleSortTypeChange()
      } else if (action === 'reset') {
        handleClearAdditonalFilterSearch()
      }
    }
  })
}

const handleClearAdditonalFilterSearch = () => {
  sortState.value.activeSort = SORT_TYPE_DEFAULT
  typeAnimal.value = ''
  searchDoctor.value = ''
  clearDoctorFilter()
  handleSortTypeChange()
}

// Mount hook
onMounted(async() => {
  // Apply saved sort if exists
  if (sortState.value.activeSort !== SORT_TYPE_DEFAULT) {
    handleSortTypeChange()
  }

  await roomStore.fetchPreparationRooms()
  qtDisplayRoomList.value = getAllRooms.value
   .filter((room: RoomType) => room.flg_qt_room_display)
  qtDisplayRoomListDefault.push(...qtDisplayRoomList.value)
  // initLapseTicker()

  // lapseTickerInterval = setInterval(() => {
  //   initLapseTicker()
  // }, 1000)
})

// Add sortByOption function
const sortByOption = (rows: any[]) => {
  if (!rows) return []

  const direction =
    sortState.value.activeSort === 'process'
      ? sortState.value.processDirection
      : sortState.value.activeSort === 'number'
      ? sortState.value.numberDirection
      : sortState.value.timeDirection

  return [...rows].sort((a, b) => {
    switch (sortState.value.activeSort) {
      case 'process':
        return direction === 'process_asc'
          ? a.process_order - b.process_order
          : b.process_order - a.process_order

      case 'number':
        const numA = parseInt(a.number_queue_ticket)
        const numB = parseInt(b.number_queue_ticket)
        return direction === 'number_asc' ? numA - numB : numB - numA

      case 'time':
        const timeA = new Date(a.datetime_issued).getTime()
        const timeB = new Date(b.datetime_issued).getTime()
        return direction === 'time_asc' ? timeA - timeB : timeB - timeA

      default:
        return 0
    }
  })
}

// Update computed properties that use sortByOption
const filteredRows90_99 = computed(() => {
  const filtered = filterRowsByLink(rows90_99.value, '90_99')
  return sortByOption(filtered)
})

const filteredRows1_2 = computed(() =>
  sortByOption(filterRowsByLink(rows1_2.value, '1_2'))
)
const filteredRows3 = computed(() =>
  sortByOption(filterRowsByLink(rows3.value, '3'))
)
const filteredRows4 = computed(() =>
  sortByOption(filterRowsByLink(rows4.value, '4'))
)

const absentQueueTicketList = computed(() => {
  if (getFilteredQueueTicketLists.value.length) {
    return getFilteredQueueTicketLists.value
      .filter((v: any) => v.flg_auto_absent_updated && !v.flg_absent_confirmed)
      .sort(
        (a, b) => Date.parse(b.datetime_absent) - Date.parse(a.datetime_absent)
      )
  }
  return null
})

const isIpad = computed(() => {
  return Platform.is.ipad
})

const doctorsById = computed(() => {
  return keyBy(employeeStore.getDoctors, 'id_employee')
})

const employeesById = computed(() => {
  return keyBy(employeeStore.getEmployees, 'id_employee')
})

const doctorsAndEmployeesByIdWithColor = computed(() => {
  const colorManager = getEmployeeColorManager()
  
  return keyBy([
    ...employeeStore.getDoctors,  
    ...employeeStore.getEmployees
  ].map((v) => {
    return {
      ...v,
      color: colorManager.assignColorToEmployee(v.id_employee, v.color_emp)
    }
  }),
  'id_employee')
})


const setupQueueTicket = async (doctorId?: number | null) => {
  resetRowsExpandedActions()
  const ticketsWithColor = getFilteredQueueTicketLists.value.map((ticket, index) => {
    const doctor = ticket?.queue_detail?.[ticket?.id_pet[0]]?.type_doctor_list?.[0]
    return {
      ...ticket,
      color: doctorsAndEmployeesByIdWithColor.value[doctor]?.color
    }
  })
  const res1_2 = ticketsWithColor.filter((ticket) => {
    return (
      Number(ticket.type_status_queue_ticket) === 1 ||
      Number(ticket.type_status_queue_ticket) === 2
    )
  })
  status1_2.value = res1_2?.length
  rows1_2.value = res1_2 ? res1_2 : []
  totalStatus.value = ticketsWithColor.filter((ticket) => {
    return Number(ticket.type_status_queue_ticket) === 2
  })?.length
  const res3 = ticketsWithColor.filter(
    (ticket) => Number(ticket.type_status_queue_ticket) === 3
  )
  status3.value = res3?.length
  rows3.value = res3 ? res3 : []
  const res4 = ticketsWithColor.filter(
    (ticket) => Number(ticket.type_status_queue_ticket) === 4
  )
  status4.value = res4?.length
  rows4.value = res4 ? res4 : []
  const res90_99 = ticketsWithColor.filter((ticket) => {
    return (
      Number(ticket.type_status_queue_ticket) === 90 ||
      Number(ticket.type_status_queue_ticket) === 99
    )
  })
  status90_99.value = res90_99?.length
  rows90_99.value = res90_99 ? res90_99 : []
  doctors.value = []
  const availableDoctors: Record<number, {
    id: number;
    name: string;
    count: number;
    qt_list: string[];
    color: string;
  }> = {}

  const getDoctorInfo = (id_doctor: number) => {
    if (id_doctor === 0) {
      return { id: 0, name: '担当なし', color: '#9e9e9e' }
    }
    const doctor = doctorsAndEmployeesByIdWithColor.value[id_doctor]
    if (doctor) {
      return { id: id_doctor, name: doctor.name_display, color: doctor.color }
    }
    const employee = doctorsAndEmployeesByIdWithColor.value[id_doctor]
    if (employee) {
      return { 
        id: id_doctor, 
        name: `${employee.name_family} ${employee.name_first}`, 
        color: employee.color 
      }
    }
    return { id: 0, name: '担当なし', color: '#9e9e9e' }
  }

  const initializeDoctor = (id_doctor: number) => {
    const { id, name, color } = getDoctorInfo(id_doctor)
    availableDoctors[id] = {
      id,
      name,
      count: 0,
      qt_list: [],
      color
    }
  }

  const processDoctorTicket = (doctor: any, ticket: any, petId: string | number) => {
    let hasIncremented: boolean = false
    if (doctor.id > 0) {
      // if (false) {
        doctor.count++
        hasIncremented = true
      } else {
      //counting no doctors
      if (ticket.petList?.find( (p: any) => p.id_pet == petId)) {
        const petQdetail = ticket.queue_detail[petId];
        // _.set(petQdetail , 'type_doctor_list', [-1])
        const doctorCountPerPet = Array.isArray(petQdetail?.type_doctor_list) ? petQdetail.type_doctor_list.length : 0;
        if ((doctorCountPerPet < 1 || (doctorCountPerPet === 1 && !!petQdetail?.type_doctor_list[0])) && petId) {
          hasIncremented = true
          doctor.count++
        }
      }
    }
    if(!hasIncremented) return
    const ticketInfo = `${ticket.number_queue_ticket} ${ticket.customer.code_customer || ''} ${ticket.customer.name_customer_display || ''}`
    if (!doctor.qt_list.includes(ticketInfo)) {
      doctor.qt_list.push(ticketInfo)
    }
  }

  const processTicketDoctors = (ticket: any, doctorId?: number | null) => {
    if (!ticket.queue_detail) return

    Object.entries(ticket.queue_detail).forEach(([petId, detail]) => {
      const doctorList = detail?.type_doctor_list?.length >= 1 
      ? detail.type_doctor_list 
      : [0]

      doctorList.forEach(id_doctor => {
        // Skip if specific doctor is selected and this isn't it
        if (doctorId !== undefined && doctorId !== null && id_doctor !== doctorId) return

        // Initialize doctor if not exists
        const preDoctorId = id_doctor < 1 ? 0 : id_doctor
        if (!availableDoctors[preDoctorId]) {
          initializeDoctor(preDoctorId)
        }

        // Process ticket for this doctor
        const doctor = availableDoctors[preDoctorId]
        if (doctor) {
          processDoctorTicket(doctor, ticket, petId)
        }
      })
    })
  }

  // Initialize selected doctor if specified
  if (doctorId !== undefined && doctorId !== null) {
    initializeDoctor(doctorId)
  }

  // Process tickets
  ticketsWithColor
    .filter(ticket => [1, 2].includes(Number(ticket.type_status_queue_ticket)))
    .forEach(ticket => processTicketDoctors(ticket, doctorId))

    doctors.value = Object.values(availableDoctors)
    doctors.value = Object.values(availableDoctors).filter((d) => {
      return searchDoctor.value ? d.id == searchDoctor.value : true  
    })
}

const openRequestDetail = (row: Record<string, any>, petId?: string | number) => {
  const idRequest = petId ? Object.values(row.queue_detail?.request_dict ?? {}).find((r: any) => r.id_pet == petId)?.id_request : row['request'].id_request
  if(!idRequest) return
  let query = null

  if (row && row.queue_detail && row.queue_detail.request_dict) {
    query = {
      id_customer: row.id_customer,
      id_pet: row.queue_detail?.request_dict[idRequest]?.id_pet,
      is_new_tab: row.queue_detail?.request_dict[idRequest]?.id_pet ? true : null
    }
  }
  
  
  const route = router.resolve({
    name: 'RequestDetail',
    params: { id: idRequest },
    query: query
  })
  window.open(route.href, '_blank')
}

const handleStatus = (data: any, status: String) => {
  data[status] = !data[status]
  queueTicketStore
    .updateQueueTicketList(data.id_queue_ticket, data)
    .then(async () => {
      await queueTicketStore.fetchQueueTicketList({
        today: true,
        id_employee_doctor: searchDoctor.value
      })
      setupQueueTicket()
    })
}

const refresh = async () => {
  if (typeAnimal.value) {
    await queueTicketStore.fetchQueueTicketList({
      today: true,
      id_employee_doctor: searchDoctor.value
    })
    return await selectAnimalType(typeAnimal.value)
  }
  await queueTicketStore.fetchQueueTicketList({
    today: true,
    id_employee_doctor: searchDoctor.value
  })
  setupQueueTicket()
}


const updateFlgAbsent = async (ticketData: any) => {
  await mtUtils.promiseAllWithLoader([
    queueTicketStore.updateQueueTicketList(ticketData.id_queue_ticket, {
      flg_absent_confirmed: true
    })
  ])
  queueTicketStore.fetchQueueTicketList({
    today: true,
    id_employee_doctor: searchDoctor.value
  })
  setupQueueTicket()
}

let timeoutId
let intervalId
let lapseTickerInterval

const startIntervalAtNextMinute = () => {
  const now = new Date()
  const nextMinute = new Date(now.getTime() + 60000)
  nextMinute.setSeconds(0, 0)

  const timeUntilNextMinute = nextMinute - now

  timeoutId = setTimeout(() => {
    refresh() // Call the API immediately at the start of the next minute
    intervalId = setInterval(refresh, 60000) // Set an interval to call the API every 60 seconds
  }, timeUntilNextMinute)
}

const clearDoctorFilter = async () => {
  searchDoctor.value = ''
  doctors.value = [] // Reset doctors list
  await selectedDoctor(null)
}

const openQTRQ = async (event: any, row: any) => {
  event.stopPropagation()
  await mtUtils.smallPopup(UpdQtRequest, { queueTicket: row })
  await mtUtils.promiseAllWithLoader([refresh()])
}

watch(
  () => queueTicketStore.openUpdateModal,
  (nowValue) => {
    if (nowValue) {
      openAddModal()
      actionStore.resetAction()
      localStorage.removeItem('pageAction')
      queueTicketStore.openUpdateModal = false
    }
  }
)

const handleSortChange = async () => {
  await refresh()

  const result = sortByOption(rows1_2.value)
}

const fetchCustomersWithPets = async (page: number = 1) => {
  await customerStore.fetchCustomersWithPets({ page })
}

const getCustomerInfoLabelProps = (row) => {
  return {
    code: row?.code_customer,
    fullKanaName: `${row?.name_kana_family} ${row?.name_kana_first}`,
    fullName: `${row?.name_family} ${row?.name_first}`,
    colorType: row?.type_customer_color,
  }
}

onMounted(async () => {
  await commonStore.fetchPreparationCommonList(
    { code_common: [1, 13, 29] },
    true
  )
  await cliCommonStore.fetchPreparationCliCommonList(
    { code_cli_common: [4] },
    true
  )
  await queueTicketStore.fetchQueueTicketList({
    today: true,
    id_employee_doctor: searchDoctor.value
  })
  await fetchCustomersWithPets()
  if (
    action.value === 'createQueueTicket' ||
    localStorage.getItem('pageAction') === 'createQueueTicket'
  ) {
    openAddModal()
    actionStore.resetAction()
    localStorage.removeItem('pageAction')
    queueTicketStore.openUpdateModal = false
  }

  queueTicketStore.filterQueueTicketsByTypeAnimal(typeAnimal.value)
  setupQueueTicket()

  startIntervalAtNextMinute()

  typeAnimalOptions.value = getCommonTypeAnimalOptionList.value
    .filter((op) => {
      return op.value !== 3
    })
    .map((opt) => {
      if (opt.value === 1) {
        return {
          label: '全て',
          value: ''
        }
      }
      if (opt.value === 2) {
        return {
          label: '犬',
          value: '00'
        }
      }
      return {
        label: opt.label,
        value: opt.value
      }
    })
  // set page title
  setPageTitle('受付・整理券')

  // Apply saved sort if exists
  if (sortState.value.activeSort !== 'process') {
    await handleSortChange()
  }
})

const selectAnimalType = async (val: number | string) => {
  queueTicketStore.filterQueueTicketsByTypeAnimal(val)
  await setupQueueTicket()
}

const openPetDetailModal = async (row) => {
  await mtUtils.popup(ViewPetDetailModal, {
    id_customer: row.id_customer,
    id_pet: row.id_pet
  })
}

const openStatusBoardListModal = async () => {
  await nextTick(() => {
    mtUtils.popup(SearchStatusBoardListModal, {})
  })
}

// Add process order tracking
const currentProcessOrder = ref('asc')

// Notification handler
const showDragDropWarning = () => {
  $q.notify({
    message:
      '順番の並び替えを実行するには「処理順」のフィルターを適応してください',
    color: 'primary',
    icon: 'info',
    position: 'top',
    timeout: 4000,
    actions: [{ icon: 'close', color: 'white' }]
  })
}

// Add drag attempt handler
const handleDragAttempt = () => {
  resetRowsExpandedActions()
  if (!isDragEnabled.value) {
    showDragDropWarning()
  }
}

const openHelpMenu1 = async () => {
  await mtUtils.mediumPopup(MtToolTipsSmall, {
    title: queueTicketHelperContents.queueTicketViewPage1.title,
    content: queueTicketHelperContents.queueTicketViewPage1.content,
  })
}

const onDrop = async (dropEvent: any) => {
  // Get current sorted rows
  const currentRows = [...filteredRows1_2.value]
  
  const payload = {
    queue_ticket_list: currentRows.map(ticket => ticket.id_queue_ticket)
  }

  try {
    await mtUtils.callApi(
      selectOptions.reqMethod.POST,
      'UpdQueueTicketOrder',
      payload
    )
    await refresh()
  } catch (error) {
    console.error('Reorder failed:', error)
    await refresh()
  }
}

const getDayName = (dateString: string) => {
  const date = new Date(dateString).getDay()
  return typeWeekday[date].label
}

onUnmounted(() => {
  if (timeoutId) clearTimeout(timeoutId)
  if (intervalId) clearInterval(intervalId)
  if (lapseTickerInterval) clearInterval(lapseTickerInterval)
})
</script>
<template>
  <div>
    <MtHeader :notSticky="false">
      <q-toolbar class="text-primary q-pa-none">
        <q-toolbar-title class="title2 bold text-grey-900">
          <span>受付</span>
          <span class="body2 q-ml-lg">{{
            todaysDate + ' - ' + getDayName(todaysDate)
          }}</span>
          <q-btn dense flat round @click="openHelpMenu1" class="q-mx-sm">
            <q-icon size="24px" name="help_outline" />
          </q-btn>
        </q-toolbar-title>
        <div
          v-if="useClinicStore().getClinic?.min_auto_absent > 0"
          class="col-auto "
        >
          自動不在 : {{ useClinicStore()?.getClinic?.min_auto_absent }} 分
        </div>
        <div v-else class="col-auto q-pr-md">自動不在なし</div>
        <MtFormCheckBox
          label="シンプル"
          v-model:checked="isTableRowSimplified"
          @update:checked="handleTableRowUiSimplification"
        />
        <div class="flex items-center q-mr-md">
          <q-btn
            padding="5px 5px"
            round
            unelevated
            color="white"
            text-color="primery"
            class="q-ml-sm"
            @click="refresh"
          >
            <q-icon size="20px" name="refresh" />
          </q-btn>
        </div>
        <q-btn outline @click="syncDatetimeEstimate" class="q-mr-md">
          (予) 更新
        </q-btn>
        <q-btn outline @click="handleOpenAdditionalFilterModal">
          詳細検索
          <span
            v-if="additionalFilterCount > 0"
            class="q-badge q-badge--floating q-badge--top q-badge--right text-white bg-red"
          >
            {{ additionalFilterCount }}
          </span>
        </q-btn>
        <q-btn
          class="q-mx-sm"
          color="grey-100"
          outline
          text-color="primary"
          unelevated
          @click="handleClearAdditonalFilterSearch()"
        >
          クリア
        </q-btn>
        <q-btn class="q-mx-sm" outline @click="openStatusBoardListModal">
          <q-icon name="people" class="q-mr-xs" />
          ステータスボード
        </q-btn>

        <q-btn
          unelevated
          color="primary"
          text-color="white"
          class="q-ml-xs"
          @click="openAddModal"
        >
          <q-icon size="20px" name="add" />
          受付
        </q-btn>
      </q-toolbar>
    </MtHeader>
    <div class="q-px-md">
      <div class="q-px-md content q-mb-md" style="height: calc(100vh - 49px); overflow-y: hidden;">
        <div class="flex column full-width full-height">
          <div class="col-auto full-width q-pt-lg" ref="toolBarRef" v-if="absentQueueTicketList?.length" >
            <div class="q-mt-md absent-box">
              <div
                v-for="queue in absentQueueTicketList"
                :key="queue.id_queue_ticket"
                class="q-pa-xs absent-item"
              >
                <span>
                  [自動不在]　 呼出
                  {{ formatDateWithTime(queue.datetime_absent, 'HH:mm') }}
                </span>
                <span class="q-ml-md">
                  {{ queue.number_queue_ticket }} /
                  {{ getCustomerName(queue.customer) }} 様 /
                  {{
                    (queue?.petList && queue?.petList.length > 0) ? `${queue?.petList[0]?.id_pet} ${queue?.petList[0]?.name_pet}` : ''
                  }}　　 {{ useClinicStore()?.getClinic?.min_auto_absent }} 分間不在 
                </span>
                <q-btn
                  class="q-ml-lg"
                  color="primary"
                  size="sm"
                  text-color="white"
                  unelevated
                  @click="updateFlgAbsent(queue)"
                >
                  確認しました
                </q-btn>
              </div>
            </div>
          </div>
          <div class="col full-width">
            <div class="row q-col-gutter-md q-mt-xs full-height">
              <div class="purpose-sidebar-wrapper col-auto full-height overflow-hidden" :class="{ 'sidebar-toggler-expanded' : sideBarPurposeExpaned }">
                <div class="sidebar-toggler-wrapper">
                  <div class="sidebar-info col">
                    <div class="sidebar-icon-wrapper q-mr-xs">
                      <q-icon class='text-blue-700' size="20px" name="spa" />
                    </div>
                    <span class="caption1 regular text-grey-800">目的</span>
                    <span class="sidebar-group-count q-mx-sm title1 bold text-grey-900">
                    </span>
                  </div>
                  <div class="sidebar-toggler" @click="handleSideBarPurposeExpaned">
                    <q-icon class="toggler-icon" size="20px" name="chevron_left"  />
                  </div>
                </div>
                <div style="height:100%; overflow-y: auto;">
                  <q-list separator>
                    <q-item
                      clickable
                      v-ripple
                      :active="link === -1"
                      class="sp-list-item-wrapper"
                      active-class="active-menu"
                      @click=";(link = -1), filterVisitPurpose('')"
                    >
                      <q-item-section>全て</q-item-section>
                    </q-item>
                    <q-item
                      clickable
                      v-ripple
                      :active="link === visit.id_cli_common"
                      active-class="active-menu"
                      class="sp-list-item-wrapper"
                      @click="
                        ;(link = visit.id_cli_common),
                          filterVisitPurpose(visit.id_cli_common)
                      "
                      v-for="(
                        visit, index
                      ) in getCliCommonQTVisitPurposeList.filter((p) =>
                        dayjs(p.date_end).isSame(dayjs('9999/12/31'), 'year') && p.id_clinic == (clinicIdFromLs ?? 0)
                      )"
                      :key="index"
                    >
                      <q-item-section>
                        <div class="sp-list-item row gap-2 justify-between">
                          <span
                            :class="{
                              'text-grey-7': !!!ticketCountByPurpose[visit.id_cli_common.toString()]
                            }"
                          >
                            {{ shortenStringNameFirstChar((visit.label ?? '').toString() , !sideBarPurposeExpaned) }}
                            <q-icon name="schedule" style="color: #373434" v-if="visit.flg_etc1"/>
                          </span>
                          <q-badge 
                            class="q-pa-xs items-center justify-center"
                            :style="{
                              'min-width': '25px',
                              'border-radius': '10px !important',
                              'background': getCliCommonPurposeColor(visit.id_cli_common),
                              'color': 'white'
                            }"
                            :label="ticketCountByPurpose[visit.id_cli_common.toString()] ?? 0" 
                            v-if="!!ticketCountByPurpose[visit.id_cli_common.toString()]"
                          />
                        </div>
                      </q-item-section>
                    </q-item>
                  </q-list>
                </div>
              </div>
              <div class="col full-height">
                <div class="row items-center q-mb-sm gap-2">
                  <div class="col-auto">
                    <q-tabs
                      v-model="tab"
                      dense
                      inline-label
                      indicator-color="grey-300"
                      active-bg-color="blue-700"
                      active-class="text-white bold body1"
                      class="ticket-tabs"
                    >
                      <q-tab name="受付中" :label="'受付中 (' + status1_2 + ')'" />
                      <q-tab name="呼出済" :label="'呼出済 (' + status3 + ')'" />
                      <q-tab name="不在" :label="'不在 (' + status4 + ')'" />
                      <q-tab name="削除" :label="'削除 (' + status90_99 + ')'" />
                    </q-tabs>
                  </div>
                </div>
                <div class="row q-col-gutter-xs">
    
                  <div class="col">
                    <q-tab-panels v-model="tab" animated>
                      <q-tab-panel
                        :name="tabData"
                        class="q-pt-none q-px-xs"
                        v-for="(tabData, index) in tabItems"
                        :key="index"
                      >
                        <MtTable2
                          :columns="columns"
                          :rows="
                            index == 0
                              ? filterByPurpose(filteredRows1_2)
                              : index == 1
                              ? filterByPurpose(filteredRows3)
                              : index == 2
                              ? filterByPurpose(filteredRows4)
                              : filterByPurpose(filteredRows90_99)
                          "
                          row-key="name"
                          :classTd="'bg-grey-300 caption2 bold text-grey-800'"
                          :rowsBg="true"
                          flat
                          separator="cell"
                          bordered
                          :style="{
                            height: isIpad
                              ? `calc(100vh - ${160 + (boxHeight || boxCHeight)}px)`
                              : `calc(100vh - ${130 + (boxHeight || boxCHeight)}px)`,
                            boxShadow: 'none'
                          }"
                          :draggable="isDragEnabled"
                          :allowDrop="isDragEnabled"
                          :class="{ 'no-drag': !isDragEnabled }"
                          @onDrop="onDrop"
                          @dragAttempt="handleDragAttempt"
                          @dragstart="handleDragAttempt"
                          :classRowCallback="(row) => {
                            return [`row-is-type-${row['type_status_queue_ticket']}`]
                          }"
                        >
                          <template v-slot:row="{ row }">
                            <td
                              class="cursor-pointer"
                              :class="row['flg_appointment'] ? 'bg-accent-200' : ''"
                              v-for="(col, index) in columns"
                              :key="index"
                              :data-index="index"
                              @click="onRowClick(row, index)"
                            >
                              <div
                                v-if="col.field == 'label_time'"
                                :class="{ 'qt-row-is-simplified': isTableRowSimplified}"
                                key="label_time"
                              >
                                <div class="column no-wrap">
                                  <div v-if="row['datetime_issued']">
                                    <span
                                      class="qt-time-label"
                                      >整</span
                                    >
                                    <span
                                      class="caption1 regular text-grey-900"
                                      >{{
                                        formatHoursMinutes(row['datetime_issued'])
                                      }}</span
                                    >
                                  </div>
                                  <div v-if="row['datetime_check_in']">
                                    <span
                                      class="qt-time-label text-weight-bold"
                                      >受
                                    </span>
                                    <span
                                      class="caption1 bold text-grey-900"
                                      >{{
                                        formatHoursMinutes(row['datetime_check_in'])
                                      }}</span
                                    >
                                  </div>
                                  <div v-if="row['datetime_service_start']">
                                    <span
                                      class="qt-time-label"
                                      >呼</span
                                    >
                                    <span
                                      class="caption1 regular text-grey-900"
                                      >{{
                                        formatHoursMinutes(
                                          row['datetime_service_start']
                                        )
                                      }}</span
                                    >
                                  </div>
                                  <div v-if="row['datetime_absent']">
                                    <span
                                      class="qt-time-label text-darkred"
                                      >不</span
                                    >
                                    <span
                                      class="caption1 regular text-grey-900"
                                      >{{
                                        formatHoursMinutes(row['datetime_absent'])
                                      }}</span
                                    >
                                  </div>
                                  <div v-if="row['datetime_cancel']">
                                    <span
                                      class="qt-time-label"
                                      >C</span
                                    >
                                    <span
                                      class="caption1 regular text-grey-900"
                                      >{{
                                        formatHoursMinutes(row['datetime_cancel'])
                                      }}</span
                                    >
                                  </div>
                                  <div v-if="row['datetime_bin']">
                                    <span
                                      class="qt-time-label"
                                      >削</span
                                    >
                                    <span
                                      class="caption1 regular text-grey-900"
                                      >{{
                                        formatHoursMinutes(row['datetime_bin'])
                                      }}</span
                                    >
                                  </div>
                                  <div 
                                    v-if="row['type_status_queue_ticket'] === 1 || row['type_status_queue_ticket'] === 2"
                                    class="q-mt-sm"
                                  >
                                    <span class="qt-expected-time-label">
                                      <small class="q-mr-xs">見込</small>
                                      <span v-html="calculateHourMinDiffFromNow(row['datetime_issued'], row['type_process_time'] ?? 0)" v-if="tickerRender"></span>
                                    </span>
                                  </div>
                                </div>
                              </div>
                              <div
                                v-if="col.field == 'number_queue_ticket'"
                                key="number_queue_ticket"
                              >
                                <div class="queue-info-col-wrapper column items-center no-wrap" :class="{ 'qt-row-is-simplified': isTableRowSimplified}">
                                  <div v-if="row['number_queue_ticket']">
                                    <div class="q-mb-xs flex items-center justify-center">
                                      <span class="num-qt-box q-pr-sm">
                                        {{row['number_queue_ticket'] }}
                                      </span>
                                      <span
                                        v-if="
                                          row['type_status_queue_ticket'] === 1 ||
                                          row['type_status_queue_ticket'] === 2
                                        "
                                        :style="{
                                          backgroundColor:
                                            row['color'] ?? '#9e9e9e'
                                        }"
                                        class="num-process"
                                      >
                                        {{ row['process_order'] }}
                                      </span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                              <div
                                v-if="col.field == 'customer_info'"
                                key="customer_info"
                              >
                                <div class="column customer-info-col-wrapper" :class="{ 'qt-row-is-simplified': isTableRowSimplified}">
                                  <div class="customer-info-col row" style="column-gap: 10px;">
                                    <div class="cic-customer col-auto">
                                      <div class="row gap-1 full-width">
                                        <div class="cic-customer-label-wrapper column gap-2 full-width">
                                          <MtCustomerInfoLabel
                                            v-if="row?.customer"
                                            :customer="getCustomerInfoLabelProps(row.customer)"
                                            :show-customer-code="!isTableRowSimplified"
                                          >
                                            <template #fullname>
                                              <div class="row items-center no-wrap">
                                                <span class="caption1 bold text-grey-900">
                                                  {{
                                                    getCustomerName(row?.customer)
                                                  }}
                                                </span>
                                                <small class="q-ml-xs">様</small>
                                                <q-badge
                                                  v-if="
                                                    getCustomerLabelColor(row?.customer?.type_customer_color)
                                                  "
                                                  rounded
                                                  :color="
                                                    getCustomerLabelColor(
                                                      row?.customer?.type_customer_color
                                                    )
                                                  "
                                                  style="width: 12px; height: 12px"
                                                  :style="{
                                                    color: getCustomerLabelColor(
                                                      row?.customer?.type_customer_color
                                                    ),
                                                    backgroundColor: getCustomerLabelColor(
                                                      row?.customer?.type_customer_color
                                                    )
                                                  }"
                                                  class="q-ml-sm"
                                                />
                                              </div>
                                            </template>
                                            <template #customerColorIcon>
                                              <!-- This is to hide the color icon from MtCustomerInfoLabel -->
                                              <div style="display: none;"></div>
                                            </template>
                                          </MtCustomerInfoLabel>
                                          <div class="row flex item-center justify-around">
                                            <component :is="() => generateRequestButton(row, 'customer')"
                                                       class="text-weight-bold"></component>
                                            <q-icon v-if="clinicTypeQtRequestByCustomer" name="refresh"
                                                    @click.stop="(event)=>openQTRQ(event, row)"></q-icon>
                                          </div>
                                        </div>
                                      </div>
                                      <div class="cic-customer-badges-wrapper row gap-2">
                                        <div
                                          v-if="row['flg_new_customer']"
                                          class="q-mb-xs"
                                        >
                                          <q-badge
                                            class="bg-ticket1 caption2 bold justify-center custom-badge"
                                            text-color="white"
                                            label="新"
                                            rounded
                                          />
                                        </div>
                                        <div
                                          v-if="row['flg_visit_for_pet']"
                                          class="q-mb-xs"
                                        >
                                          <q-badge
                                            class="bg-ticket3 caption2 bold justify-center custom-badge"
                                            text-color="white"
                                            label="見"
                                            rounded
                                          />
                                        </div>
                                        <div
                                          v-if="row['flg_appointment']"
                                          class="q-mb-xs"
                                        >
                                          <q-badge
                                            class="bg-ticket2 caption2 bold justify-center custom-badge"
                                            text-color="white"
                                            label="予"
                                            rounded
                                          />
                                        </div>
                                        <div
                                          v-if="row['flg_web_payment_requested']"
                                          class="q-mb-xs"
                                        >
                                          <q-badge
                                            class="bg-ticket4 caption2 bold justify-center custom-badge"
                                          >
                                            <q-icon name="payment" color="white" />
                                          </q-badge>
                                        </div>
                                        <div
                                          v-if="row?.queue_detail?.flg_via_pps"
                                          class="q-mb-xs"
                                        >
                                          <q-badge
                                            class="bg-primary caption2 bold justify-center custom-badge"
                                          >
                                            <q-icon name="aod" color="white" />
                                          </q-badge>
                                        </div>
                                      </div>
                                    </div>
                                    <div class="cic-customer-other col">
                                      <div class="cic-customer-pets">
                                        <div
                                          v-for="pet in row['petList']"
                                          class="cic-pet-detail-wrapper q-my-xs ellipsis row"
                                        >
                                          <div class="cic-pet-detail col-auto ellipsis">
                                            <MtPetInfoLabel 
                                              :pet="pet" 
                                              is-clickable 
                                              :hides="isTableRowSimplified ? ['kanaName', 'codePet','animalColor'] : []" 
                                              @click.stop="openPetDetailModal({ id_customer: row?.id_customer, id_pet: pet.id_pet })" />
                                            <component :is="() => generateRequestButton(row, 'pet', pet.id_pet)" class="text-weight-bold"></component>
                                          </div>
                                          <div class="cic-pet-purpose-doctor col column q-pl-md gap-2">
                                            <div class="q-ml-md full-width">
                                              <span style="text-wrap: auto;">
                                                <q-icon name="spa" class="text-grey-600 q-mr-xs" />
                                                <span v-html="visitPurpose(row?.queue_detail?.[pet.id_pet]?.type_purpose_list)"></span>
                                              </span>
                                            </div>
                                            <div class="q-ml-md full-width">
                                              <span style="text-wrap: auto;">
                                                <q-icon name="person" class="text-grey-600 q-mr-xs" style="margin-top: -2.5px;" v-if="displayDoctors(row?.queue_detail?.[pet.id_pet]?.type_doctor_list)"/>
                                                <span v-html="displayDoctors(row?.queue_detail?.[pet.id_pet]?.type_doctor_list)"></span>
                                              </span>
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                      <div class="cic-customer-bottom row full-width">
                                        <div class="col cic-co-memo-wrapper">
                                          <div class="cic-co-memo row column full-width" v-if="row['memo_customer'] || row['memo_admin'] || row['flg_tel_requested'] || row['flg_parking_wait']">
                                            <span style="text-wrap: auto;" v-if="row['flg_tel_requested']">
                                              <q-icon name="settings_phone" color="blue-7"/> 電話での呼び出し希望<br>
                                            </span>
                                            <span style="text-wrap: auto;" v-if="row['flg_parking_wait']">
                                              <q-icon name="directions_car" color="blue-7"/> 外 / 駐車場 待ち<br>
                                            </span>
                                            <span style="text-wrap: auto;" v-if="row['memo_customer']">
                                              {{  row['memo_customer'] }} <br>
                                            </span>
                                            <span style="text-wrap: auto;" v-if="row['memo_admin']">
                                              {{  row['memo_admin'] }}
                                            </span>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                              <div
                                v-if="col.field == 'type_status_queue_ticket'"
                                key="status"
                              >
                                <div
                                  class="caption1 regular text-grey-900 ellipsis"
                                  :class="{ 'qt-row-is-simplified': isTableRowSimplified}"
                                  @click.stop
                                >
                                  <MtFormPullDown
                                    v-model:selected="
                                      row['type_status_queue_ticket_placeholder']
                                    "
                                    :clearable="false"
                                    :options="statusQueueTicket"
                                    outlined
                                    @update:selected="(value) => filterQueueTicket(row, value)"
                                  />
                                </div>
                              </div>
                              <div v-if="col.field == 'flags'" key="flags" class="full-height" @click.stop="() => {}" :ref="el => setCellACtionRef(row['id_queue_ticket'].toString(), el)">
                                <div class="tr-cell-actions"  :class="{ 'qt-row-is-simplified': isTableRowSimplified}">
                                  <div 
                                    class="tr-cell-actions-expandable" 
                                    :class="[
                                      row['flg_appointment'] ? 'bg-accent-200' : `row-is-type-${row['type_status_queue_ticket']}`
                                    ]">
                                    <div class="row full-height">
                                      <div class="tr-cell-action-toggler bg-toggle-color" @click.stop="() => handleCellActionToggle((row['id_queue_ticket'].toString()))">
                                          <q-icon name="arrow_left" class="toggler-icon" size="md" />
                                      </div>
                                      <div class="col full-height">
                                        <div class="row gap-4 items-center tr-cell-actions-container">
                                          <q-btn
                                            round
                                            @click.stop="() => { fillQueueTicketCallData(row) }"
                                            outline
                                            size="md"
                                          >
                                            <q-icon 
                                              :color="
                                                row['queue_detail']['flg_qt_calling']
                                                  ? 'blue-7'
                                                  : 'grey-7'
                                              "
                                              name="campaign"
                                            />
                                          </q-btn>
                                          <q-btn
                                            class="row-btn-hide-on-collapse"
                                            round
                                            @click.stop="handleStatus(row, 'flg_tel_requested')"
                                            outline
                                            size="md"
                                          >
                                            <q-icon 
                                              :color="
                                                row['flg_tel_requested']
                                                  ? 'blue-7'
                                                  : 'grey-7'
                                              "
                                              name="settings_phone" 
                                            />
                                          </q-btn>
                                          <q-btn
                                            class="row-btn-hide-on-collapse"
                                            round
                                            @click.stop="handleStatus(row, 'flg_parking_wait')"
                                            outline
                                            size="md"
                                          >
                                            <q-icon 
                                              :color="
                                                row['flg_parking_wait']
                                                  ? 'blue-7'
                                                  : 'grey-7'
                                              "
                                              name="directions_car" 
                                            />
                                          </q-btn>
                                          <q-btn
                                            class="row-btn-hide-on-collapse"
                                            round
                                            @click.stop="row.customer && (row.customer.email1 !== '' || row.customer.email2 !== '') ? emailSendModalOpen(row) : ''"
                                            size="md"
                                            outline
                                            :disable="row.customer && (row.customer.email1 === '' &&
                                                  row.customer.email2 === '')"
                                          >
                                            <q-icon name="email" />
                                          </q-btn>
                                          <q-btn
                                            class="row-btn-hide-on-collapse"
                                            round
                                            @click.stop=" (event) =>
                                                row['request'] && row['request'].id_request
                                                  ? openRequestDetail(row)
                                                  : openQTRQ(event, row)
                                            "
                                            size="md"
                                            outline
                                            v-if="clinicTypeQtRequestByCustomer"
                                          >
                                            <span 
                                              :class="{
                                                  'text-blue-7': row['request'] && row['request'].id_request,
                                                  'text-grey-7': !(row['request'] && row['request'].id_request)
                                              }
                                            ">
                                              RQ
                                            </span>
                                          </q-btn>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </td>
                          </template>
                        </MtTable2>
                      </q-tab-panel>
                    </q-tab-panels>
                  </div>
                </div>
              </div>
              <div class="doctor-sidebar-wrapper col-auto full-height overflow-hidden" :class="{ 'sidebar-toggler-expanded' : sideBarDoctorExpaned }">
                <div class="sidebar-toggler-wrapper">
                  <div class="sidebar-toggler" @click="handleSideBarDoctorExpaned">
                    <q-icon class="toggler-icon" size="20px" name="chevron_right"  />
                  </div>
                  <div class="sidebar-info col">
                    <div class="sidebar-icon-wrapper">
                      <q-icon class='text-blue-700' size="20px" name="person" />
                    </div>
                    <span class="caption1 regular text-grey-800 q-ml-xs">合計</span>
                    <span class="sidebar-group-count q-mx-sm title1 bold text-grey-900">
                      {{ rows1_2.length }}組
                    </span>
                    <span class="caption1 regular text-grey-800">待ち</span>
                  </div>
                </div>
                <div class="ds-buttons-wrapper column no-wrap" style="height: 100%; overflow-y: auto;">
                  <template
                    v-for="(doctor, index) in _.orderBy(
                      doctors,
                      ['count'],
                      ['desc']
                    )"
                    :key="index"
                  >
                    <q-btn-dropdown
                      class="ds-button q-mb-sm"
                      split
                      :label="`${shortenStringNameFirstChar(doctor?.name, !sideBarDoctorExpaned)}(${doctor?.count})`"
                      size="sm"
                      align="center"
                      unelevated
                      content-class="caption1 bold"
                      @click="() => onFilterDoctorClick(doctor)"
                      :style="{ backgroundColor: doctor.color, color: '#fff' }"
                    >
                      <q-list>
                        <q-item
                          clickable
                          v-close-popup
                          v-for="(qt, i) in doctor.qt_list"
                        >
                          <q-item-section>
                            <q-item-label @click="() => onFilterQTClick(qt)">
                              {{qt}}
                            </q-item-label>
                          </q-item-section>
                        </q-item>
                      </q-list>
                    </q-btn-dropdown>
                  </template>
                  <q-btn
                    v-if="searchDoctor"
                    label="絞込解除"
                    unelevated
                    size="sm"
                    color="primary"
                    @click="clearDoctorFilter"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <q-dialog v-model="flgCallConfirmationModal">
    <q-card class="q-pa-lg">
      <q-card-section class="q-relative">
        <div class="text-h6">
          <q-icon size="25px" name="error_outline" />  待合室画面で呼び出しますか？<br/>ステ－タスを呼出済へ変更します。
        </div>
        <div class="q-mt-md">
          <MtFormCheckBox
            label="待合室画面で呼び出す部屋を表示"
            v-model:checked="queueTicketCallData.flg_qt_room_display"
            @update:checked="(val) => {
              if(!val) queueTicketCallData.id_room = null
            }"
          />
        </div>
        <MtFilterSelect
          v-if="queueTicketCallData.flg_qt_room_display"
          :options="qtDisplayRoomList"
          :options-default="qtDisplayRoomListDefault"
          v-model:selecting="queueTicketCallData.id_room"
          label=""
        />
      </q-card-section>
      <q-card-actions class="flex justify-center">
        <q-btn
          label="キャンセル"
          text-color="primary"
          class="q-mr-md"
          outline
          color="white"
          v-close-popup
        />
        <q-btn
          label="呼出"
          color="primary"
          class=""
          no-caps
          @click="updateFlgQsCalling"
          v-close-popup
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<style lang="scss" scoped>
.q-table {
  th {
    padding: 7px;
  }

  td {
    padding: 7px;
  }
}

.custom-badge {
  width: 20px;
  height: 20px;
}

.medicalExmColor {
  width: 15px;
  height: 15px;
  border-radius: 50%;
  background-color: #ffb444;
}

.status {
  padding: 5px;
}

.ticket-tabs {
  .q-tab {
    min-width: 125px;
    border-right: 1px solid $grey-200;
  }
}

.active-menu {
  border-right: 13px solid $blue-700;
}

.absent-box {
  border: 2px solid $red;
  background-color: #ffeceb;
  padding: 5px 10px;
  border-radius: 5px;
  height: 100%;
  max-height: calc(38px * 3);
  overflow-y: auto;
  .absent-item {
    max-height: 120px;
  }
}
.num-qt-box {
  // border: 2px solid $blue-700;
  // background-color: #e6f7ff;
  // padding: 5px 10px;
  border-radius: 5px;
  font-weight: 900;
  font-size: 28px;
}

.num-process {
  padding: 3px 8px;
  border-radius: 4px;
  font-weight: 900;
  font-size: 14px;
  color: #fff;
}

.active-email-icon {
  margin-left: auto;
  font-size: 20px;
  color: #424242;
}

.active-email-icon:hover {
  transform: Scale(1.2);
}

.disable-email-icon {
  margin-left: auto;
  font-size: 20px;
  color: #b2babb;
}

.employeeName {
  padding-top: 2px;
  padding-bottom: 2px;
}

.no-drag {
  cursor: default !important;
}
.no-drag tr {
  cursor: default !important;
  user-select: none;
  -webkit-user-drag: none;
}
// based on enum statusQueueTicket
:deep(.row-is-type-1),
.row-is-type-1 {
  background-color: #d1ffff !important;
}
:deep(.row-is-type-2),
.row-is-type-2 {
  background-color: #fcffcd !important;
}
:deep(.row-is-type-3),
.row-is-type-3 {
  background-color: #c8fff1 !important;
}
:deep(.row-is-type-4),
.row-is-type-4 {
  background-color: #ffe0fb !important;
}

.queue-info-col-wrapper {
  &:not(.qt-row-is-simplified){
    .qic-row-tick {
      display: none;
    }
  }
}

.customer-info-col-wrapper {
  row-gap: 8px;
  width: 100%;
  .customer-info-col {
    width: 100%;
    .cic-customer {
      border-right: 1.5px solid #e0e0e0;
      box-shadow: 3px 0 8px -6px rgba(250, 249, 249, 0.12);
      width: 18%;
      align-content: flex-start;
      row-gap: 5px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding-right: 10px
    }
    .qic-rq-btn {
      align-items: center;
    }
    .cic-pet-detail-wrapper {
      .cic-pet-detail {
        width: 120px;
      }
    }
    .cic-customer-other {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .cic-customer-pets {
        display: flex;
      }
      .cic-customer-bottom {
        column-gap: 8px;
        .cic-co-memo-wrapper {
          .cic-co-memo { 
            border: 0.5px dashed #b4b3b3;
            border-radius: 5px;
            padding: 5px;
          }
        }
        .flg-indictor-wrapper { 
          .flg-indicator {
            width: 22px;
            height: 22px;
            border-radius: 50%;
            border: 1px solid $blue-7;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }
  }
  &:not(.qt-row-is-simplified) {
    .customer-info-col {
      .cic-customer {
        .qic-rq-btn {
          display: flex;
          flex-direction: row;
          justify-content: start;
          .q-icon {
            margin-top: 1px;
          }
        }
      }
      .cic-pet-detail-wrapper {
        .cic-pet-detail {
        }
      }
      .cic-customer-other {
        row-gap: 16px;
        .cic-customer-pets {
          flex-direction: column;
          row-gap: 16px;
          flex-wrap: nowrap;
        }
        .cic-customer-bottom {
          .cic-co-memo-wrapper{
            .cic-co-memo { 
            }
          }
          .flg-indictor-wrapper { 
          }
        }
      }
    }
  }
  &.qt-row-is-simplified {
    .customer-info-col {
      .cic-customer {
        .cic-customer-code {
          display: none;
        }
        .cic-customer-badges-wrapper {
          display: none;
        }
        .qic-rq-btn {
          display: none;
        }
      }
      .cic-customer-other {
        row-gap: 8px;
        .cic-customer-pets {
          flex-direction: row;
          column-gap: 16px;
          flex-wrap: wrap;
          .cic-pet-detail-wrapper {
            .cic-pet-detail {    
            }
          }
          .cic-pet-purpose-doctor {
            display: none;
          }
        }
        .cic-customer-bottom {
          .cic-co-memo-wrapper{
            display: none;
            .cic-co-memo { 
            }
          }
          .flg-indictor-wrapper { 
            display: none;
          }
        }
      }
    }
  }
}
.tr-cell-actions {
  height: 100%;
  min-width: 100px;
  overflow: visible;
  position:relative;
  .tr-cell-actions-expandable {
    position: absolute;
    height: calc(100% - 4px);
    right: 0px;
    top: 2px;
    max-width: 117px;
    transition: max-width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: right;
    .tr-cell-action-toggler {
      display: flex;
      align-items: center;
      justify-content: center;
      color: #555;
      width: 35px;
      // background-color: #f38c8c;
      height: 100%;
      cursor: pointer;
      .toggler-icon {
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }
    }
    .tr-cell-actions-container {
      align-items: center;
      padding: 20px;
      height: 100%;
      flex-wrap: nowrap;
    }
  }
  &.qt-row-is-simplified {
    min-height: 50px;
  }
}
.row-btn-hide-on-collapse {
  display: none;
}
.action-expanded {
  display: flex;
  .tr-cell-actions-expandable{
    max-width: 1000px;
    right: 20px;
  }
  .row-btn-hide-on-collapse {
    display: flex !important;
    flex-direction: row;
    align-items: center;
    justify-content: center;
  }
  .tr-cell-action-toggler {
    .toggler-icon {
      transform: rotate(180deg);
    }
  }
}
.bg-toggle-color {
  background-color: rgba(0, 0, 0, 0.1);
}
:deep(.q-table) {
  tbody {
    td:last-child {
      padding: 0px !important;
    }
  }
}
.qt-time-label {
  font-size: 12px;
  padding: 1px 6px;
  margin: 3px 6px 3px 0px !important;
  border-radius: 4px;
  background-color: #dbe2eb;
  color: #1d2731 ;
}
.qt-expected-time-label {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  background-color: #ffffff;
  color: #005c68 ;
  display: none;
}
.doctor-sidebar-wrapper {
    display:flex;
    flex-direction: column;
    height: 100%;
  .sidebar-toggler-wrapper {
    .sidebar-info {
      justify-content: end;
    }
  }
}
.purpose-sidebar-wrapper {
  display:flex;
  flex-direction: column;
  height: 100%;
  .sidebar-toggler-wrapper {
    .sidebar-info {
      justify-content: start;
    }
  }
  .sp-list-item-wrapper {
    padding: 8px 0px 8px 8px !important;
    min-height: 43px !important;
    .sp-list-item {
      padding-right: 17px;
    }
    &.active-menu {
      .sp-list-item{
        padding-right: 4px;
      }
    }
  }
}
.doctor-sidebar-wrapper,
.purpose-sidebar-wrapper {
  .sidebar-toggler-wrapper {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    margin-bottom: 8px;
    height: 39px;
    .sidebar-toggler {
      width: 35px;
      height: 35px;
      background-color: $grey-500;
      color: #ffffff;
      cursor: pointer;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      .toggler-icon {
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }
    }
    .sidebar-info {
      height: 100%;
      display: flex;
      flex-direction: row;
      align-items: end;
      padding: 11px 5px;
      .sidebar-icon-wrapper {
        margin-bottom:-3px;
      }
      .sidebar-group-count {
        margin-bottom: -1px;
      }
    }
  }
  &:not(.sidebar-toggler-expanded) {
    .sidebar-toggler-wrapper {
      .sidebar-toggler {
        .toggler-icon {
          transform: rotate(180deg);
        }
      }
      .sidebar-info {
        justify-content: center !important;
        > *:not(.sidebar-icon-wrapper) {
          display: none;
        }
        .sidebar-icon-wrapper {
        }
        .sidebar-group-count {
          
        }
      }
    }
  }
}
@media screen and (max-width: 1290px) {
  .customer-info-col-wrapper {
    .customer-info-col {
      .cic-customer {
        border-right: none;
        border-bottom: 1.5px solid #e0e0e0;
        box-shadow: 3px 0 8px -6px rgba(250, 249, 249, 0.12);
        padding-bottom: 8px;
        margin-bottom: 8px;
        width: 100%;
        display: flex;
        column-gap: 18px;
        align-items: center;
        justify-content: start;
        .cic-customer-label-wrapper {
          flex-direction: row;
          align-items: end;
          .qic-rq-btn {
            margin-left: auto;
          }
        }
        .cic-customer-badges-wrapper {
          width: 100%;
          justify-content: end;
        }
      }
      .cic-pet-detail-wrapper {
        .cic-pet-detail {
        }
      }
      .cic-customer-other {
        .cic-customer-pets {
        }
        .cic-customer-bottom {
          .cic-co-memo-wrapper {
            .cic-co-memo {
            }
          }
          .flg-indictor-wrapper { 
            .flg-indicator {
            }
          }
        }
      }
    }
    &:not(.qt-row-is-simplified) {
      .customer-info-col {
        .cic-customer {
          .qic-rq-btn {
            .q-icon {
            }
          }
        }
        .cic-customer-other {
          .cic-customer-pets {
            .cic-pet-detail-wrapper {
              .cic-pet-detail {
              }
            }
          }
          .cic-customer-bottom {
            .cic-co-memo-wrapper{
              .cic-co-memo { 
              }
            }
            .flg-indictor-wrapper { 
            }
          }
        }
      }
    }
    &.qt-row-is-simplified {
      .customer-info-col {
        flex-direction: column;
        .cic-customer {
          .cic-customer-code {
          }
          .cic-customer-badges-wrapper {
          }
          .qic-rq-btn {
          }
        }
        .cic-customer-other {
          .cic-customer-pets {
            .cic-pet-detail-wrapper {
              .cic-pet-detail {
              }
            }
            .cic-pet-purpose-doctor {
            }
          }
          .cic-customer-bottom {
            .cic-co-memo-wrapper{
              .cic-co-memo { 
              }
            }
            .flg-indictor-wrapper { 
            }
          }
        }
      }
    }
  }
}
@media screen and (max-width: 1124px) {
  .customer-info-col-wrapper {
    .customer-info-col {
      .cic-customer {
        .cic-customer-label-wrapper {
          .qic-rq-btn {
            justify-content: start;
          }
        }
        .cic-customer-badges-wrapper {
        }
      }
      .cic-customer-other {
        .cic-customer-pets {
          .cic-pet-detail-wrapper {
            flex-direction: column;
            .cic-pet-detail {
              width: 100%;
            }
          }
          .cic-pet-purpose-doctor {
            margin-top: 8px;
            padding-left: 1px;
          }
        }
        .cic-customer-bottom {
          .cic-co-memo-wrapper {
            .cic-co-memo {
            }
          }
          .flg-indictor-wrapper { 
            .flg-indicator {
            }
          }
        }
      }
    }
    &:not(.qt-row-is-simplified) {
      .customer-info-col {
        .cic-customer {
          .qic-rq-btn {
            .q-icon {
            }
          }
        }
        .cic-customer-other {
          .cic-customer-pets {
            .cic-pet-detail-wrapper {
              .cic-pet-detail {
              }
            }
          }
          .cic-customer-bottom {
            .cic-co-memo-wrapper{
              .cic-co-memo { 
              }
            }
            .flg-indictor-wrapper { 
            }
          }
        }
      }
    }
    &.qt-row-is-simplified {
      .customer-info-col {
        .cic-customer {
          .cic-customer-code {
          }
          .cic-customer-badges-wrapper {
          }
        }
        .cic-customer-other {
          .cic-customer-pets {
            .cic-pet-purpose-doctor {
            }
            .cic-pet-detail-wrapper {
              .cic-pet-detail {
              }
            }
          }
          .cic-customer-bottom {
            .cic-co-memo-wrapper{
              .cic-co-memo { 
              }
            }
            .flg-indictor-wrapper { 
            }
          }
        }
      }
    }
  }
}
@media screen and (max-width: 1056px) {
  .customer-info-col-wrapper {
    .customer-info-col {
      .cic-customer {
        .cic-customer-label-wrapper {
          .qic-rq-btn {
            margin-left: unset;
          }
        }
      }
    }
  }
}
</style>
