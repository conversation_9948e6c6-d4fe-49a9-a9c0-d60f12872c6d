<script setup lang="ts">
import MtModalHeader from '@/components/MtModalHeader.vue'
import SelectPetModal from '@/pages/queueTicket/checkIn/SelectPetModal.vue'
import ReceptionConfirmation from '@/pages/queueTicket/checkIn/ReceptionConfirmation.vue'
import AddPetModal from '@/pages/queueTicket/checkIn/AddPetModal.vue'
import PetRegistrationQR from '@/pages/queueTicket/checkIn/newCustomer/PetRegistrationQR.vue'
import PetPawImage from '@/assets/img/checkin/paw.png'

import { computed, onMounted, onUnmounted, ref } from 'vue'
import { nanoid } from 'nanoid'
import { storeToRefs } from 'pinia'
import { CliCommon, CustomerType, PetType, QueueTicketType } from '@/types/types'
import { aahUtilsGetEmployeeName, getDateTimeNow } from '@/utils/aahUtils'
import { openLargeModal } from './checkInUtils'
import mtUtils from '@/utils/mtUtils'
import aahMessages from '@/utils/aahMessages'
import { event_bus } from '@/utils/eventBus'
import selectOptions from '@/utils/selectOptions'

import useCliCommonStore from '@/stores/cli-common'
import useEmployeeStore from '@/stores/employees'
import useCustomerStore from '@/stores/customers'
import usePetStore from '@/stores/pets'
import useQueueTicketStore, { ToBeCreatedTicketType, typeCheckInCustomer } from '@/stores/queue_ticket'
import useClinicStore from '@/stores/clinics'

const cliCommonStore = useCliCommonStore()
const employeeStore = useEmployeeStore()
const customerStore = useCustomerStore()
const queueTicketStore = useQueueTicketStore()
const petsStore = usePetStore()
const clinicStore = useClinicStore()
const { getCliCommonQTVisitPurposeList } = storeToRefs(cliCommonStore)
const { getTicketsToBeCreated } = storeToRefs(queueTicketStore)
const { getClinic }  = storeToRefs(clinicStore)

const emits = defineEmits(['close'])
const closeModal = () => emits('close')

interface Props {
  customerInfo: CustomerType,
  todayQtickets: QueueTicketType[],
  flgNewCustomer: boolean,
  skipQrNewCustomerFlow: boolean
}
const props = withDefaults(defineProps<Props>(), {
  customerInfo: {} as CustomerType,
  todayQtickets: [] as QueueTicketType,
  flgNewCustomer: false,
  skipQrNewCustomerFlow: false
})

const { flgNewCustomer } = props

const petMap = ref<Map<string, PetType>>(new Map())
const queueTicketList = ref(props.todayQtickets)
const isSubmitting = ref<boolean>(false)

const popupFunction = openLargeModal() ? mtUtils.popup : mtUtils.mediumPopup

const sortedQueueTickets = computed(() => {
  return [...filterQueueTickets(queueTicketList.value)].sort((a, b) => {
    const aNum = parseInt(a.number_queue_ticket)
    const bNum = parseInt(b.number_queue_ticket)

    const aHasQueueTicketNum = !isNaN(aNum)
    const bHasQueueTicketNum = !isNaN(bNum)

    if(!aHasQueueTicketNum && !bHasQueueTicketNum) return 0
    if(!aHasQueueTicketNum) return 1
    if(!bHasQueueTicketNum) return -1

    return bNum - aNum
  })
})

const getPetNames = (queueTicket: QueueTicketType | ToBeCreatedTicketType) => {
  if(queueTicket.newPet) return queueTicket.pet_name_ui.map((v) => v.name_pet).join(' ･ ')
  const petNamesWithHonorific:string[] = []  
  queueTicket.petList.forEach((v) => {
    const pet = petMap.value.get(v.id_pet)
    if(pet) {
      const petName = pet.name_pet
      const petHonorific = customerStore.getPetHonorific(pet)
      petNamesWithHonorific.push(`<span class="text-regular weighted">${petName} <span class="text-medium weighted">${petHonorific}</span></span>`)
    }
  })
  return petNamesWithHonorific.join(' ･ ')
}

const getTicketPurposeEmployeeList = (queueTicket: QueueTicketType) => {
  const typePurposeVisitList = []
  const employeeList = []
  Object.keys(queueTicket.queue_detail || {}).forEach((pet) => {
    if(!queueTicket.id_pet.includes(Number(pet))) return 
    if(queueTicket.queue_detail?.[pet]?.type_purpose_list && queueTicket.queue_detail?.[pet]?.type_purpose_list.length > 0) {
      queueTicket.queue_detail[pet].type_purpose_list.forEach((typePurpose) => {
        typePurposeVisitList.push(getCliCommonQTVisitPurposeList.value.find((v: CliCommon) => v.id_cli_common === typePurpose)?.label)
      })
    }
    if(queueTicket.queue_detail?.[pet]?.type_doctor_list && queueTicket.queue_detail?.[pet]?.type_doctor_list.length > 0) {
      queueTicket.queue_detail[pet].type_doctor_list.forEach((employee) => {
        employeeList.push(employee === -1 ? '指名なし（最短）' : aahUtilsGetEmployeeName(employeeStore.getAllEmployees, employee))
      })
    }
    else employeeList.push('担当なし')
  })
  return typePurposeVisitList.join(' ･ ') + `${typePurposeVisitList.length > 0 && employeeList.length > 0 ? ' / ' : ''}` + employeeList.join(' ･ ')
}

const ticketIsEditable = (queueTicket: QueueTicketType) => {
  return ![2, 3, 90, 99].includes(queueTicket.type_status_queue_ticket) && !!!queueTicket.flg_delete
}

const filterQueueTickets = (queueTickets: QueueTicketType[]) => {
  return queueTickets.filter((v: QueueTicketType) => ![3, 90, 99].includes(v.type_status_queue_ticket) && !!!v.flg_delete)
}

let refreshIntervalId: NodeJS.Timer | null = null

const startRefreshInterval = () => {
  if (!refreshIntervalId) {
    refreshIntervalId = setInterval(async () => {
      await refreshQueueTicketList()
    }, 3000)
  }
}

const stopRefreshInterval = () => {
  if (refreshIntervalId) {
    clearInterval(refreshIntervalId)
    refreshIntervalId = null
  }
}

const openSelectPetModal = async () => {
  stopRefreshInterval()
  await mtUtils.popup(SelectPetModal, {
    customerInfo: props.customerInfo,
    flgNewCustomer: props.flgNewCustomer,
    queueTicket: props.queueTicket,
    popup: {
      persistent: true
    },
  }, true)
  startRefreshInterval()
}

const openAddPetModal = async () => {
  await mtUtils.popup(AddPetModal, {
    popup: {
      persistent: true
    },
  }, true)
}

const submitQueueTicketList = async () => {
  isSubmitting.value = true
  let promises = []
  let newPetPromises = []
  filterQueueTickets(queueTicketList.value).forEach((ticket: QueueTicketType | ToBeCreatedTicketType, idx: number) => {
    let payload = {
      ...ticket
    }

    if(typeof payload.id_queue_ticket == 'string') delete payload.id_queue_ticket  // for id_queue_ticket generating for UI(create case)

    if(!payload.id_queue_ticket) {
      payload.datetime_issued = getDateTimeNow()
      payload.datetime_estimate = getDateTimeNow()
      payload.code_customer = props.customerInfo.code_customer,
      payload.id_customer = props.customerInfo.id_customer
      payload.flg_appointment = false
      payload.flg_apply_insurance = false
    }
    payload.type_status_queue_ticket = 2

    payload.datetime_check_in = getDateTimeNow()
      
    if(payload.id_queue_ticket) promises.push(() => queueTicketStore.updateQueueTicketList(payload.id_queue_ticket, payload))
    else {
      payload.petList.forEach((pet) => { // Creating new pets
        if(!pet.code_pet) {
          payload.is_new_pet = true
          let additionalNewPetPayload = { is_new_pet: true, id_customer: props.customerInfo.id_customer }
          let petPayload = { ...pet, ...additionalNewPetPayload }
          delete petPayload.id_pet
          newPetPromises.push(() => petsStore.submitPet(props.customerInfo.id_customer, petPayload))
        }
      })
      promises.push(() => queueTicketStore.submitQueueTicketList(payload))
    }
  })

  let petResponse = null
  for(const petPromiseFn of newPetPromises) {
    const response = await petPromiseFn()
    if(!petResponse) petResponse = response
  }
  await Promise.all(promises.map((promiseFn) => promiseFn())).then(async() => {
    await queueTicketStore.fetchQueueTicketList({
      code_customer: props.customerInfo.code_customer,
      today: true
    })
    queueTicketList.value = filterQueueTickets(queueTicketStore.queueTickets)
    mtUtils.autoCloseAlert(aahMessages.success)
    if(newPetPromises.length > 0 && getClinic.value.type_checkin_new_customer == typeCheckInCustomer.ALLOW_NEW_PET_WITH_QR) {
      const barCode = petResponse.data.data.barcode
      popupFunction(PetRegistrationQR, {
        barCode,
        numberQueueTicket: filterQueueTickets(queueTicketStore.queueTickets).map((ticket: QueueTicketType) => ticket.number_queue_ticket),
        skipQr: false,
        popup: {
          persistent: true
        }
      }, true)
    } else {
      await popupFunction(ReceptionConfirmation, {
        confirmedQueueTickets: filterQueueTickets(queueTicketStore.queueTickets).map((ticket: QueueTicketType) => ticket.number_queue_ticket),
        popup: {
          persistent: true
        }
      }, true)
    }
  })
  isSubmitting.value = false
}

const editQueueTicket = async (queueTicket: QueueTicketType | ToBeCreatedTicketType) => {
  stopRefreshInterval()
  await refreshQueueTicketList()
  const foundQueueTicket = queueTicketList.value.find((v: QueueTicketType) => v.id_queue_ticket === queueTicket.id_queue_ticket)
  if (!foundQueueTicket) {
    mtUtils.autoCloseAlert("受付番号が見つかりません", "エラー")
    return
  }
  await popupFunction(SelectPetModal, {
    customerInfo: props.customerInfo,
    queueTicket
  }, true)
  startRefreshInterval()
}

const handleConfirmTicket = async (addAnotherTicket?: boolean) => {
  const addedTicket = getTicketsToBeCreated.value[getTicketsToBeCreated.value.length - 1]
  if(addedTicket.id_queue_ticket) {
    const idQueueTicket = addedTicket.id_queue_ticket
    const ticket = queueTicketList.value.find((v: QueueTicketType) => v.id_queue_ticket === idQueueTicket)
    ticket.queue_detail = addedTicket.queue_detail
    ticket.petList = addedTicket.petList
    ticket.id_pet = addedTicket.id_pet
  }
  else {
    queueTicketList.value.push({...addedTicket, id_queue_ticket: nanoid() })
  }
  
  if (!addAnotherTicket) { 
    flgNewCustomer ? await submitNewCustomer() : await submitQueueTicketList()
  } else {
    openAddPetModal()
  }
}

const submitNewCustomer = async () => {
  let payload = {
    customer: {},
    pet_list: {},
    queue_detail: {},
    type_process_time: 5
  }
  queueTicketList.value.forEach((v) => {
    payload.queue_detail = v.queue_detail
    payload.pet_list = v.pet_name_ui,
    payload.type_status_queue_ticket = 2
  })
  const response = await mtUtils.callApi(selectOptions.reqMethod.POST, '/mst/generate-temp-customer', payload)
  if(response && response.barcode) {
    popupFunction(PetRegistrationQR, {
      barCode: response.barcode,
      numberQueueTicket: response.queue_ticket.number_queue_ticket,
      skipQr: props.skipQrNewCustomerFlow,
      popup: {
        persistent: true
      }
    }, true)
  }
}

const handleConfirmTicketEvent = async (resolve: () => void, addAnotherTicket?: boolean) => {
  if(resolve) {
    await handleConfirmTicket(addAnotherTicket)
    resolve()
  }
}

const refreshQueueTicketList = async () => {
  await queueTicketStore.fetchQueueTicketList({
    code_customer: props.customerInfo.code_customer,
    today: true
  })
  queueTicketList.value = filterQueueTickets(queueTicketStore.queueTickets)
}

onMounted(() => {
  const { customerInfo, todayQtickets, flgNewCustomer } = props
  if(!flgNewCustomer) {
    customerInfo.pets.forEach((pet: PetType) => {
      petMap.value.set(pet.id_pet, pet)
    })
    if(filterQueueTickets(todayQtickets).length === 0) {
      openSelectPetModal()
    }
    startRefreshInterval()
  }

  if(flgNewCustomer) {
    queueTicketStore.clearToBeCreatedTickets()
    openAddPetModal()
  }

  event_bus.on('confrimTicket', handleConfirmTicketEvent)
  event_bus.on('finalConfirmationDone', closeModal)
})

onUnmounted(() => {
  stopRefreshInterval()
  event_bus.off('confrimTicket', handleConfirmTicketEvent)
  event_bus.off('finalConfirmationDone', closeModal)
})

</script>
<template>
  <div class="checkin-feat content flex col">
    <div class="checkin-feat-wrapper">
      <MtModalHeader class="q-py-sm" :closeBtn="false">
        <q-toolbar-title
          class="checkin-feat row no-wrap items-center"
          style="height: 95px; align-items: end; padding-bottom: 15px; padding-left: 16px;"
        >
          <template v-if="!flgNewCustomer">
            <span class="text-regular weighted">{{ customerInfo.name_family }} {{ customerInfo.name_first}} </span>
            <span class="text-medium weighted q-ml-sm relative-position" style="top: 0px; left: 10px;">{{ customerStore.getCustomerHonorific(customerInfo) }}</span>
          </template>
          <template v-else>
            <span class="text-regular weighted">新規オーナー</span>
            <span class="text-medium weighted q-ml-sm relative-position" style="top: 0px; left: 10px;">さま</span>
          </template>
        </q-toolbar-title>
        <div style="padding-top: 15px; padding-right: 16px;">
          <span class="text-medium normal text-blue cursor-pointer" v-if="!flgNewCustomer || queueTicketList.length === 0" @click="() => { flgNewCustomer ? openAddPetModal() : openSelectPetModal() }">
            新しい受付
          </span>
        </div>
      </MtModalHeader>
      <q-card-section class="qt-wrapper q-pa-md">
        <div class="info-content" style="overflow-y: auto;">
          <div>
           <span class="text-medium normal text-grey-800">受付内容を確認して、『院内受付をする』ボタンを選択してください。</span>
          </div>
          <div class="flex justify-sart items-center gap-4 q-py-sm">
            <div class="existing-tickets q-mt-md">
              <template v-for="(queueTicket, idx) in sortedQueueTickets" :key="idx">
                <div class="queue-box col-4" :class="{ 'cursor-pointer': ticketIsEditable(queueTicket) }" @click="ticketIsEditable(queueTicket) ? editQueueTicket(queueTicket) : null">
                  <div style="flex-grow: 1;">
                    <div class="row flex nowrap items-center">
                      <img :src="PetPawImage" />
                      <span class="q-pa-sm pet-names" v-html="getPetNames(queueTicket)" />
                    </div>
                    <div>
                      <span class="text-medium weighted text-blue">
                        {{getTicketPurposeEmployeeList(queueTicket)}}
                      </span>
                    </div>
                    <div>
                      <span class="text-small">受付番号</span>
                      <span class="text-medium weighted"> {{queueTicket.number_queue_ticket}} </span>
                    </div>
                  </div>
                  <div class="flex justify-center q-mt-md" v-if="ticketIsEditable(queueTicket)">
                    <q-btn unelevated rounded class="bg-white q-pa-lg">
                      <span class="text-small">編集</span>
                    </q-btn>
                  </div>
                </div>
              </template>
            </div>
          </div>
        </div>
      </q-card-section>
      <q-card-section class="bg-white q-bt action-btns row">
        <div class="flex justify-between row full-width">
          <div class="row" style="width: 40%;">
            <q-btn 
            outline 
            class="full-width cancel outline-btn" 
            @click="closeModal"
            >
            <span>やり直す</span>
            </q-btn>
          </div>
          <div class="row" style="width: 60%; padding-left: 20px;">
            <q-btn 
              :disable="sortedQueueTickets.length === 0"
              class="next text-white full-width"
              :class="getTicketsToBeCreated.length ? 'confirm-queue' : 'outline'"
              @click="() => { flgNewCustomer ? submitNewCustomer() : submitQueueTicketList() }"
              :loading="isSubmitting"
            >
              <span>院内受付をする</span>
            </q-btn>
          </div>
        </div>
      </q-card-section>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.add-new {
  background: $pale-blue;  
  div {
    padding: 5px 20px;
    border-radius: 20px;
  }
}
.existing-tickets {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  .queue-box {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    background: #FFF4CB;
    padding-left:40px;
    padding-right:40px;
    padding-top: 25px;
    padding-bottom: 25px;
    .text-blue {
      color: #033C71 !important;
    }
  }
  .pet-names {
    border-radius: 4px;
    font-size: 24px;
    :deep(small) {
      font-size: 16px;
    }
  }
}
.action-btn {
  border-radius: 5px;
  height: 60px;
  font-size: 20px;
  &.outline {
    &:before {
      border-color: $dark-blue;
    }  
    :deep(.q-btn__content) {
      color: $dark-blue;
    }
  }
  &.confirm-queue {
    background: $dark-blue;
    color: #fff;
  }
}
</style>