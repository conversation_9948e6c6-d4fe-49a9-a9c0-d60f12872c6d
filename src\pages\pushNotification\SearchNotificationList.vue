<script setup lang="ts">
import MtHeader from '@/components/layouts/MtHeader.vue';
import MtTable2 from '@/components/MtTable2.vue';
import { onMounted, ref } from 'vue';
import { usePushNotificationStore } from '@/stores/push-notifications';
import MtCustomerInfoLabel from '@/components/customers/MtCustomerInfoLabel.vue';
import { dateFormat, getDateToday, getDaysBefore } from '@/utils/aahUtils';
import useCustomerStore from '@/stores/customers';
import mtUtils from '@/utils/mtUtils';
import { typePushNotification, typePushTitle } from '@/utils/enum';
import MtFormInputDate from '@/components/form/MtFormInputDate.vue';
import MtSearchCustomer from '@/components/MtSearchCustomer.vue';
import MtFilterSelect from '@/components/MtFilterSelect.vue';
import MtFormPullDown from '@/components/form/MtFormPullDown.vue';

const pushNotificationStore = usePushNotificationStore()
const customerStore = useCustomerStore()

const columns = ref([
  { name: 'id_customer', label: '顧客ID', field: 'id_customer' },
  { name: 'datetime_push', label: '送信日時', field: 'datetime_push' },
  { name: 'type_push_notification', label: '通知種別', field: 'type_push_notification' },
  { name: 'type_push_title', label: '通知タイトル', field: 'type_push_title' },
  { name: 'url_noti', label: 'クリック後URL', field: 'url_noti' },
  { name: 'flg_checked', label: '通知確認', field: 'flg_checked' },
])
const searchData = ref({
  date_from: getDaysBefore(0),
  date_to: getDateToday(),
  type_push_notification: '',
  id_customer: '',
})

const getPageCount = ref(1)
const pagination = ref({ currentPage: 1, pageSize: 10, })
const updateCustomerList = () => {
  console.log('updateCustomerList')
}
const scrollToTop = () => {
  console.log('scrollToTop')
}
const onRowClick = async (row: any) => {
  const flg_checked = row.flg_checked == 0 ? 1 : 0
  await pushNotificationStore.updatePushNotification(row.id_push_notification, { flg_checked })
  pushNotificationStore.fetchPushNotificationList()
  mtUtils.autoCloseAlert("通知確認を更新しました")
}

const getCustomerInfoLabelProps = (row: any) => {
  return {
    code: row?.code_customer,
    fullKanaName: `${row?.name_kana_family} ${row?.name_kana_first}`,
    fullName: `${row?.name_family} ${row?.name_first}`,
    nameCustomerEx: `${row?.name_customer_ex}`,
    colorType: row?.type_customer_color,
  }
}

const moveNext = () => {
  const inputs = Array.from(
    e.target.form.querySelectorAll('input[type="text"]')
  )
  const index = inputs.indexOf(e.target)
  if (index === 0 || index === 1) {
    inputs[index + 1].focus()
  } else {
    inputs[2].blur()
    search()
  }
}
const search = async () => {
  const payload = { ...searchData.value }
  for (const key in payload) {
    if (payload[key] === '') {
      delete payload[key]
    }
  }
  await pushNotificationStore.fetchPushNotificationList(payload)
}

onMounted(async () => {
  await pushNotificationStore.fetchPushNotificationList(searchData.value)
})
</script>

<template>
  <q-page :style="{ 'min-height': 'unset !important' }">
    <MtHeader>
      <q-toolbar class="text-primary q-pa-none">
          <q-toolbar-title class="title2 bold text-grey-900">
            プッシュ通知一覧
          </q-toolbar-title>
        <div class="row mobile-hide flex justify-end">
          <MtFormInputDate
            v-model:date="searchData.date_from"
            outlined
            class="col-2"
            label="入金日：Start"
            type="date"
            @keydown.enter="moveNext"
            tabindex="1"
          />
          <MtFormInputDate
            v-model:date="searchData.date_to"
            outlined
            label="入金日：End"
            type="date"
            class="q-mx-sm col-2"
            @keydown.enter="moveNext"
            tabindex="2"
          />
          <MtFormPullDown
            v-model:selected="searchData.type_push_notification"
            :options="typePushNotification"
            class="q-mx-sm selection-field"
            label="通知種別"
            outlined
          />
          <MtSearchCustomer
            :applyDefaultClass="false"
            :preSelectedId="searchData.id_customer"
            custom-option
            label="オーナー "
            outlined
            style="width: 200px !important"
            @update:selecting="(val) => {
              searchData.id_customer = val;
            }"
          />
          <q-btn
            @click="search"
            unelevated
            color="primary"
            text-color="white"
            tabindex="3"
            class="q-ml-md"
          >
            <q-icon size="20px" name="search" />検索</q-btn
          >
        </div>
      </q-toolbar>
    </MtHeader>
    <div class="q-mt-sm" style="margin-bottom: 64px;">
      <MtTable2
        :columns="columns"
        :rows="pushNotificationStore.getPushNotifications"
        :rowsBg="true"
        class="custody-table"
        :style="{ boxShadow: 'none' }"
      >
        <template v-slot:row="{ row }">
          <td v-for="(col, index) in columns" :key="index" @click="onRowClick(row)" class="cursor-pointer" :class="{ 'bg-yellow-1': row.flg_checked == 0 }">
            <div v-if="col.field == 'id_customer'" class="body1 regular text-grey-900">
              <MtCustomerInfoLabel :customer="getCustomerInfoLabelProps(row.customer)" :data="row.customer" show-customer-code is-clickable />
            </div>
            <div v-if="col.field == 'datetime_push'" class="body1 regular text-grey-900">
              {{ dateFormat(row.datetime_push, 'YYYY/MM/DD HH:mm') }}
            </div>
            <div v-if="col.field == 'type_push_notification'" class="body1 regular text-grey-900">
              {{ typePushNotification.find(item => item.value == row.type_push_notification)?.label }}
            </div>
            <div v-if="col.field == 'type_push_title'" class="body1 regular text-grey-900">
              {{ typePushTitle.find(item => item.value == row.type_push_title)?.label }}
            </div>
            <div v-if="col.field == 'url_noti'" class="body1 regular text-grey-900">
              {{ row.url_noti }}
            </div>
            <div v-if="col.field == 'flg_checked'" class="body1 regular text-grey-900">
              <div :class="row.flg_checked == 0 ? 'text-red-5' : 'text-green'" v-html="row.flg_checked == 0 ? '未確認' : '確認済'"></div>
            </div>
          </td>
        </template>
      </MtTable2>
      <div
        v-if="getPageCount && getPageCount !== 1"
        class="row q-pa-md fixed-bottom relative-postion"
        style="
          background: rgba(255, 255, 255, 0.2);
          box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
          backdrop-filter: blur(5px);
          -webkit-backdrop-filter: blur(5px);
        "
      >
        <q-pagination
          v-model="pagination.currentPage"
          :max="getPageCount"
          :max-pages="5"
          boundary-numbers
          class="col justify-center"
          direction-links
          @update:model-value="updateCustomerList"
        />
        <q-btn flat icon="vertical_align_top" @click.stop="scrollToTop()">
          TOP
        </q-btn>
      </div>
    </div>
  </q-page>
</template>